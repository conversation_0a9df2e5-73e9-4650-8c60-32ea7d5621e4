<?php
// تفعيل عرض جميع أنواع الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include 'db_connection.php';
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

$store_id = isset($_GET['store_id']) ? decrypt($_GET['store_id'], $key) : null;
$search_name = isset($_GET['search_name']) ? $_GET['search_name'] : '';
$search_phone = isset($_GET['search_phone']) ? $_GET['search_phone'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$search_barcode = isset($_GET['search_barcode']) ? $_GET['search_barcode'] : '';
$display_mode = isset($_GET['display_mode']) ? $_GET['display_mode'] : 'by_date';
$data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales'; // نوع البيانات (مبيعات أو مرتجعات)

// تحديد الجدول المطلوب بناءً على نوع البيانات
$table_name = ($data_type === 'returns') ? 'returns' : 'sales';

if ($display_mode === 'by_account') {
    $sql = "SELECT s.account_id, a.username, a.phone, 
                   s.account_buyer_id, ";
    
    if ($table_name === 'returns') {
        // في حالة المرتجعات، نستخدم 'confirmed' كقيمة ثابتة للحالة
        $sql .= "'confirmed' AS status, ";
    } else {
        // في حالة المبيعات، نستخدم الحقل الفعلي
        $sql .= "CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
                END AS status, ";
    }
    
    $sql .= "COUNT(s.item_id) AS item_count, 
                   SUM(s.quantity) AS total_quantity, ";
    
    if ($table_name === 'returns') {
        $sql .= "SUM(s.refund_amount) AS total_amount, ";
        $sql .= "COALESCE(SUM(s.refunded), 0) AS collected";
    } else {
        $sql .= "SUM(s.price * s.quantity) AS total_amount, ";
        $sql .= "SUM((s.price - s.cost) * s.quantity) AS total_profit, ";
        $sql .= "COALESCE(SUM(s.collected), 0) AS collected";
    }
    
    $sql .= " FROM $table_name s
            JOIN accounts a ON s.account_id = a.account_id
            WHERE 1=1 ";

    // شروط التصفية يجب إضافتها ضمن WHERE
    $params = [];
    if ($store_id) {
        $sql .= " AND s.store_id = ?";
        $params[] = $store_id;
    }
    if ($search_name) {
        $sql .= " AND a.username LIKE ?";
        $params[] = '%' . $search_name . '%';
    }
    if ($search_phone) {
        $sql .= " AND a.phone LIKE ?";
        $params[] = '%' . $search_phone . '%';
    }

    // بعد جمع شروط WHERE يتم إضافة GROUP BY 
    $sql .= " GROUP BY s.account_id, a.username, a.phone ";
    
    // إضافة HAVING فقط إذا كنا نستعلم من جدول المبيعات
    if ($table_name !== 'returns' && $status) {
        $sql .= " HAVING status = ? COLLATE utf8mb4_unicode_ci ";
        $params[] = $status;
    }
}
else {
    $sql = "SELECT DATE(s.time) AS order_date, s.account_id, a.username, a.phone, 
               s.account_buyer_id,
               MAX(s.time) AS max_time, ";
               
    if ($table_name === 'returns') {
        // في حالة المرتجعات، نستخدم 'confirmed' كقيمة ثابتة للحالة
        $sql .= "'confirmed' AS status, ";
    } else {
        // في حالة المبيعات، نستخدم الحقل الفعلي
        $sql .= "CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
                END AS status, ";
    }
    
    $sql .= "COUNT(s.item_id) AS item_count, 
               SUM(s.quantity) AS total_quantity, ";
    
    if ($table_name === 'returns') {
        $sql .= "SUM(s.refund_amount) AS total_amount, ";
        $sql .= "COALESCE(SUM(s.refunded), 0) AS collected";
    } else {
        $sql .= "SUM(s.price * s.quantity) AS total_amount, ";
        $sql .= "SUM((s.price - s.cost) * s.quantity) AS total_profit, ";
        $sql .= "COALESCE(SUM(s.collected), 0) AS collected";
    }
    
    $sql .= " FROM $table_name s
        JOIN accounts a ON s.account_id = a.account_id
        WHERE 1=1";
    
    $params = [];
    if ($store_id) {
        $sql .= " AND s.store_id = ?";
        $params[] = $store_id;
    }
    if ($search_name) {
        $sql .= " AND a.username LIKE ?";
        $params[] = '%' . $search_name . '%';
    }
    if ($search_phone) {
        $sql .= " AND a.phone LIKE ?";
        $params[] = '%' . $search_phone . '%';
    }
    if ($start_date) {
        $sql .= " AND DATE(s.time) >= ?";
        $params[] = $start_date;
    }
    if ($end_date) {
        $sql .= " AND DATE(s.time) <= ?";
        $params[] = $end_date;
    }
    
    // شرط البحث عن الباركود يختلف حسب نوع البيانات
    if ($search_barcode) {
        // الباركود في المبيعات يمكن أن يكون account_id + التاريخ بدون شرطات
        // وفي المرتجعات يمكن أن يكون return_id أو account_id + التاريخ
        if ($table_name === 'returns') {
            $sql .= " AND (CONCAT(s.account_id, REPLACE(DATE(s.time), '-', '')) COLLATE utf8mb4_unicode_ci LIKE ? OR s.return_id LIKE ?)";
            $params[] = '%' . $search_barcode . '%';
            $params[] = '%' . $search_barcode . '%';
        } else {
            $sql .= " AND CONCAT(s.account_id, REPLACE(DATE(s.time), '-', '')) COLLATE utf8mb4_unicode_ci LIKE ?";
            $params[] = '%' . $search_barcode . '%';
        }
    }
    
    $sql .= " GROUP BY DATE(s.time), s.account_id, a.username, a.phone";
    
    // إذا تم تمرير قيمة للمتغير status وكنا نستعلم من جدول المبيعات
    if ($table_name !== 'returns' && $status) {
        $sql .= " HAVING status = ? COLLATE utf8mb4_unicode_ci";
        $params[] = $status;
    }
    
    // ترتيب النتائج بحيث تُعرض أولاً حسب التاريخ (الأحدث أولاً) ثم حسب أحدث توقيت داخل اليوم
    $sql .= " ORDER BY order_date DESC, max_time DESC";
}


$stmt = $conn->prepare($sql);
if ($params) {
    $stmt->bind_param(str_repeat('s', count($params)), ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$orders = [];
while ($row = $result->fetch_assoc()) {
    $row['encrypted_account_id'] = encrypt($row['account_id'], $key);
    if ($table_name === 'returns') {
        $row['remaining'] = $row['total_amount'] - $row['collected']; // الباقي في المرتجعات
    } else {
        $row['remaining'] = $row['total_amount'] - $row['collected']; // الباقي في المبيعات
    }
    // جلب اسم العميل إذا كان account_buyer_id موجوداً
    if (!empty($row['account_buyer_id'])) {
        $stmt2 = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
        $stmt2->bind_param("i", $row['account_buyer_id']);
        $stmt2->execute();
        $stmt2->bind_result($customer_name);
        $stmt2->fetch();
        $stmt2->close();
        $row['customer_name'] = $customer_name;
    } else {
        $row['customer_name'] = null;
    }
    $orders[] = $row;
}

$stmt->close();
$conn->close();

header('Content-Type: application/json');
echo json_encode($orders);
?>
