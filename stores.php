<?php
require_once 'security.php';

// التحقق من صلاحية الوصول لوحة التحكم
checkPagePermission('dashboard', 'view');

include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// الحصول على معرف المستخدم الحالي
$current_user_id = decrypt($_SESSION['account_id'], $key);

// التحقق من صلاحية إضافة فرع جديد
$can_add_store = hasPermission('dashboard', 'add_store');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['store_name'])) {
    // التحقق من صلاحية إضافة فرع
    if (!$can_add_store) {
        echo "<script>Swal.fire('خطأ', 'ليس لديك صلاحية لإضافة فرع جديد', 'error');</script>";
    } else {
        $store_name = trim($_POST['store_name']);
        
        if (!empty($store_name)) {
            $stmt = $conn->prepare("INSERT INTO stores (name) VALUES (?)");
            $stmt->bind_param("s", $store_name);
            if ($stmt->execute()) {
                $new_store_id = $conn->insert_id;
                
                // إذا كان المستخدم لديه صلاحية إدارة فروع محددة، أضف الفرع الجديد لقائمة فروعه
                if (hasPermission('dashboard', 'manage_specific_stores') && !hasPermission('dashboard', 'manage_all_stores')) {
                    $store_access_stmt = $conn->prepare("INSERT INTO user_stores (user_id, store_id, granted) VALUES (?, ?, TRUE)");
                    $store_access_stmt->bind_param("ii", $current_user_id, $new_store_id);
                    $store_access_stmt->execute();
                    $store_access_stmt->close();
                }
                
                header("Location: " . $_SERVER['PHP_SELF']); 
                exit();
            } else {
                echo "<script>Swal.fire('خطأ', 'حدث خطأ أثناء إضافة المخزن', 'error');</script>";
            }
            $stmt->close();
        } else {
            echo "<script>Swal.fire('خطأ', 'يرجى إدخال اسم المخزن', 'error');</script>";
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['selected_store_id'])) {
    $_SESSION['store_id'] = $_POST['selected_store_id']; // Save the encrypted store ID in the session
    echo json_encode(['success' => true]);
    exit();
}

// بناء استعلام الفروع حسب الصلاحيات
$stores_sql = "";
$stores_params = [];
$stores_types = "";

if (hasPermission('dashboard', 'manage_all_stores')) {
    // المستخدم يمكنه رؤية جميع الفروع
    $stores_sql = "SELECT * FROM stores ORDER BY name";
} elseif (hasPermission('dashboard', 'manage_specific_stores')) {
    // الم��تخدم يمكنه رؤية فروع محددة فقط
    $stores_sql = "SELECT s.* FROM stores s 
                   INNER JOIN user_stores us ON s.store_id = us.store_id 
                   WHERE us.user_id = ? AND us.granted = TRUE 
                   ORDER BY s.name";
    $stores_params[] = $current_user_id;
    $stores_types = "i";
} else {
    // المستخدم ليس لديه صلاحية رؤية أي فروع
    $stores_sql = "SELECT * FROM stores WHERE 1=0"; // استعلام فارغ
}

// تنفيذ الاستعلام
if (!empty($stores_params)) {
    $stmt = $conn->prepare($stores_sql);
    $stmt->bind_param($stores_types, ...$stores_params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($stores_sql);
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفروع</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>

<div class="container">
    <h2>إدارة الفروع</h2>
    
    <!-- معلومات الصلاحيات -->
    <?php if (!hasPermission('dashboard', 'manage_all_stores')): ?>
    <div class="permissions-info">
        <?php
        if (hasPermission('dashboard', 'manage_specific_stores')) {
            echo "<span class='permission-warning'><i class='fas fa-exclamation-circle'></i> يمكنك إدارة فروع محددة فقط</span>";
        } else {
            echo "<span class='permission-error'><i class='fas fa-times-circle'></i> ليس لديك صلاحية إدارة الفروع</span>";
        }
        ?>
    </div>
    <?php endif; ?>
    
    <?php if ($can_add_store): ?>
        <button class="add-btn" id="addStoreBtn" title="إضافة فرع جديد">+</button>
    <?php endif; ?>

    <div class="store-list">
        <?php
        if ($result && $result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                $encrypted_store_id = encrypt($row['store_id'], $key);
                
                echo "<div class='store-card' onclick='redirectToCategories(\"{$encrypted_store_id}\")'>
                        <div class='store-icon'>
                            <i class='fas fa-store'></i>
                        </div>
                        <h3>" . htmlspecialchars($row['name']) . "</h3>
                      </div>";
            }
        } else {
            if (hasPermission('dashboard', 'manage_all_stores') || hasPermission('dashboard', 'manage_specific_stores')) {
                echo "<div class='no-stores-message'><i class='fas fa-store-slash'></i> لا توجد فروع متاحة لك حالياً</div>";
            } else {
                echo "<div class='no-access-message'><i class='fas fa-ban'></i> ليس لديك صلاحية للوصول لأي فروع. يرجى الاتصال بالإدارة.</div>";
            }
        }
        ?>
    </div>

    <?php if ($can_add_store): ?>
    <div id="storeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>إضافة فرع جديد</h2>
            <form method="POST">
                <input type="text" name="store_name" class="input-field" placeholder="اسم المخزن" required maxlength="100">
                <button type="submit" class="add-btn">إضافة المخزن</button>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
    // تأكد من تحميل الصفحة أولاً
    document.addEventListener('DOMContentLoaded', function() {
        <?php if ($can_add_store): ?>
        var modal = document.getElementById("storeModal");
        var btn = document.getElementById("addStoreBtn");
        var span = document.getElementsByClassName("close")[0];

        if (btn && modal) {
            btn.onclick = function() {
                modal.style.display = "block";
            }
        }

        if (span && modal) {
            span.onclick = function() {
                modal.style.display = "none";
            }
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }

        // إغلاق النافذة بمفتاح Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && modal && modal.style.display === 'block') {
                modal.style.display = "none";
            }
        });
        <?php endif; ?>
    });

    function redirectToCategories(storeId) {
        // Save the encrypted store ID in the session before redirecting
        fetch('handle_store_selection.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ selected_store_id: storeId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to the store shortcuts page
                window.location.href = `store_shortcuts.php?store_id=${encodeURIComponent(storeId)}`;
            } else {
                alert('حدث خطأ أثناء حفظ معرف الفرع.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الاتصال بالخادم.');
        });
    }
</script>
<script src="js/theme.js"></script>

</body>
</html>

<?php
$conn->close();
?>
