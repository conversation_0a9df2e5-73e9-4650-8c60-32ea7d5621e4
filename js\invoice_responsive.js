document.addEventListener('DOMContentLoaded', function() {
    // استخدام الحاوية المحددة التي تحتوي على الجدول
    const mainContainer = document.querySelector('.main-content');
    const tableContainer = document.querySelector('.table-container');
    const searchBar = document.querySelector('.search-container');
    const itemCountContainer = document.querySelector('.item-count-bar'); // حاوية عدد الأصناف
    let originalTableHTML = '';
    let originalItemCount = document.querySelector('.item-count-bar');
    let categories = [];

    // مصفوفة لتخزين بيانات الباركود مع الأصناف
    let barcodeItemsMap = {};

    // دالة تشغيل صوت الخطأ
    function playErrorSound() {
        try {
            const audio = new Audio('sounds/error.wav');
            audio.volume = 0.7; // تحديد مستوى الصوت
            audio.play().catch(error => {
                console.log('لا يمكن تشغيل الصوت:', error);
            });
        } catch (error) {
            console.log('خطأ في تشغيل الصوت:', error);
        }
    }

    // دالة تشغيل صوت المرتجع
    function playReturnSound() {
        try {
            const audio = new Audio('sounds/return.wav');
            audio.volume = 0.6; // تحديد مستوى الصوت للمرتجع
            audio.play().catch(error => {
                console.log('لا يمكن تشغيل صوت المرتجع:', error);
            });
        } catch (error) {
            console.log('خطأ في تشغيل صوت المرتجع:', error);
        }
    }

    // دالة لإظهار إشعار اختصار الكيبورد
    function showKeyboardShortcutNotification(message) {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: 'Cairo', sans-serif;
        `;

        notification.innerHTML = `
            <i class="fas fa-keyboard"></i>
            ${message}
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار بعد 2.5 ثانية
        setTimeout(() => {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2500);
    }

    // دالة لإظهار نافذة المساعدة مع جميع اختصارات الكيبورد
    function showKeyboardShortcutsHelp() {
        // استخدام SweetAlert إذا كان متاحاً، وإلا استخدام alert عادي
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '<i class="fas fa-keyboard"></i> اختصارات الكيبورد - التصميم المقسم',
                html: `
                    <div style="text-align: right; font-family: 'Cairo', sans-serif;">
                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #007bff; margin-bottom: 15px;"><i class="fas fa-save"></i> أوامر الحفظ:</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                <strong style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">S</strong>
                                <strong style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">س</strong>
                                حفظ الفاتورة الحالية
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #28a745; margin-bottom: 15px;"><i class="fas fa-search"></i> أوامر البحث:</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                <strong>Enter</strong> + <strong>الباركود</strong> - إضافة صنف بالباركود
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #6f42c1; margin-bottom: 15px;"><i class="fas fa-question-circle"></i> أوامر المساعدة:</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                <strong style="background: #6f42c1; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">F1</strong>
                                إظهار هذه النافذة
                            </div>
                        </div>
                        
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; color: #155724;">
                            <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                            <strong>التصميم المقسم:</strong> يتيح لك عرض الأصناف والفئات بشكل منفصل لسهولة الاستخدام
                        </div>
                        
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; color: #856404; margin-top: 10px;">
                            <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                            <strong>ملاحظة:</strong> الاختصارات تعمل فقط عندما لا تكون تكتب في حقول الإدخال
                        </div>
                    </div>
                `,
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#007bff',
                width: '650px',
                customClass: {
                    popup: 'keyboard-shortcuts-modal'
                }
            });
        } else {
            // رسالة مبسطة إذا لم يكن SweetAlert متاحاً
            alert('اختصارات الكيبورد - التصميم المقسم:\n\nS أو س - حفظ الفاتورة\nF1 - مساعدة\nEnter + الباركود - إضافة صنف بالباركود');
        }
    }

    // متغير لتتبع ما إذا تم تحميل invoice.css
    let invoiceCssLoaded = false;

    // دالة لتحميل ملف invoice.css ديناميكياً عند الحاجة
    function loadInvoiceCssIfNeeded() {
        if (!invoiceCssLoaded) {
            const linkElement = document.createElement('link');
            linkElement.rel = 'stylesheet';
            linkElement.href = 'web_css/invoice.css';
            linkElement.id = 'invoice-responsive-css';
            document.head.appendChild(linkElement);
            invoiceCssLoaded = true;
            console.log('تم تحميل ملف invoice.css للتصميم الريسبونسيف');
        }
    }

    // دالة لإزالة ملف invoice.css عند العودة للتصميم العادي
    function unloadInvoiceCss() {
        const linkElement = document.getElementById('invoice-responsive-css');
        if (linkElement) {
            linkElement.remove();
            invoiceCssLoaded = false;
            console.log('تم إزالة ملف invoice.css');
        }
    }

    // إضافة مستمع للأحداث لالتقاط إدخال الباركود
    let barcodeBuffer = '';
    let barcodeTimeoutId = null;
    document.addEventListener('keydown', function(e) {
        // تجاهل الأحداث في عناصر الإدخال
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return;
        }

        // التحقق من أن النظام في الوضع الريسبونسيف فقط
        if (!window.isResponsiveMode || !window.isResponsiveMode()) {
            return; // إذا لم يكن في الوضع الريسبونسيف، لا تعالج هنا
        }

        // اختصار حفظ الفاتورة (حرف S أو س)
        if ((e.key === 'S' || e.key === 's' || e.key === 'س') && !e.ctrlKey && !e.altKey && !e.shiftKey && !e.metaKey) {
            e.preventDefault(); // منع السلوك الافتراضي

            // البحث عن زر الحفظ في التصميم المقسم
            const saveButton = document.querySelector('button[onclick*="view-items"]') || // زر حفظ الفاتورة في التصميم المقسم
                document.getElementById('view-items') || // الزر الأصلي
                document.querySelector('.save-invoice-btn') || // أزرار أخرى محتملة
                document.querySelector('#save-invoice-button') ||
                document.querySelector('.finalize-btn');

            if (saveButton && !saveButton.disabled) {
                // إضافة تأثير بصري
                saveButton.style.boxShadow = '0 0 20px #28a745';
                saveButton.style.transform = 'scale(0.95)';
                saveButton.style.backgroundColor = '#1e7e34';

                // إظهار إشعار للمستخدم
                showKeyboardShortcutNotification('تم استخدام اختصار حفظ الفاتورة (S/س) - التصميم المقسم');

                // تفعيل زر الحفظ بعد تأخير قصير لإظهار التأثير البصري
                setTimeout(() => {
                    saveButton.click();
                }, 150);

                // إزالة التأثير البصري
                setTimeout(() => {
                    saveButton.style.boxShadow = '';
                    saveButton.style.transform = '';
                    saveButton.style.backgroundColor = '';
                }, 350);

                console.log('تم تفعيل حفظ الفاتورة باستخدام اختصار الكيبورد (S/س) - التصميم المقسم');
            }

            return;
        }

        // اختصار المساعدة (مفتاح F1)
        if (e.key === 'F1') {
            e.preventDefault(); // منع فتح المساعدة الافتراضية للمتصفح
            showKeyboardShortcutsHelp();
            return;
        }

        // الباركود عادة ما ينتهي بمفتاح Enter
        if (e.key === 'Enter' && barcodeBuffer.length >= 5) {
            // معالجة الباركود المخصص
            const customBarcodeData = processCustomBarcode(barcodeBuffer);

            if (customBarcodeData) {
                console.log('تم اكتشاف باركود مخصص:', barcodeBuffer);
                console.log('باركود الصنف:', customBarcodeData.itemBarcode);
                console.log('الوزن بالجرام:', customBarcodeData.weightInGrams);
                console.log('الكمية بالكيلو:', customBarcodeData.quantity);

                // البحث عن الصنف بالباركود المستخرج
                const itemId = barcodeItemsMap[customBarcodeData.itemBarcode];
                if (itemId) {
                    // البحث عن العنصر في قائمة الأصناف
                    const item = findItemById(itemId);
                    if (item) {
                        // تحديث كمية الصنف قبل الإضافة وإضافة معلومات الباركود المخصص
                        item.quantity = customBarcodeData.quantity;
                        item.isCustomBarcode = true;
                        item.customBarcodeData = customBarcodeData;
                        console.log('قبل إضافة الصنف - الصنف:', item.name, 'الكمية:', item.quantity);

                        addItemToSelectedList(item);
                        console.log('تمت إضافة الصنف بالباركود المخصص:', barcodeBuffer);
                        console.log('الصنف:', item.name, 'الكمية:', customBarcodeData.quantity, 'كيلو');

                        // إظهار تنبيه بصري مخصص
                        showBarcodeNotification(`تم إضافة ${item.name} - الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو`);

                        // تمييز الصف المضاف
                        highlightAddedItem(itemId);
                    }
                } else {
                    console.log('باركود صنف غير معروف:', customBarcodeData.itemBarcode);

                    // تشغيل صوت الخطأ
                    playErrorSound();

                    // عرض إشعار للباركود غير المعروف
                    showBarcodeNotification(`باركود صنف غير معروف: ${customBarcodeData.itemBarcode}`, 'error');
                }
            } else {
                // معالجة الباركود العادي
                const itemId = barcodeItemsMap[barcodeBuffer];
                if (itemId) {
                    // البحث عن العنصر في قائمة الأصناف
                    const item = findItemById(itemId);
                    if (item) {
                        addItemToSelectedList(item);
                        console.log('تمت إضافة الصنف بالباركود:', barcodeBuffer);

                        // إظهار تنبيه بصري
                        showBarcodeNotification(`تم إضافة ${item.name}`);

                        // تمييز الصف المضاف
                        highlightAddedItem(itemId);
                    }
                } else {
                    console.log('باركود غير معروف:', barcodeBuffer);

                    // تشغيل صوت الخطأ
                    playErrorSound();

                    // عرض إشعار للباركود غير المعروف
                    showBarcodeNotification('باركود غير معروف!', 'error');
                }
            }

            // إعادة تعيين المخزن المؤقت
            barcodeBuffer = '';
            e.preventDefault();
            return;
        }

        // إضافة الحرف إلى المخزن المؤقت وإعادة تعيين المؤقت
        if (e.key.length === 1 || e.key === '-' || e.key === '_') {
            // قارئات الباركود عادة ما تكون سريعة جدًا في إدخال الأحرف
            // إعادة تعيين المخزن المؤقت إذا كان هناك فارق زمني كبير بين الحروف
            if (barcodeTimeoutId) {
                clearTimeout(barcodeTimeoutId);
            }

            barcodeBuffer += e.key;

            // إعادة تعيين المخزن المؤقت بعد 100 مللي ثانية من عدم النشاط
            barcodeTimeoutId = setTimeout(() => {
                barcodeBuffer = '';
                barcodeTimeoutId = null;
            }, 100);
        }
    });

    // دالة للبحث عن عنصر بواسطة معرفه
    function findItemById(itemId) {
        for (let i = 0; i < categories.length; i++) {
            const category = categories[i];
            for (let j = 0; j < category.items.length; j++) {
                const item = category.items[j];
                if (item.id === itemId) {
                    return item;
                }
            }
        }
        return null;
    }

    // دالة معالجة الباركود المخصص
    function processCustomBarcode(barcode) {
        if (!barcode.startsWith('2') || barcode.length !== 13) {
            return null;
        }

        // استخراج باركود الصنف (6 أرقام بعد الرقم 2)
        const itemBarcode = barcode.substring(1, 7);

        // استخراج الوزن بالجرام (5 أرقام من الموضع 7 إلى 11، الرقم 12 هو check digit)
        const weightInGrams = parseInt(barcode.substring(7, 12));
        const quantityInKg = weightInGrams / 1000; // تحويل إلى كيلوجرام

        return {
            itemBarcode: itemBarcode,
            quantity: quantityInKg,
            weightInGrams: weightInGrams,
            isCustomBarcode: true // إضافة علامة للتمييز
        };
    }

    // تغيير لون الشريط العلوي (الهيدر)
    updateHeaderStyle();

    // تحقق مما إذا كان الوضع الداكن مفعلًا
    const isDarkMode = document.body.classList.contains('dark-mode');

    // تنفيذ دالة حفظ الفاتورة المؤقتة داخل ملف invoice_responsive.js
    function saveItemsToFile() {
        try {
            // استخدام نفس الأصناف المضافة من العرض العادي
            const addedItems = window.addedItems || [];
            console.log("Saving items to file:", addedItems.length, "items");

            // الحصول على معرف الحساب المشفر والنوع من URL الحالي
            const urlParams = new URLSearchParams(window.location.search);
            const encryptedAccountId = urlParams.get('account_id');
            if (!encryptedAccountId) {
                console.error('Account ID not found in URL');
                return;
            }

            // الحصول على نوع الفاتورة وبيانات الفرع والحساب إذا كانت متاحة
            const invoiceTypeSelect = document.getElementById('invoice-type');
            const invoiceType = invoiceTypeSelect ? invoiceTypeSelect.value : 'purchase';

            const branchSelect = document.getElementById('branch-select');
            const buyerSelect = document.getElementById('account-select');

            // إنشاء البيانات للإرسال
            const params = new URLSearchParams();
            params.append('account_id', encryptedAccountId);
            params.append('items', JSON.stringify(addedItems));
            params.append('invoice_type', invoiceType);

            // إضافة معلومات الفرع والمشتري إذا كانت فاتورة بيع
            if (invoiceType === 'sale' && branchSelect && buyerSelect) {
                if (branchSelect.value) params.append('branch_id', branchSelect.value);
                if (buyerSelect.value) params.append('account_buyer_id', buyerSelect.value);
            }

            // إرسال البيانات للحفظ باستخدام الوعد (Promise) للتأكد من إكمال العملية
            const url = `save_invoice.php?${params.toString()}`;
            return fetch(url, { method: 'GET' })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to save invoice');
                    }
                    console.log("Items saved successfully");
                    return response;
                })
                .catch(error => {
                    console.error('Error saving invoice:', error);
                });
        } catch (error) {
            console.error('Error in saveItemsToFile:', error);
        }
    }

    // استخدام هذه الدالة بدلاً من الدالة المرجعية من window
    const saveInvoice = saveItemsToFile;

    // تحميل الأصناف وتقسيمها حسب التصنيفات
    function loadCategories() {
        categories = []; // تفريغ المصفوفة

        // إعادة تعيين خريطة الباركود
        barcodeItemsMap = {};

        // الحصول على التصنيفات والأصناف من البيانات المحملة مسبقًا
        try {
            const categoriesData = window.invoiceCategories || [];
            const itemsByCategory = window.invoiceItemsByCategory || {};

            console.log('تحميل التصنيفات:', categoriesData.length, 'تصنيف');
            console.log('تحميل الأصناف حسب التصنيف:', Object.keys(itemsByCategory).length, 'مجموعة');

            // إضافة تصنيف "جميع الأصناف" مع جميع العناصر
            const allItems = [];
            // البحث عن الجدول بطرق مختلفة للتوافق
            const tableRows = document.querySelectorAll('#accountsTable tr, .custom-table tbody tr, .table tbody tr');
            console.log('عدد صفوف الجدول الموجودة:', tableRows.length);
            tableRows.forEach(row => {
                if (row.dataset.itemId) {
                    const itemBarcode = row.dataset.itemBarcode || ''; // الحصول على الباركود إذا كان موجودًا

                    // تخزين الباركود في خريطة الباركود إذا كان موجودًا
                    if (itemBarcode && itemBarcode.trim() !== '') {
                        barcodeItemsMap[itemBarcode] = row.dataset.itemId;
                        console.log('تم إضافة الباركود:', itemBarcode, 'للصنف:', row.dataset.itemName);
                    }

                    allItems.push({
                        id: row.dataset.itemId,
                        name: row.dataset.itemName,
                        price: row.dataset.itemPrice,
                        barcode: itemBarcode, // تخزين الباركود للاستخدام لاحقًا
                        element: row,
                        categoryId: row.dataset.categoryId
                    });
                }
            });

            console.log('عدد الأصناف المحملة:', allItems.length);
            if (allItems.length > 0) {
                categories.push({
                    id: 0,
                    name: "جميع الأصناف",
                    items: allItems
                });
            }

            // إضافة باقي التصنيفات مع العناصر الخاصة بها
            if (categoriesData && categoriesData.length > 0) {
                categoriesData.forEach(category => {
                    // الحصول على العناصر المرتبطة بهذا التصنيف
                    const categoryItems = [];

                    // البحث عن العناصر في الجدول الأصلي بطرق مختلفة للتوافق
                    const categoryRows = document.querySelectorAll(
                        `#accountsTable tr[data-category-id="${category.category_id}"], 
                         .custom-table tbody tr[data-category-id="${category.category_id}"], 
                         .table tbody tr[data-category-id="${category.category_id}"]`
                    );
                    categoryRows.forEach(row => {
                        if (row.dataset.itemId) {
                            const itemBarcode = row.dataset.itemBarcode || ''; // الحصول على الباركود إذا كان موجودًا

                            // تخزين الباركود في خريطة الباركود إذا لم يكن مخزنًا بالفعل
                            if (itemBarcode && itemBarcode.trim() !== '' && !barcodeItemsMap[itemBarcode]) {
                                barcodeItemsMap[itemBarcode] = row.dataset.itemId;
                            }

                            categoryItems.push({
                                id: row.dataset.itemId,
                                name: row.dataset.itemName,
                                price: row.dataset.itemPrice,
                                barcode: itemBarcode, // تخزين الباركود للاستخدام لاحقًا
                                element: row,
                                categoryId: category.category_id
                            });
                        }
                    });

                    // إضافة التصنيف إذا كان يحتوي على عناصر
                    if (categoryItems.length > 0) {
                        categories.push({
                            id: category.category_id,
                            name: category.name,
                            items: categoryItems
                        });
                    }
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);

            // خطة بديلة: إذا حدث خطأ، نستخدم جميع العناصر في تصنيف واحد
            const allItems = [];
            // البحث عن الجدول بطرق مختلفة للتوافق
            const fallbackRows = document.querySelectorAll('#accountsTable tr, .custom-table tbody tr, .table tbody tr');
            fallbackRows.forEach(row => {
                if (row.dataset.itemId) {
                    const itemBarcode = row.dataset.itemBarcode || ''; // الحصول على الباركود إذا كان موجودًا

                    // تخزين الباركود في خريطة الباركود إذا كان موجودًا
                    if (itemBarcode && itemBarcode.trim() !== '') {
                        barcodeItemsMap[itemBarcode] = row.dataset.itemId;
                    }

                    allItems.push({
                        id: row.dataset.itemId,
                        name: row.dataset.itemName,
                        price: row.dataset.itemPrice,
                        barcode: itemBarcode, // تخزين الباركود للاستخدام لاحقًا
                        element: row
                    });
                }
            });

            if (allItems.length > 0) {
                categories.push({
                    id: 0,
                    name: "جميع الأصناف",
                    items: allItems
                });
            }
        }

        console.log(`تم تحميل ${Object.keys(barcodeItemsMap).length} باركود للأصناف.`);
        return categories;
    }

    function createResponsiveLayout() {
        // تحقق من وضع الظلام لتحديد الألوان المناسبة
        const isDarkMode = document.body.classList.contains('dark-mode');
        const bgColor = isDarkMode ? '#0f1419' : '#ffffff';
        const textColor = isDarkMode ? '#e1e8f0' : '#333333';
        const borderColor = isDarkMode ? '#2d3748' : '#eee';
        const sectionBgColor = isDarkMode ? '#1a2332' : '#f8f9fa';
        const tableBgColor = isDarkMode ? '#242b3d' : '#ffffff';
        // تغيير لون هيدر الجدول من الأزرق إلى اللون الرمادي المناسب للوضع
        const tableHeaderBgColor = isDarkMode ? '#2a3441' : '#f0f0f0';
        const tableHeaderTextColor = isDarkMode ? '#e1e8f0' : '#333333';
        const inputBgColor = isDarkMode ? '#242b3d' : '#ffffff';
        const inputTextColor = isDarkMode ? '#e1e8f0' : '#333333';

        // إنشاء قسم التصنيفات
        const categoriesSection = document.createElement('div');
        categoriesSection.className = 'categories';
        categoriesSection.style.minWidth = '250px'; // الحد الأدنى للعرض
        categoriesSection.style.flex = '0.8';
        categoriesSection.style.height = '580px'; // زيادة الارتفاع
        categoriesSection.style.overflowY = 'auto'; // التمرير العمودي
        categoriesSection.style.padding = '15px'; // زيادة التباعد الداخلي قليلاً
        categoriesSection.style.backgroundColor = sectionBgColor;
        categoriesSection.style.color = textColor;
        categoriesSection.innerHTML = `
            <h3 style="margin-top: 5px; margin-bottom: 15px; font-size: 1.5rem; color: ${textColor}">التصنيفات</h3>
            <input type="text" id="categorySearch" class="search-bar" placeholder="ابحث عن تصنيف..." style="background-color: ${inputBgColor}; color: ${inputTextColor};">
            <ul id="categoryList" style="margin-top: 15px;"></ul>
        `;

        // إنشاء قسم الأصناف
        const itemsSection = document.createElement('div');
        itemsSection.className = 'items';
        itemsSection.style.minWidth = '300px'; // الحد الأدنى للعرض
        itemsSection.style.flex = '1.2';
        itemsSection.style.height = '580px'; // زيادة الارتفاع
        itemsSection.style.overflowY = 'auto'; // التمرير العمودي
        itemsSection.style.padding = '15px'; // زيادة التباعد الداخلي قليلاً
        itemsSection.style.backgroundColor = sectionBgColor;
        itemsSection.style.color = textColor;
        itemsSection.innerHTML = `
            <h3 style="margin-top: 5px; margin-bottom: 15px; font-size: 1.5rem; color: ${textColor}">الأصناف</h3>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <div style="flex: 1;">
                    <input type="text" id="itemSearch" class="search-bar" placeholder="ابحث عن صنف..." style="width: 100%; background-color: ${inputBgColor}; color: ${inputTextColor}; border: 1px solid ${borderColor}; border-radius: 5px; padding: 8px 12px;">
                </div>
                <div style="flex: 1;">
                    <div style="position: relative; display: flex; align-items: center;">
                        <input type="text" id="barcodeSearch" class="search-bar" placeholder="ادخل رقم الباركود..." style="width: 100%; background-color: ${inputBgColor}; color: ${inputTextColor}; border: 1px solid ${borderColor}; border-radius: 5px; padding: 8px 12px; padding-left: 35px;">
                        <i class="fas fa-barcode" style="position: absolute; left: 10px; color: ${textColor}; opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
            <table style="margin-top: 15px; width: 100%; background-color: ${tableBgColor}; color: ${textColor}; border-collapse: collapse; border-radius: 8px; overflow: hidden;">
                <thead>
                    <tr>
                        <th style="padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">اسم الصنف</th>
                        <th style="padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">السعر</th>
                        <th style="padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">إضافة</th>
                    </tr>
                </thead>
                <tbody id="itemsList"></tbody>
            </table>
        `;

        // إنشاء قسم الأصناف المختارة مع شريط مثبت للإجمالي
        const selectedItemsSection = document.createElement('div');
        selectedItemsSection.className = 'selected-items';
        selectedItemsSection.style.minWidth = '400px'; // الحد الأدنى للعرض
        selectedItemsSection.style.flex = '2'; // جعل هذا القسم أكبر من الأقسام الأخرى
        selectedItemsSection.style.position = 'relative'; // جعل الموضع نسبي لتثبيت الشريط السفلي
        selectedItemsSection.style.paddingBottom = '80px'; // إضافة مساحة في الأسفل للشريط المثبت
        selectedItemsSection.style.height = '580px'; // زيادة الارتفاع
        selectedItemsSection.style.overflowY = 'auto'; // التمرير العمودي
        selectedItemsSection.style.padding = '15px'; // زيادة التباعد الداخلي قليلاً
        selectedItemsSection.style.backgroundColor = sectionBgColor;
        selectedItemsSection.style.color = textColor;
        selectedItemsSection.innerHTML = `
            <h3 style="margin-top: 5px; margin-bottom: 15px; font-size: 1.5rem; color: ${textColor}">الأصناف المختارة</h3>
            <div class="table-wrapper" style="margin-top: 15px; width: 100%; overflow-y: auto; max-height: calc(100% - 130px);">
                <table style="width: 100%; table-layout: fixed; border-collapse: collapse; background-color: ${tableBgColor}; color: ${textColor}; border-radius: 8px; overflow: hidden;">
                    <thead>
                        <tr>
                            <th style="width: 35%; padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">اسم المنتج</th>
                            <th style="width: 15%; padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">السعر</th>
                            <th style="width: 10%; padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none; text-align: center;">الكمية</th>
                            <th style="width: 18%; padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">الإجمالي</th>
                            <th style="width: 22%; padding: 10px; background-color: ${tableHeaderBgColor}; color: ${tableHeaderTextColor}; border: none;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="selectedItemsTable"></tbody>
                </table>
            </div>

            <!-- شريط الإجمالي مطابق تماماً للتصميم المطلوب -->
            <div id="total_amount" class="total-display" style="background-color: ${isDarkMode ? '#242b3d' : '#f8f9fa'}; color: ${textColor};">
                <span>إجمالي الفاتورة: <span id="total_amount_value">0.00</span></span>
                <button type="button" class="add-btn" onclick="document.getElementById('view-items').click()">حفظ الفاتورة</button>
            </div>
        `;

        return [categoriesSection, itemsSection, selectedItemsSection];
    }

    function setupSearchFunctionality() {
        // بحث في التصنيفات
        const categorySearch = document.getElementById('categorySearch');
        if (categorySearch) {
            categorySearch.addEventListener('input', function() {
                const searchText = this.value.toLowerCase();
                const categoryItems = document.querySelectorAll('#categoryList li');

                categoryItems.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    item.style.display = text.includes(searchText) ? 'block' : 'none';
                });
            });
        }

        // بحث في الأصناف
        const itemSearch = document.getElementById('itemSearch');
        if (itemSearch) {
            itemSearch.addEventListener('input', function() {
                const searchText = this.value.toLowerCase();
                const itemRows = document.querySelectorAll('#itemsList tr');

                itemRows.forEach(row => {
                    const name = row.querySelector('td:first-child').textContent.toLowerCase();
                    row.style.display = name.includes(searchText) ? 'table-row' : 'none';
                });
            });
        }

        // بحث بالباركود
        const barcodeSearch = document.getElementById('barcodeSearch');
        if (barcodeSearch) {
            // إضافة مستمع للتركيز لتحديد محتوى الحقل عند النقر عليه
            barcodeSearch.addEventListener('focus', function() {
                this.select();
            });

            // إضافة مستمع للإدخال للبحث عن الباركود وفلترة الجدول
            barcodeSearch.addEventListener('input', function() {
                const barcode = this.value.trim();

                // فلترة جدول الأصناف بناءً على الباركود
                filterItemsByBarcode(barcode);
            });

            // إضافة مستمع للإدخال للبحث عن الباركود عند الضغط على Enter
            barcodeSearch.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const barcode = this.value.trim();

                    if (barcode.length > 0) {
                        // معالجة الباركود المخصص
                        const customBarcodeData = processCustomBarcode(barcode);

                        if (customBarcodeData) {
                            console.log('تم اكتشاف باركود مخصص في حقل البحث:', barcode);
                            console.log('باركود الصنف:', customBarcodeData.itemBarcode);
                            console.log('الوزن بالجرام:', customBarcodeData.weightInGrams);
                            console.log('الكمية بالكيلو:', customBarcodeData.quantity);

                            // البحث عن الصنف بالباركود المستخرج
                            const itemId = barcodeItemsMap[customBarcodeData.itemBarcode];
                            if (itemId) {
                                // البحث عن العنصر في قائمة الأصناف
                                const item = findItemById(itemId);
                                if (item) {
                                    // تحديث كمية الصنف قبل الإضافة وإضافة معلومات الباركود المخصص
                                    item.quantity = customBarcodeData.quantity;
                                    item.isCustomBarcode = true;
                                    item.customBarcodeData = customBarcodeData;

                                    addItemToSelectedList(item);
                                    console.log('تمت إضافة الصنف بالباركود المخصص:', barcode);
                                    console.log('الصنف:', item.name, 'الكمية:', customBarcodeData.quantity, 'كيلو');

                                    // إظهار تنبيه بصري مخصص
                                    showBarcodeNotification(`تم إضافة ${item.name} - الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو`);

                                    // تمييز الصف المضاف
                                    highlightAddedItem(itemId);

                                    // مسح حقل البحث
                                    this.value = '';

                                    // إعادة عرض جميع الأصناف
                                    filterItemsByBarcode('');
                                }
                            } else {
                                console.log('باركود صنف غير معروف:', customBarcodeData.itemBarcode);

                                // تشغيل صوت الخطأ
                                playErrorSound();

                                // عرض إشعار للباركود غير المعروف
                                showBarcodeNotification(`باركود صنف غير معروف: ${customBarcodeData.itemBarcode}`, 'error');
                            }
                        } else {
                            // معالجة الباركود العادي
                            const itemId = barcodeItemsMap[barcode];
                            if (itemId) {
                                // البحث عن العنصر في قائمة الأصناف
                                const item = findItemById(itemId);
                                if (item) {
                                    addItemToSelectedList(item);
                                    console.log('تمت إضافة الصنف بالباركود:', barcode);

                                    // إظهار تنبيه بصري
                                    showBarcodeNotification(`تم إضافة ${item.name}`);

                                    // تمييز الصف المضاف
                                    highlightAddedItem(itemId);

                                    // مسح حقل البحث
                                    this.value = '';

                                    // إعادة عرض جميع الأصناف
                                    filterItemsByBarcode('');
                                }
                            } else {
                                console.log('باركود غير معروف:', barcode);

                                // تشغيل صوت الخطأ
                                playErrorSound();

                                // عرض إشعار للباركود غير المعروف
                                showBarcodeNotification('باركود غير معروف!', 'error');
                            }
                        }
                    }
                }
            });
        }

        // دالة لفلترة الأصناف بناءً على الباركود
        function filterItemsByBarcode(barcode) {
            const itemRows = document.querySelectorAll('#itemsList tr');

            // إذا كان الباركود فارغًا، أظهر جميع الأصناف
            if (barcode === '') {
                itemRows.forEach(row => {
                    row.style.display = 'table-row';
                });
                return;
            }

            // عدد الأصناف المطابقة
            let matchCount = 0;

            // فلترة الأصناف بناءً على الباركود
            itemRows.forEach(row => {
                const itemId = row.dataset.itemId;
                let isMatch = false;

                // البحث في خريطة الباركود
                for (const [barcodeKey, id] of Object.entries(barcodeItemsMap)) {
                    if (id === itemId && barcodeKey.includes(barcode)) {
                        isMatch = true;
                        matchCount++;
                        break;
                    }
                }

                // عرض أو إخفاء الصف بناءً على المطابقة
                row.style.display = isMatch ? 'table-row' : 'none';
            });

            // إذا كان هناك صنف واحد فقط مطابق وكان الباركود كاملاً، قم بتمييزه
            if (matchCount === 1 && Object.keys(barcodeItemsMap).includes(barcode)) {
                const itemId = barcodeItemsMap[barcode];
                highlightAddedItem(itemId);
            }
        }
    }

    // دالة لإظهار إشعار عند إضافة صنف بالباركود
    function showBarcodeNotification(message, type = 'success') {
        // التحقق من وجود toastr
        if (typeof toastr !== 'undefined') {
            if (type === 'success') {
                toastr.success(message, 'إضافة بالباركود');
            } else if (type === 'error') {
                toastr.error(message, 'خطأ في الباركود');
                // تشغيل صوت الخطأ فقط للأخطاء
                if (typeof window.playSound === 'function') {
                    window.playSound('error');
                }
            }
        } else {
            // إذا لم يكن toastr متاحًا، استخدم تنبيه بسيط
            alert(message);
        }
    }

    // دالة لتمييز الصف المضاف
    function highlightAddedItem(itemId) {
        const itemRows = document.querySelectorAll('#itemsList tr');
        itemRows.forEach(row => {
            if (row.dataset.itemId === itemId) {
                // إضافة تأثير وميض للصف
                row.style.transition = 'background-color 0.5s';

                // تحقق من وضع الظلام
                const isDarkMode = document.body.classList.contains('dark-mode');
                const highlightColor = isDarkMode ? '#5b9bd5' : '#cce5ff';

                row.style.backgroundColor = highlightColor;

                // إعادة لون الخلفية إلى الوضع الطبيعي بعد ثانية واحدة
                setTimeout(() => {
                    row.style.backgroundColor = isDarkMode ? '#242b3d' : '#ffffff';
                }, 1000);
            }
        });
    }

    function populateCategories() {
        const categoryList = document.getElementById('categoryList');
        if (!categoryList) return;

        categoryList.innerHTML = '';

        // إضافة أنماط CSS للقائمة
        categoryList.style.listStyle = 'none';
        categoryList.style.padding = '0';
        categoryList.style.margin = '0';

        // تحقق من وضع الظلام
        const isDarkMode = document.body.classList.contains('dark-mode');
        const categoryBgColor = isDarkMode ? '#242b3d' : '#f8f9fa';
        const categoryTextColor = isDarkMode ? '#b8c5d1' : '#333';
        const categoryActiveBgColor = isDarkMode ? '#1e3a8a' : '#3b82f6';

        categories.forEach((category, index) => {
            const li = document.createElement('li');
            li.textContent = category.name;
            li.dataset.index = index;
            li.dataset.id = category.id;

            // تنسيق عنصر القائمة
            li.style.padding = '10px 15px';
            li.style.margin = '5px 0';
            li.style.borderRadius = '8px';
            li.style.backgroundColor = categoryBgColor;
            li.style.color = categoryTextColor;
            li.style.cursor = 'pointer';
            li.style.transition = 'all 0.2s ease';

            // تحديد التصنيف عند النقر
            li.addEventListener('click', function() {
                // إزالة التحديد من جميع العناصر
                document.querySelectorAll('#categoryList li').forEach(item => {
                    item.style.backgroundColor = categoryBgColor;
                    item.style.color = categoryTextColor;
                    item.style.fontWeight = 'normal';
                });

                // تحديد العنصر الحالي
                this.style.backgroundColor = categoryActiveBgColor;
                this.style.color = 'white';
                this.style.fontWeight = 'bold';

                populateItems(category.items, category.id);
            });

            categoryList.appendChild(li);
        });

        // العرض الافتراضي للعناصر من الفئة الأولى وتحديد التصنيف الأول
        if (categories.length > 0) {
            const firstCategory = categoryList.querySelector('li[data-index="0"]');
            if (firstCategory) {
                firstCategory.style.backgroundColor = isDarkMode ? '#1e3a8a' : '#007bff';
                firstCategory.style.color = 'white';
                firstCategory.style.fontWeight = 'bold';
            }
            populateItems(categories[0].items, categories[0].id);
        }
    }

    function populateItems(items, categoryId) {
        const itemsList = document.getElementById('itemsList');
        if (!itemsList) return;

        itemsList.innerHTML = '';

        // تحقق من وضع الظلام
        const isDarkMode = document.body.classList.contains('dark-mode');

        // إذا تم تحديد تصنيف معين وليس "جميع الأصناف"
        if (categoryId && categoryId !== 0) {
            // تصفية العناصر للحصول على العناصر التي تنتمي لهذا التصنيف فقط
            items = items.filter(item => item.categoryId == categoryId);
        }

        items.forEach(item => {
            const tr = document.createElement('tr');
            tr.dataset.itemId = item.id;
            tr.dataset.itemName = item.name;
            tr.dataset.itemPrice = item.element ? item.element.dataset.itemPrice : (item.price || '0');
            tr.dataset.categoryId = item.categoryId;
            tr.style.cursor = 'pointer'; // تغيير مؤشر الماوس عند تمريره فوق الصف

            // تعيين لون الخلفية المناسب للوضع
            tr.style.backgroundColor = isDarkMode ? '#242b3d' : '#ffffff';
            tr.style.color = isDarkMode ? '#e1e8f0' : '#333333';
            tr.style.borderBottom = `1px solid ${isDarkMode ? '#3a4553' : '#eee'}`;

            tr.innerHTML = `
                <td>${item.name}</td>
                <td>${tr.dataset.itemPrice}</td>
                <td class="add-cell">
                    <button class="btn btn-primary add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </td>
            `;

            // تغيير لون الخلفية عند تمرير المؤشر
            tr.addEventListener('mouseenter', function() {
                this.style.backgroundColor = isDarkMode ? '#2a3441' : '#f5f5f5';
            });

            tr.addEventListener('mouseleave', function() {
                this.style.backgroundColor = isDarkMode ? '#242b3d' : '#ffffff';
            });

            // إضافة النقر على الصف بالكامل لإضافة الصنف
            tr.addEventListener('click', function(e) {
                // تجنب تنفيذ الحدث مرتين إذا تم النقر على الزر
                if (!e.target.closest('.add-btn')) {
                    addItemToSelectedList(item);
                }
            });

            // الإبقاء على زر الإضافة أيضًا للاستخدام
            const addBtn = tr.querySelector('.add-btn');
            addBtn.addEventListener('click', function(e) {
                e.stopPropagation(); // منع انتشار الحدث للصف
                addItemToSelectedList(item);
            });

            itemsList.appendChild(tr);
        });
    }

    function addItemToSelectedList(item) {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        if (!selectedItemsTable) return;

        // تحقق من وضع الظلام
        const isDarkMode = document.body.classList.contains('dark-mode');
        const rowBgColor = isDarkMode ? '#242b3d' : '#ffffff';
        const textColor = isDarkMode ? '#e1e8f0' : '#333333';
        const borderColor = isDarkMode ? '#3a4553' : '#eee';
        const inputBgColor = isDarkMode ? '#2a3441' : '#ffffff'; // تعريف لون خلفية حقول الإدخال

        // تأكد من أن window.addedItems موجود
        if (!window.addedItems) {
            window.addedItems = [];
        }

        // تحقق مما إذا كان العنصر موجودًا بالفعل في window.addedItems
        const existingItemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);

        if (existingItemIndex !== -1) {
            // العنصر موجود بالفعل، قم بزيادة الكمية أو استخدام الكمية المحددة
            const currentQuantity = parseFloat(window.addedItems[existingItemIndex].quantity) || 1;
            const newQuantity = item.quantity ? parseFloat(item.quantity) : (currentQuantity + 1);
            window.addedItems[existingItemIndex].quantity = newQuantity;

            // تحديث واجهة المستخدم
            const existingRow = Array.from(selectedItemsTable.querySelectorAll('tr')).find(
                row => row.dataset.itemId === item.id
            );

            if (existingRow) {
                const quantityInput = existingRow.querySelector('.quantity-input');
                if (quantityInput) {
                    quantityInput.value = newQuantity;

                    // تحديث خلية الإجمالي مباشرة
                    const price = parseFloat(existingRow.dataset.itemPrice) || 0;
                    let newTotal;

                    // التحقق من وجود معلومات الباركود المخصص في العنصر الموجود
                    const existingItem = window.addedItems[existingItemIndex];
                    if (existingItem.isCustomBarcode && existingItem.customBarcodeData) {
                        newTotal = newQuantity * price;
                        console.log('تحديث عنصر موجود بباركود مخصص - الكمية:', newQuantity, 'السعر/كيلو:', price, 'الإجمالي:', newTotal);
                    } else {
                        newTotal = newQuantity * price;
                    }

                    const totalCell = existingRow.querySelector('.item-total');
                    if (totalCell) {
                        totalCell.textContent = newTotal.toFixed(2);
                    }

                    // إطلاق حدث input لضمان تحديث كل شيء
                    quantityInput.dispatchEvent(new Event('input'));

                    // تحديث الكمية في الصف الأصلي إذا كان موجودًا
                    const originalTable = document.getElementById('accountsTable');
                    if (originalTable) {
                        const originalRow = Array.from(originalTable.querySelectorAll('tr.highlight')).find(
                            row => row.dataset.itemId === item.id
                        );
                        if (originalRow) {
                            const originalQuantityInput = originalRow.querySelector('.quantity-input');
                            if (originalQuantityInput) {
                                originalQuantityInput.value = newQuantity;
                            }
                        }
                    }
                }
            }

            // تحديث إجمالي الفاتورة
            updateInvoiceTotal();

            // حفظ الفاتورة المؤقتة
            saveInvoice();
            return;
        }

        // الحصول على سعر العنصر
        const itemPrice = item.element ? item.element.dataset.itemPrice : (item.price || '0');

        // إضافة العنصر إلى window.addedItems
        window.addedItems.push({
            id: item.id,
            name: item.name,
            quantity: item.quantity || 1,
            price: itemPrice,
            isCustomBarcode: item.isCustomBarcode || false,
            customBarcodeData: item.customBarcodeData || null
        });

        // إنشاء صف جديد للعنصر
        const tr = document.createElement('tr');
        tr.dataset.itemId = item.id;
        tr.dataset.itemName = item.name;
        tr.dataset.itemPrice = itemPrice;
        tr.dataset.isCustomBarcode = item.isCustomBarcode || false;
        if (item.customBarcodeData) {
            tr.dataset.customBarcodeData = JSON.stringify(item.customBarcodeData);
        }
        tr.style.borderBottom = `1px solid ${borderColor}`; // إضافة خط فاصل بين الصفوف مع مراعاة الوضع المظلم
        tr.style.backgroundColor = rowBgColor;
        tr.style.color = textColor;

        // إنشاء خلية الاسم
        const nameCell = document.createElement('td');
        nameCell.style.textAlign = 'right'; // محاذاة النص إلى اليمين
        nameCell.style.paddingRight = '20px'; // إضافة تباعد يمين
        nameCell.style.paddingTop = '8px'; // زيادة التباعد العلوي
        nameCell.style.paddingBottom = '8px'; // زيادة التباعد السفلي
        nameCell.style.color = textColor;
        nameCell.innerHTML = `
            <span style="display: inline-block; vertical-align: middle;">${item.name}</span>
        `;

        // إضافة خلية للسعر (سعر الوحدة)
        const priceCell = document.createElement('td');
        priceCell.style.textAlign = 'center'; // توسيط محتوى الخلية
        priceCell.style.paddingTop = '8px'; // زيادة التباعد العلوي
        priceCell.style.paddingBottom = '8px'; // زيادة التباعد السفلي
        priceCell.style.color = textColor;
        priceCell.style.fontSize = '12px'; // تصغير حجم الخط
        priceCell.textContent = `${itemPrice}/كيلو`;

        // إنشاء خلية الكمية
        const quantityCell = document.createElement('td');
        quantityCell.style.width = '10%'; // تحديد عرض ثابت
        quantityCell.style.maxWidth = '60px'; // حد أقصى للعرض
        quantityCell.style.minWidth = '60px'; // حد أدنى للعرض
        quantityCell.style.textAlign = 'center';
        quantityCell.style.paddingTop = '8px';
        quantityCell.style.paddingBottom = '8px';
        quantityCell.style.paddingRight = '5px'; // تقليل التباعد
        quantityCell.style.paddingLeft = '5px'; // تقليل التباعد
        quantityCell.style.color = textColor;
        quantityCell.innerHTML = `
            <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="${item.quantity || 1}" style="width: 50px !important; max-width: 50px !important; min-width: 50px !important; margin: auto; padding: 4px 2px !important; text-align: center; background-color: ${inputBgColor}; color: ${textColor}; border-color: ${borderColor}; font-size: 12px;">
        `;

        // إضافة خلية جديدة للإجمالي
        const totalCell = document.createElement('td');
        totalCell.className = 'item-total';
        totalCell.style.textAlign = 'center';
        totalCell.style.paddingTop = '8px';
        totalCell.style.paddingBottom = '8px';
        totalCell.style.paddingRight = '10px'; // إضافة تباعد يمين
        totalCell.style.paddingLeft = '10px'; // إضافة تباعد يسار
        totalCell.style.color = textColor;
        totalCell.style.fontWeight = 'bold';

        // حساب الإجمالي
        const itemQuantity = parseFloat(item.quantity || 1);
        let itemTotal;

        // إذا كان باركود مخصص، احسب الإجمالي بناءً على الوزن الفعلي
        if (item.isCustomBarcode && item.customBarcodeData) {
            itemTotal = itemQuantity * parseFloat(itemPrice || 0);
            console.log('باركود مخصص - الصنف:', item.name, 'الكمية:', itemQuantity, 'السعر/كيلو:', itemPrice, 'الإجمالي:', itemTotal);
        } else {
            itemTotal = itemQuantity * parseFloat(itemPrice || 0);
            console.log('إنشاء صف جديد - الصنف:', item.name, 'الكمية:', itemQuantity, 'السعر:', itemPrice, 'الإجمالي:', itemTotal);
        }

        totalCell.textContent = itemTotal.toFixed(2);

        const actionsCell = document.createElement('td');
        actionsCell.style.textAlign = 'center'; // توسيط محتوى الخلية
        actionsCell.style.paddingTop = '8px'; // زيادة التباعد العلوي
        actionsCell.style.paddingBottom = '8px'; // زيادة التباعد السفلي
        actionsCell.style.color = textColor;
        actionsCell.innerHTML = `
            <button type="button" class="btn btn-danger btn-sm remove-btn" style="padding: 4px 8px;">
                <i class="fas fa-trash"></i>
            </button>
        `;

        // إضافة الخلايا إلى الصف
        tr.appendChild(nameCell);
        tr.appendChild(priceCell);
        tr.appendChild(quantityCell);
        tr.appendChild(totalCell);
        tr.appendChild(actionsCell);

        // إضافة معالج حدث لزر الحذف
        const removeBtn = actionsCell.querySelector('.remove-btn');
        removeBtn.addEventListener('click', function() {
            // استخدام وظيفة الحذف المشتركة
            removeItemFromBothTables(item.id);

            // تحديث واجهة المستخدم
            updateItemCount();
            updateInvoiceTotal();
        });

        // معالجة تغيير الكمية
        const quantityInput = quantityCell.querySelector('.quantity-input');
        quantityInput.addEventListener('input', function() {
            // تحديث الكمية في window.addedItems
            const itemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);
            if (itemIndex !== -1) {
                window.addedItems[itemIndex].quantity = parseFloat(this.value) || 1;
            }

            // تحديث خلية الإجمالي
            const quantity = parseFloat(this.value) || 1;
            const price = parseFloat(tr.dataset.itemPrice) || 0;
            let total;

            // إذا كان باركود مخصص، احسب الإجمالي بناءً على الكمية الجديدة
            if (item.isCustomBarcode && item.customBarcodeData) {
                total = quantity * price;
                console.log('تحديث كمية باركود مخصص - الكمية الجديدة:', quantity, 'السعر/كيلو:', price, 'الإجمالي:', total);
            } else {
                total = quantity * price;
            }

            const totalCell = tr.querySelector('.item-total');
            if (totalCell) {
                totalCell.textContent = total.toFixed(2);
            }

            // تحديث إجمالي الفاتورة
            updateInvoiceTotal();

            // استخدام setTimeout لتأخير عملية الحفظ
            setTimeout(() => {
                saveInvoice();
            }, 300);
        });

        selectedItemsTable.appendChild(tr);
        updateItemCount();

        // إضافة العنصر إلى القائمة الأصلية أيضًا (للتبديل السلس)
        addItemToOriginalTable(item);

        // حفظ الفاتورة المؤقتة بعد إضافة العنصر
        saveInvoice();
    }

    // دالة جديدة لإضافة الصنف إلى الجدول الأصلي
    function addItemToOriginalTable(item) {
        const originalTable = document.getElementById('accountsTable');
        if (!originalTable) return;

        const originalRow = Array.from(originalTable.querySelectorAll('tr')).find(
            row => row.dataset.itemId === item.id
        );

        if (originalRow && !originalRow.classList.contains('highlight')) {
            originalRow.classList.add('highlight');
            const nameCell = originalRow.querySelector('td:first-child');
            const cell = originalRow.querySelector('.add-cell');

            if (nameCell) {
                nameCell.innerHTML = `
                    <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                    ${item.name}
                `;
            }

            if (cell) {
                cell.innerHTML = `
                    <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="${item.quantity || 1}" style="width: 80px; margin: auto;">
                `;

                // إضافة معالج حدث لتغيير الكمية
                const quantityInput = cell.querySelector('.quantity-input');
                quantityInput.addEventListener('input', function() {
                    // تحديث الكمية في القائمة المتجاوبة
                    const responsiveTable = document.getElementById('selectedItemsTable');
                    if (responsiveTable) {
                        const responsiveRow = Array.from(responsiveTable.querySelectorAll('tr')).find(
                            row => row.dataset.itemId === item.id
                        );
                        if (responsiveRow) {
                            const responsiveQuantityInput = responsiveRow.querySelector('.quantity-input');
                            if (responsiveQuantityInput) {
                                responsiveQuantityInput.value = this.value;
                            }
                        }
                    }
                });
            }

            // إضافة معالج حدث للإزالة
            const removeIcons = originalRow.querySelectorAll('.remove-item');
            removeIcons.forEach(icon => {
                icon.addEventListener('click', function() {
                    removeItemFromBothTables(item.id);
                });
            });
        }
    }

    // دالة جديدة لإزالة الصنف من الجدول الأصلي
    function removeItemFromOriginalTable(itemId) {
        const originalTable = document.getElementById('accountsTable');
        if (!originalTable) return;

        const originalRow = Array.from(originalTable.querySelectorAll('tr')).find(
            row => row.dataset.itemId === itemId
        );

        if (originalRow) {
            const itemName = originalRow.dataset.itemName;
            originalRow.classList.remove('highlight');

            const nameCell = originalRow.querySelector('td:first-child');
            const cell = originalRow.querySelector('td:last-child');

            if (nameCell) {
                nameCell.innerHTML = `<i class="fas fa-box-open text-blue-500 mr-2"></i> ${itemName}`;
            }

            if (cell) {
                cell.innerHTML = `
                    <button class="btn btn-primary add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                `;

                // إعادة ربط حدث الإضافة
                const addBtn = cell.querySelector('.add-btn');
                if (addBtn) {
                    addBtn.addEventListener('click', function() {
                        addItemToSelectedList({
                            id: itemId,
                            name: itemName
                        });
                    });
                }
            }
        }
    }

    // دالة جديدة للحذف المنسق للعناصر من كلا الجدولين
    function removeItemFromBothTables(itemId) {
        // حذف العنصر من window.addedItems
        if (window.addedItems) {
            const itemIndex = window.addedItems.findIndex(item => item.id === itemId);
            if (itemIndex !== -1) {
                window.addedItems.splice(itemIndex, 1);
            }
        }

        // إزالة من الجدول الأصلي
        removeItemFromOriginalTable(itemId);

        // إزالة من جدول الأصناف المختارة
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        if (selectedItemsTable) {
            const responsiveRow = Array.from(selectedItemsTable.querySelectorAll('tr')).find(
                row => row.dataset.itemId === itemId
            );
            if (responsiveRow) {
                responsiveRow.remove();
            }
        }

        updateItemCount();

        // حفظ الفاتورة المؤقتة بعد حذف العنصر
        // استخدام setTimeout لتأخير عملية الحفظ بعد اكتمال عمليات التحديث
        setTimeout(() => {
            // تأكد من أن window.addedItems بها محتوى قبل الحفظ
            if (window.addedItems && window.addedItems.length >= 0) {
                saveInvoice();
            }
        }, 100);
    }

    function updateItemCount() {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        const itemCountElement = document.getElementById('item-count');

        if (selectedItemsTable && itemCountElement) {
            const count = selectedItemsTable.querySelectorAll('tr').length;
            itemCountElement.textContent = count;

            // حساب إجمالي الفاتورة
            updateInvoiceTotal();
        }
    }

    // دالة جديدة لحساب وتحديث إجمالي الفاتورة
    function updateInvoiceTotal() {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        const invoiceTotalElement = document.getElementById('total_amount_value');

        if (selectedItemsTable && invoiceTotalElement) {
            let total = 0;

            // حساب الإجمالي باستخدام خلايا الإجمالي مباشرة
            const totalCells = selectedItemsTable.querySelectorAll('.item-total');
            totalCells.forEach(cell => {
                const rowTotal = parseFloat(cell.textContent) || 0;
                total += rowTotal;
            });

            // تحديث العرض
            invoiceTotalElement.textContent = total.toFixed(2);
        }
    }

    // دالة لاسترجاع الأصناف المحفوظة في العرض المتجاوب
    function restoreSavedItemsResponsive() {
        // الحصول على الأصناف المحفوظة من window.addedItems
        const addedItems = window.addedItems || [];
        if (!addedItems.length) {
            return; // لا توجد أصناف محفوظة
        }

        // تحقق من وضع الظلام
        const isDarkMode = document.body.classList.contains('dark-mode');
        const rowBgColor = isDarkMode ? '#242b3d' : '#ffffff';
        const textColor = isDarkMode ? '#e1e8f0' : '#333333';
        const borderColor = isDarkMode ? '#3a4553' : '#eee';
        const inputBgColor = isDarkMode ? '#2a3441' : '#ffffff'; // تعريف لون خلفية حقول الإدخال

        // الحصول على جدول الأصناف المختارة
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        if (!selectedItemsTable) {
            return;
        }

        // مسح الجدول
        selectedItemsTable.innerHTML = '';

        // إضافة الأصناف المحفوظة إلى الجدول
        addedItems.forEach(item => {
            const tr = document.createElement('tr');
            tr.dataset.itemId = item.id;
            tr.dataset.itemName = item.name;
            tr.dataset.itemPrice = item.price || '0';
            tr.dataset.isCustomBarcode = item.isCustomBarcode || false;
            if (item.customBarcodeData) {
                tr.dataset.customBarcodeData = JSON.stringify(item.customBarcodeData);
            }
            tr.style.borderBottom = `1px solid ${borderColor}`;
            tr.style.backgroundColor = rowBgColor;
            tr.style.color = textColor;

            // إنشاء خلية الاسم
            const nameCell = document.createElement('td');
            nameCell.style.textAlign = 'right';
            nameCell.style.paddingRight = '20px';
            nameCell.style.paddingTop = '8px';
            nameCell.style.paddingBottom = '8px';
            nameCell.style.color = textColor;
            nameCell.innerHTML = `
                <span style="display: inline-block; vertical-align: middle;">${item.name}</span>
            `;

            // إنشاء خلية السعر
            const priceCell = document.createElement('td');
            priceCell.style.textAlign = 'center';
            priceCell.style.paddingTop = '8px';
            priceCell.style.paddingBottom = '8px';
            priceCell.style.color = textColor;
            priceCell.textContent = item.price || '0';

            // إنشاء خلية الكمية
            const quantityCell = document.createElement('td');
            quantityCell.style.width = '10%'; // تحديد عرض ثابت
            quantityCell.style.maxWidth = '60px'; // حد أقصى للعرض
            quantityCell.style.minWidth = '60px'; // حد أدنى للعرض
            quantityCell.style.textAlign = 'center';
            quantityCell.style.paddingTop = '8px';
            quantityCell.style.paddingBottom = '8px';
            quantityCell.style.paddingRight = '5px'; // تقليل التباعد
            quantityCell.style.paddingLeft = '5px'; // تقليل التباعد
            quantityCell.style.color = textColor;
            quantityCell.innerHTML = `
                <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="0.1" value="${item.quantity || 1}" style="width: 50px !important; max-width: 50px !important; min-width: 50px !important; margin: auto; padding: 4px 2px !important; text-align: center; background-color: ${inputBgColor}; color: ${textColor}; border-color: ${borderColor}; font-size: 12px;">
            `;

            // إضافة خلية الإجمالي
            const totalCell = document.createElement('td');
            totalCell.className = 'item-total';
            totalCell.style.textAlign = 'center';
            totalCell.style.paddingTop = '8px';
            totalCell.style.paddingBottom = '8px';
            totalCell.style.paddingRight = '10px'; // إضافة تباعد يمين
            totalCell.style.paddingLeft = '10px'; // إضافة تباعد يسار
            totalCell.style.color = textColor;
            totalCell.style.fontWeight = 'bold';

            // حساب الإجمالي
            const itemQuantity = parseFloat(item.quantity || 1);
            const itemPrice = parseFloat(item.price || 0);
            let itemTotal;

            console.log('تفاصيل حساب الإجمالي في restoreSavedItemsResponsive:');
            console.log('- اسم الصنف:', item.name);
            console.log('- الكمية:', itemQuantity);
            console.log('- السعر:', itemPrice);
            console.log('- هل باركود مخصص؟', item.isCustomBarcode);
            console.log('- بيانات الباركود المخصص:', item.customBarcodeData);

            // إذا كان باركود مخصص، احسب الإجمالي بناءً على الوزن الفعلي
            if (item.isCustomBarcode && item.customBarcodeData) {
                itemTotal = itemQuantity * itemPrice;
                console.log('حساب باركود مخصص - الإجمالي:', itemTotal);
            } else {
                itemTotal = itemQuantity * itemPrice;
                console.log('حساب صنف عادي - الإجمالي:', itemTotal);
            }

            totalCell.textContent = itemTotal.toFixed(2);

            // إنشاء خلية الإجراءات
            const actionsCell = document.createElement('td');
            actionsCell.style.textAlign = 'center';
            actionsCell.style.paddingTop = '8px';
            actionsCell.style.paddingBottom = '8px';
            actionsCell.style.color = textColor;
            actionsCell.innerHTML = `
                <button type="button" class="btn btn-danger btn-sm remove-btn" style="padding: 4px 8px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;

            // إضافة الخلايا إلى الصف
            tr.appendChild(nameCell);
            tr.appendChild(priceCell);
            tr.appendChild(quantityCell);
            tr.appendChild(totalCell);
            tr.appendChild(actionsCell);

            // إضافة معالج حدث للحذف
            const removeBtn = actionsCell.querySelector('.remove-btn');
            removeBtn.addEventListener('click', function() {
                // استخدام وظيفة الحذف المشتركة
                removeItemFromBothTables(item.id);
            });

            // معالجة تغيير الكمية
            const quantityInput = quantityCell.querySelector('.quantity-input');
            quantityInput.addEventListener('input', function() {
                // تحديث الكمية في window.addedItems
                const itemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);
                if (itemIndex !== -1) {
                    window.addedItems[itemIndex].quantity = parseFloat(this.value) || 1;
                }

                // تحديث خلية الإجمالي
                const quantity = parseFloat(this.value) || 1;
                const price = parseFloat(tr.dataset.itemPrice) || 0;
                let total;

                // التحقق من نوع الباركود لحساب الإجمالي الصحيح
                const isCustomBarcode = tr.dataset.isCustomBarcode === 'true';
                if (isCustomBarcode) {
                    total = quantity * price;
                    console.log('تحديث كمية باركود مخصص - الصنف:', tr.dataset.itemName, 'الكمية الجديدة:', quantity, 'السعر/كيلو:', price, 'الإجمالي الجديد:', total);
                } else {
                    total = quantity * price;
                    console.log('تحديث كمية صنف عادي - الصنف:', tr.dataset.itemName, 'الكمية الجديدة:', quantity, 'السعر:', price, 'الإجمالي الجديد:', total);
                }

                const totalCell = tr.querySelector('.item-total');
                if (totalCell) {
                    totalCell.textContent = total.toFixed(2);
                }

                // تحديث إجمالي الفاتورة
                updateInvoiceTotal();

                // استخدام setTimeout لتأخير عملية الحفظ
                setTimeout(() => {
                    saveInvoice();
                }, 300);
            });

            // إضافة الصف إلى الجدول
            selectedItemsTable.appendChild(tr);
        });

        // تحديث إجمالي الفاتورة
        updateInvoiceTotal();
    }

    function handleScreenSize() {
        console.log('Checking screen size...');
        const screenWidth = window.innerWidth;
        console.log('Screen width:', screenWidth);

        // فحص وجود العناصر المطلوبة
        if (!mainContainer) {
            console.log('Main container not found, skipping responsive handling');
            return;
        }

        // تحديث لون الهيدر عند كل فحص لحجم الشاشة
        updateHeaderStyle();

        if (screenWidth >= 768) { // للشاشات الكبيرة
            console.log('Large screen detected');

            if (!document.querySelector('.responsive-container')) {
                console.log('Switching to responsive layout');

                // تحميل ملف invoice.css فقط عند تفعيل التصميم الريسبونسيف
                loadInvoiceCssIfNeeded();

                // حفظ HTML الأصلي إذا لم يتم حفظه بالفعل
                if (!originalTableHTML && mainContainer) {
                    originalTableHTML = mainContainer.innerHTML;
                    categories = loadCategories();
                }

                // إخفاء شريط البحث العلوي وشريط عدد الأصناف
                if (searchBar) {
                    searchBar.style.display = 'none';
                }

                // إخفاء حاوية عدد الأصناف المضافة
                if (itemCountContainer) {
                    itemCountContainer.style.display = 'none';
                }

                // إنشاء تخطيط متجاوب جديد
                const responsiveContainer = document.createElement('div');
                responsiveContainer.className = 'container responsive-container';
                responsiveContainer.style.display = 'flex';
                responsiveContainer.style.gap = '20px'; // تقليل المسافة بين الأقسام
                responsiveContainer.style.padding = '25px'; // زيادة التباعد الداخلي قليلاً
                responsiveContainer.style.maxWidth = '1200px';
                responsiveContainer.style.margin = '25px auto'; // زيادة الهوامش قليلاً
                responsiveContainer.style.textAlign = 'center';

                // تطبيق ألوان الوضع المظلم إذا كان مفعلاً
                const isDarkMode = document.body.classList.contains('dark-mode');
                responsiveContainer.style.backgroundColor = isDarkMode ? '#0f1419' : '#ffffff';
                responsiveContainer.style.color = isDarkMode ? '#e1e8f0' : '#333333';
                responsiveContainer.style.borderRadius = '20px';
                responsiveContainer.style.boxShadow = isDarkMode ? '0 5px 15px rgba(0, 0, 0, 0.5)' : '0 5px 15px rgba(0, 0, 0, 0.1)'; // تخفيف الظل

                const [categoriesSection, itemsSection, selectedItemsSection] = createResponsiveLayout();

                // الخصائص أصبحت تعين في دالة createResponsiveLayout

                responsiveContainer.appendChild(categoriesSection);
                responsiveContainer.appendChild(itemsSection);
                responsiveContainer.appendChild(selectedItemsSection);

                // تعديل أسلوب الجدول مباشرة
                setTimeout(() => {
                    const selectedItemsTable = document.querySelector('.selected-items table');
                    if (selectedItemsTable) {
                        selectedItemsTable.style.width = '100%';
                        selectedItemsTable.style.tableLayout = 'fixed';
                    }

                    // تثبيت موضع شريط الإجمالي
                    const totalAmount = document.getElementById('total_amount');
                    if (totalAmount) {
                        totalAmount.style.position = 'absolute';
                        totalAmount.style.bottom = '0';
                        totalAmount.style.left = '0';
                        totalAmount.style.right = '0';
                        totalAmount.style.zIndex = '10';
                    }

                    // إعداد وظائف البحث
                    setupSearchFunctionality();
                }, 100);

                // إخفاء الحاوية الأصلية وإضافة الحاوية المتجاوبة
                mainContainer.style.display = 'none';
                const itemCountBar = document.querySelector('.item-count-bar');
                if (itemCountBar) {
                    itemCountBar.style.display = 'none';
                }

                if (mainContainer && mainContainer.parentNode) {
                    mainContainer.parentNode.insertBefore(responsiveContainer, mainContainer);
                }

                // تحديث التخطيط المتجاوب ببيانات الجدول الأصلي
                populateCategories();

                // استرجاع الأصناف المحفوظة في العرض المتجاوب
                restoreSavedItemsResponsive();

                // نقل العناصر المختارة إلى القسم الجديد
                const selectedItems = document.querySelectorAll('#accountsTable tr.highlight');
                selectedItems.forEach(row => {
                    const itemId = row.dataset.itemId;
                    const itemName = row.dataset.itemName;

                    if (itemId && itemName) {
                        // جلب قيمة الكمية من الصف الأصلي
                        let quantity = 1;
                        const quantityInput = row.querySelector('.quantity-input');
                        if (quantityInput) {
                            quantity = parseFloat(quantityInput.value) || 1;
                        }

                        // إيجاد الصف في جدول الأصناف المختارة (إذا كان موجودًا بالفعل)
                        const selectedItemsTable = document.getElementById('selectedItemsTable');
                        const existingRow = selectedItemsTable ? Array.from(selectedItemsTable.querySelectorAll('tr')).find(
                            r => r.dataset.itemId === itemId
                        ) : null;

                        // لن نقوم بإضافة العناصر مرة أخرى لأننا استرجعناها من window.addedItems
                    }
                });

                // تحديث إجمالي الفاتورة
                updateInvoiceTotal();
            }
        } else { // للشاشات الصغيرة
            console.log('Small screen detected');

            const responsiveContainer = document.querySelector('.responsive-container');
            if (responsiveContainer) {
                console.log('Switching back to original layout');

                // إزالة ملف invoice.css عند العودة للتصميم العادي
                unloadInvoiceCss();

                responsiveContainer.remove();
                if (mainContainer) {
                    mainContainer.style.display = '';
                }
                const itemCountBar = document.querySelector('.item-count-bar');
                if (itemCountBar) {
                    itemCountBar.style.display = '';
                }

                // إظهار شريط البحث العلوي وحاوية عدد الأصناف مرة أخرى
                if (searchBar) {
                    searchBar.style.display = '';
                }

                // إظهار حاوية عدد الأصناف المضافة مرة أخرى
                if (itemCountContainer) {
                    itemCountContainer.style.display = '';
                }
            }
        }
    }

    // دالة جديدة لتحديث نمط الهيدر
    function updateHeaderStyle() {
        const header = document.querySelector('.header');
        if (header) {
            // تحقق مما إذا كان الوضع الداكن مفعلًا
            const isDarkMode = document.body.classList.contains('dark-mode');

            // تعيين الألوان المناسبة
            header.style.backgroundColor = isDarkMode ? '#1a2332' : '#f0f0f0';
            header.style.color = isDarkMode ? '#e1e8f0' : '#333333';

            // تغيير لون العناصر الفرعية داخل الهيدر
            const storeName = header.querySelector('.store-name');
            if (storeName) {
                storeName.style.color = isDarkMode ? '#e1e8f0' : '#333333';
            }

            // تحديث مظهر العناصر الأخرى داخل الهيدر
            const formElements = header.querySelectorAll('select, button, input');
            formElements.forEach(element => {
                element.style.borderColor = isDarkMode ? '#2d3748' : '#ddd';
                if (element.classList.contains('form-select')) {
                    element.style.backgroundColor = isDarkMode ? '#242b3d' : '#fff';
                    element.style.color = isDarkMode ? '#e1e8f0' : '#333';
                }
            });

            // إزالة الظل إذا لزم الأمر
            header.style.boxShadow = isDarkMode ? '0 4px 8px rgba(0, 0, 0, 0.3)' : '0 4px 8px rgba(0, 0, 0, 0.1)';
        }
    }

    // دالة لعرض إشعار بعد قراءة الباركود
    function showBarcodeNotification(message, type = 'success') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = 'barcode-notification';
        notification.textContent = message;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '10px 20px';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '9999';
        notification.style.transition = 'opacity 0.5s';
        notification.style.opacity = '0';

        // تعيين لون الإشعار حسب النوع
        if (type === 'success') {
            notification.style.backgroundColor = '#4CAF50'; // أخضر
            notification.style.color = 'white';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#F44336'; // أحمر
            notification.style.color = 'white';
        }

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إظهار الإشعار بتأثير تلاشي
        setTimeout(() => { notification.style.opacity = '1'; }, 10);

        // إخفاء الإشعار بعد ثانيتين
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 2000);
    }

    // دالة لتمييز الصنف المضاف
    function highlightAddedItem(itemId) {
        // البحث عن الصف في كلا العرضين (المستجيب والعادي)
        let foundRow = null;

        // البحث في العرض المستجيب
        const itemsList = document.getElementById('itemsList');
        if (itemsList) {
            foundRow = Array.from(itemsList.querySelectorAll('tr')).find(
                row => row.dataset.itemId === itemId
            );
        }

        // إذا لم يتم العثور عليه في العرض المستجيب، ابحث في العرض العادي
        if (!foundRow) {
            const accountsTable = document.getElementById('accountsTable');
            if (accountsTable) {
                foundRow = Array.from(accountsTable.querySelectorAll('tr')).find(
                    row => row.dataset.itemId === itemId
                );
            }
        }

        // إذا تم العثور على الصف، أضف تأثير وميض
        if (foundRow) {
            const originalBg = foundRow.style.backgroundColor;
            foundRow.style.backgroundColor = '#FFEB3B'; // أصفر

            // إعادة اللون الأصلي بعد ثانية واحدة
            setTimeout(() => {
                foundRow.style.backgroundColor = originalBg;
            }, 1000);
        }
    }

    // فحص أولي
    setTimeout(handleScreenSize, 500);

    // الاستماع لتغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleScreenSize, 250); // مهلة لتجنب التنفيذ المتكرر
    });

    // إضافة معالج أحداث للكميات لتحديث الإجمالي
    function addQuantityChangeListener(quantityInput) {
        quantityInput.addEventListener('input', function() {
            updateInvoiceTotal();
        });
    }

    // تصدير الدوال للاستخدام الخارجي
    window.updateItemCount = updateItemCount;
    window.updateInvoiceTotal = updateInvoiceTotal;
    window.restoreSavedItemsResponsive = restoreSavedItemsResponsive;
});