<?php
require 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

header('Content-Type: application/json');

if (isset($_GET['item_id'])) {
    $encrypted_item_id = $_GET['item_id'];
    $item_id = decrypt($encrypted_item_id, $key);

    $sql = "SELECT it.transaction_type, it.quantity, it.transaction_date, p.invoice_id, p.time AS purchase_date
            FROM itemtransactions it
            LEFT JOIN purchases p ON it.transaction_id_ref = p.purchases_id
            WHERE it.item_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $item_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $transactions = [];
    while ($row = $result->fetch_assoc()) {
        $transactions[] = [
            'transaction_type' => $row['transaction_type'],
            'quantity' => $row['quantity'],
            'transaction_date' => $row['transaction_date'],
            'purchase_details' => "Invoice ID: " . $row['invoice_id'] . ", Date: " . $row['purchase_date']
        ];
    }
    $stmt->close();

    echo json_encode(['success' => true, 'transactions' => $transactions]);
} else {
    echo json_encode(['success' => false, 'message' => 'Item ID is missing.']);
}
?>
