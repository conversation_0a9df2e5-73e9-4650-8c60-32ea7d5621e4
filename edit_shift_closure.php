<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $closure_id = $_POST['closure_id'];
    $shift_amount = $_POST['shift_amount'];
    $shift_type = $_POST['shift_type'];
    $notes = $_POST['notes'];
    $purchases = $_POST['purchases'];

    // Fetch the current shift closure details for comparison
    $fetch_stmt = $conn->prepare("SELECT shift_amount, shift_type, notes, purchases FROM shift_closures WHERE closure_id = ?");
    $fetch_stmt->bind_param("i", $closure_id);
    $fetch_stmt->execute();
    $fetch_stmt->bind_result($current_shift_amount, $current_shift_type, $current_notes, $current_purchases);
    $fetch_stmt->fetch();
    $fetch_stmt->close();

    $stmt = $conn->prepare("UPDATE shift_closures SET shift_amount = ?, shift_type = ?, notes = ?, purchases = ? WHERE closure_id = ?");
    $stmt->bind_param("dssdi", $shift_amount, $shift_type, $notes, $purchases, $closure_id);

    if ($stmt->execute()) {
        // Log the shift closure edit action
        $key = getenv('ENCRYPTION_KEY');
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Prepare a description of what was changed
        $changes = [];
        if ($shift_amount != $current_shift_amount) $changes[] = "المبلغ من $current_shift_amount إلى $shift_amount";
        if ($shift_type !== $current_shift_type) $changes[] = "نوع الوردية من $current_shift_type إلى $shift_type";
        if ($notes !== $current_notes) $changes[] = "الملاحظات من '$current_notes' إلى '$notes'";
        if ($purchases != $current_purchases) $changes[] = "المشتريات من $current_purchases إلى $purchases";

        $description = "تم تعديل تفاصيل الوردية رقم $closure_id: " . implode(", ", $changes);

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'update', 'shift_closures', ?, ?)";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $closure_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true, 'message' => 'تم تعديل تفاصيل الوردية بنجاح']);
    } else {
        echo json_encode(['success' => false, 'error' => $stmt->error]);
    }

    $stmt->close();
    $conn->close();
}
?>
