<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

$account_id = decrypt($_GET['account_id'], $key);
$order_dates = explode(',', $_GET['order_dates']);

$placeholders = implode(',', array_fill(0, count($order_dates), '?'));
$params = array_merge([$account_id], $order_dates);
$types = 'i' . str_repeat('s', count($order_dates)); // Correctly define the types as a string

$sql = "SELECT s.sale_id, i.name, s.quantity, s.returned, s.price, 
               (s.price * s.quantity) AS total_amount, 
               COALESCE(s.collected, 0) AS collected, 
               s.status, DATE(s.time) AS order_date
        FROM sales s
        JOIN items i ON s.item_id = i.item_id
        WHERE s.account_id = ? AND DATE(s.time) IN ($placeholders)";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, $account_id, ...$order_dates); // Use the correct types string
$stmt->execute();
$result = $stmt->get_result();

$order_details = [];
while ($row = $result->fetch_assoc()) {
    $order_details[] = $row;
}

$stmt->close();
$conn->close();

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلبات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
</head>
<body>
    <div class="container">
        <h2>تفاصيل الطلبات</h2>
        <table>
            <thead>
                <tr>
                    <th>معرف البيع</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>مرتجع</th>
                    <th>سعر القطعة</th>
                    <th>مجموع السعر</th>
                    <th>المدفوع</th>
                    <th>الباقي</th>
                    <th>الحالة</th>
                    <th>تاريخ الطلب</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($order_details as $item): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($item['sale_id']); ?></td>
                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                        <td><?php echo htmlspecialchars($item['quantity']); ?></td>
                        <td><?php echo htmlspecialchars($item['returned']); ?></td>
                        <td><?php echo htmlspecialchars($item['price']); ?></td>
                        <td><?php echo htmlspecialchars($item['total_amount']); ?></td>
                        <td><?php echo htmlspecialchars($item['collected']); ?></td>
                        <td><?php echo htmlspecialchars($item['total_amount'] - $item['collected']); ?></td>
                        <td><?php echo htmlspecialchars($item['status']); ?></td>
                        <td><?php echo htmlspecialchars($item['order_date']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
