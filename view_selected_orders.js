function viewSelectedOrders() {
    const selected = document.querySelectorAll('.order-checkbox:checked');
    if (selected.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'اختر طلب واحد على الأقل'
        });
        return;
    }

    const plainAccountId = selected[0].getAttribute('data-plain-account-id');
    const encryptedAccountId = selected[0].getAttribute('data-encrypted-account-id');
    let orderDates = [];
    let valid = true;

    selected.forEach(function(chk) {
        if (chk.getAttribute('data-plain-account-id') !== plainAccountId) {
            valid = false;
        }
        orderDates.push(chk.getAttribute('data-order-date'));
    });

    if (!valid) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يرجى اختيار طلبات من نفس الحساب'
        });
        return;
    }

    // Fetch and display order details in the existing modal
    fetch(`get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_dates=${encodeURIComponent(orderDates.join(','))}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                console.error('Error fetching order details: No data received');
                return;
            }

            const orderDetailsBody = document.getElementById('order-details-body');
            orderDetailsBody.innerHTML = ''; // Clear existing content
            orderDetailsBody.dataset.orderDate = orderDates.join(','); // Store selected dates
            orderDetailsBody.dataset.accountId = encryptedAccountId; // Store account ID

            data.forEach(item => {
                const status = item.status === 'pending' ?
                    '<span class="status-frame pending">انتظار</span>' :
                    item.status === 'delayed' ?
                    '<span class="status-frame delayed">مؤجل</span>' :
                    '<span class="status-frame confirmed">مؤكد</span>';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="order-detail-checkbox" data-sale-id="${item.sale_id}"></td>
                    <td class="sale_id">${item.sale_id}</td>
                    <td class="name">${item.name}</td>
                    <td class="quantity" contenteditable="true" onkeypress="handleQuantityKeyPress(event, this, ${item.sale_id}, '${item.status}', ${item.item_id})">${item.quantity}</td>
                    <td class="price">${item.price}</td>
                    <td class="total_amount">${item.total_amount}</td>
                    <td>${status}</td>
                    <td>
                        <i class="fas fa-trash-alt" style="cursor:pointer; font-size: 18px; color: #dc3545;" onclick="deleteOrderItem(${item.sale_id}, this)"></i>
                    </td>
                `;
                orderDetailsBody.appendChild(row);
            });

            // Open the modal using the active class (consistent with sales.php)
            document.getElementById('orderDetailsModal').classList.add('active');
        })
        .catch(error => console.error('Error fetching order details:', error));
}