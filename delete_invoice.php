<?php
include 'db_connection.php';
include 'encryption_functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $encrypted_invoice_id = $_POST['encrypted_invoice_id'];
    $key = getenv('ENCRYPTION_KEY');
    $invoice_id = decrypt($encrypted_invoice_id, $key);

    if ($invoice_id) {
        // Check the invoice status
        $sql = "SELECT status, total_amount FROM purchase_invoices WHERE invoice_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();
        $stmt->bind_result($status, $total_amount);
        $stmt->fetch();
        $stmt->close();

        // If the invoice is not "Pending," update item quantities
        if ($status !== 'Pending') {
            // Fetch purchased items and quantities
            $sql = "SELECT item_id, quantity FROM purchases WHERE invoice_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $invoice_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $purchasedItems = $result->fetch_all(MYSQLI_ASSOC);
            $stmt->close();

            // Deduct quantities from items table
            foreach ($purchasedItems as $item) {
                $sql = "UPDATE items SET quantity = quantity - ? WHERE item_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ii", $item['quantity'], $item['item_id']);
                $stmt->execute();
                $stmt->close();
            }
        }

        // Delete related records in purchases table
        $sql = "DELETE FROM purchases WHERE invoice_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();
        $stmt->close();

        // Delete the invoice
        $sql = "DELETE FROM purchase_invoices WHERE invoice_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();

        if ($stmt->affected_rows > 0) {
            // Log the invoice deletion action
            session_start();
            $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                        VALUES (?, 'delete', 'purchase_invoices', ?, ?)";
            $description = "تم حذف الفاتورة رقم $invoice_id بقيمة $total_amount";
            $log_stmt = $conn->prepare($log_sql);
            $log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
            $log_stmt->execute();
            $log_stmt->close();

            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false]);
        }

        $stmt->close();
    } else {
        echo json_encode(['success' => false]);
    }
}

$conn->close();
?>
