<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الباركود المخصص</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-title {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>اختبار إصلاح الباركود المخصص</h1>

        <div class="test-section">
            <div class="test-title">1. اختبار دالة processCustomBarcode</div>
            <input type="text" class="test-input" id="barcodeInput" placeholder="أدخل الباركود المخصص (مثال: 2000001001752)" value="2000001001752">
            <button onclick="testProcessCustomBarcode()">اختبار</button>
            <div id="processResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. اختبار حساب الكمية والإجمالي</div>
            <input type="number" class="test-input" id="quantityInput" placeholder="الكمية" value="0.175" step="0.001">
            <input type="number" class="test-input" id="priceInput" placeholder="السعر" value="280">
            <button onclick="testCalculation()">حساب الإجمالي</button>
            <div id="calcResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. اختبار أمثلة مختلفة</div>
            <button onclick="testExample('2000001001752', 'جبنة تركي', 280)">مثال 1: جبنة تركي (175g)</button>
            <button onclick="testExample('2000002002308', 'لحم بقري', 450)">مثال 2: لحم بقري (230g)</button>
            <button onclick="testExample('2000003007501', 'دجاج', 180)">مثال 3: دجاج (750g)</button>
            <div id="exampleResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // نسخة من دالة processCustomBarcode للاختبار
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            // استخراج باركود الصنف (6 أرقام بعد الرقم 2)
            const itemBarcode = barcode.substring(1, 7);

            // استخراج الوزن بالجرام (4 أرقام من الموضع 8 إلى 11، الرقم 13 ليس جزء من الوزن)
            const weightInGrams = parseInt(barcode.substring(8, 12));
            const quantityInKg = weightInGrams / 1000; // تحويل إلى كيلوجرام

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams
            };
        }

        function testProcessCustomBarcode() {
            const barcode = document.getElementById('barcodeInput').value;
            const result = processCustomBarcode(barcode);
            const resultDiv = document.getElementById('processResult');

            if (result) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>نتيجة ناجحة:</strong><br>
                    الباركود الأصلي: ${barcode}<br>
                    باركود الصنف: ${result.itemBarcode}<br>
                    الوزن بالجرام: ${result.weightInGrams}<br>
                    الكمية بالكيلو: ${result.quantity.toFixed(3)}
                `;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>خطأ:</strong> الباركود غير صحيح أو لا يبدأ بـ 2 أو طوله ليس 13 رقم`;
            }
        }

        function testCalculation() {
            const quantity = parseFloat(document.getElementById('quantityInput').value);
            const price = parseFloat(document.getElementById('priceInput').value);
            const total = quantity * price;

            const resultDiv = document.getElementById('calcResult');
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <strong>حساب الإجمالي:</strong><br>
                الكمية: ${quantity.toFixed(3)} كيلو<br>
                السعر: ${price.toFixed(2)} جنيه/كيلو<br>
                الإجمالي: ${total.toFixed(2)} جنيه
            `;
        }

        function testExample(barcode, itemName, price) {
            const barcodeData = processCustomBarcode(barcode);
            const resultDiv = document.getElementById('exampleResult');

            if (barcodeData) {
                const total = barcodeData.quantity * price;
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>مثال: ${itemName}</strong><br>
                    الباركود: ${barcode}<br>
                    باركود الصنف: ${barcodeData.itemBarcode}<br>
                    الوزن: ${barcodeData.weightInGrams} جرام<br>
                    الكمية: ${barcodeData.quantity.toFixed(3)} كيلو<br>
                    السعر: ${price} جنيه/كيلو<br>
                    <strong>الإجمالي المتوقع: ${total.toFixed(2)} جنيه</strong>
                `;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `خطأ في معالجة الباركود: ${barcode}`;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testProcessCustomBarcode();
            testCalculation();
        };
    </script>
</body>

</html>