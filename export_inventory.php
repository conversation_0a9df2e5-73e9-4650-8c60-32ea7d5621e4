<?php
require_once 'db_connection.php';
require_once 'security.php';
require_once 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('inventory', 'access');

$key = getenv('ENCRYPTION_KEY');

// تحديد نوع التصدير
$export_type = $_GET['type'] ?? 'excel';
$store_id = null;

// فحص إذا كان هناك فرع محدد
if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);
}

// إعداد الاستعلام
$where_clause = '';
$params = [];
$types = '';

if ($store_id) {
    $where_clause = "WHERE monthly_inventory.store_id = ?";
    $params[] = $store_id;
    $types .= 'i';
}

// استعلام البيانات
$sql = "SELECT 
            monthly_inventory.inventory_id,
            monthly_inventory.inventory_date,
            monthly_inventory.created_at,
            monthly_inventory.status,
            stores.name AS store_name
        FROM monthly_inventory 
        JOIN stores ON monthly_inventory.store_id = stores.store_id 
        $where_clause
        ORDER BY monthly_inventory.created_at DESC";

$stmt = $conn->prepare($sql);

if ($where_clause) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// جمع البيانات
$data = [];
while ($row = $result->fetch_assoc()) {
    $data[] = $row;
}

$stmt->close();

// تحديد اسم الملف
$filename = 'inventory_report_' . date('Y-m-d_H-i-s');
if ($store_id) {
    // الحصول على اسم الفرع
    $store_stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $store_stmt->bind_param("i", $store_id);
    $store_stmt->execute();
    $store_stmt->bind_result($store_name);
    $store_stmt->fetch();
    $store_stmt->close();
    
    $filename = 'inventory_' . preg_replace('/[^a-zA-Z0-9_\-]/', '_', $store_name) . '_' . date('Y-m-d_H-i-s');
}

if ($export_type === 'excel') {
    // تصدير Excel
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');
    
    // إعداد الترميز
    echo "\xEF\xBB\xBF"; // UTF-8 BOM
    
    ?>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }
            th, td { border: 1px solid #000; padding: 8px; text-align: center; }
            th { background-color: #4472C4; color: white; font-weight: bold; }
            .header { background-color: #D9E2F3; font-weight: bold; text-align: center; padding: 15px; }
            .status-pending { background-color: #FFF2CC; color: #7F6000; }
            .status-completed { background-color: #D5E8D4; color: #0F5132; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>تقرير الجرد الشهري</h2>
            <p>تاريخ التصدير: <?php echo date('Y-m-d H:i:s'); ?></p>
            <?php if ($store_id): ?>
                <p>الفرع: <?php echo htmlspecialchars($store_name ?? 'غير محدد'); ?></p>
            <?php endif; ?>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>رقم الجرد</th>
                    <th>اسم الفرع</th>
                    <th>تاريخ الجرد</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($data)): ?>
                    <tr>
                        <td colspan="5">لا توجد بيانات للعرض</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($data as $row): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($row['inventory_id']); ?></td>
                            <td><?php echo htmlspecialchars($row['store_name']); ?></td>
                            <td><?php echo htmlspecialchars($row['inventory_date']); ?></td>
                            <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                            <td class="<?php echo $row['status'] === 'pending' ? 'status-pending' : 'status-completed'; ?>">
                                <?php echo $row['status'] === 'pending' ? 'قيد التنفيذ' : 'مكتمل'; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <div style="margin-top: 20px; padding: 10px; background-color: #F8F9FA; border: 1px solid #DEE2E6;">
            <h3>إحصائيات التقرير:</h3>
            <p><strong>إجمالي سجلات الجرد:</strong> <?php echo count($data); ?></p>
            <p><strong>الجرد قيد التنفيذ:</strong> <?php echo count(array_filter($data, function($item) { return $item['status'] === 'pending'; })); ?></p>
            <p><strong>الجرد المكتمل:</strong> <?php echo count(array_filter($data, function($item) { return $item['status'] === 'confirmed'; })); ?></p>
        </div>
    </body>
    </html>
    <?php
    
} elseif ($export_type === 'csv') {
    // تصدير CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: max-age=0');
    
    // إعداد الترميز
    echo "\xEF\xBB\xBF"; // UTF-8 BOM
    
    $output = fopen('php://output', 'w');
    
    // كتابة العناوين
    fputcsv($output, [
        'رقم الجرد',
        'اسم الفرع',
        'تاريخ الجرد',
        'تاريخ الإنشاء',
        'الحالة'
    ]);
    
    // كتابة البيانات
    foreach ($data as $row) {
        fputcsv($output, [
            $row['inventory_id'],
            $row['store_name'],
            $row['inventory_date'],
            $row['created_at'],
            $row['status'] === 'pending' ? 'قيد التنفيذ' : 'مكتمل'
        ]);
    }
    
    fclose($output);
    
} elseif ($export_type === 'pdf') {
    // تصدير PDF (يتطلب مكتب�� PDF)
    echo "تصدير PDF غير متاح حالياً. يرجى استخدام Excel أو CSV.";
    
} else {
    // نوع تصدير غير مدعوم
    http_response_code(400);
    echo "نوع التصدير غير مدعوم";
}

$conn->close();
?>