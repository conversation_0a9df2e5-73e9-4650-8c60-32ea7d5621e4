<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الباركود المخصص - محدث</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-title {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .example {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>اختبار إصلاح الباركود المخصص - محدث</h1>
        <p>هذا الاختبار يتحقق من أن حساب الإجمالي يعمل بشكل صحيح مع الباركود المخصص</p>

        <div class="example">
            <strong>مثال الباركود الذي تم اختباره:</strong><br> الباركود: <code>2000001001752</code><br> باركود الصنف: <code>000001</code><br> الوزن: <code>01752</code> جرام = <code>1.752</code> كيلو<br> السعر: <code>280</code> جنيه/كيلو<br> الإجمالي
            المتوقع: <code>1.752 × 280 = 490.56</code> جنيه
        </div>

        <div class="test-section">
            <div class="test-title">اختبار معالجة الباركود المخصص</div>
            <input type="text" class="test-input" id="barcodeInput" placeholder="أدخل الباركود المخصص (مثال: 2000001001752)" maxlength="13">
            <button onclick="testCustomBarcode()">اختبار الباركود</button>
            <div id="barcodeResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار شامل للباركود المخصص</div>
            <input type="text" class="test-input" id="fullTestBarcode" placeholder="باركود مخصص (مثال: 2000001001752)" maxlength="13">
            <input type="number" class="test-input" id="fullTestPrice" placeholder="سعر الكيلو (مثال: 280)" step="0.01">
            <button onclick="testFullScenario()">اختبار شامل</button>
            <div id="fullTestResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // دالة معالجة الباركود المخصص المحدثة
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            // استخراج باركود الصنف (6 أرقام بعد الرقم 2)
            const itemBarcode = barcode.substring(1, 7);

            // استخراج الوزن بالجرام (5 أرقام من الموضع 7 إلى 11، الرقم 12 هو check digit)
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        function testCustomBarcode() {
            const barcode = document.getElementById('barcodeInput').value;
            const resultDiv = document.getElementById('barcodeResult');

            if (!barcode) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال باركود للاختبار</div>';
                return;
            }

            const result = processCustomBarcode(barcode);

            if (result) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>نجح الاختبار!</strong><br>
                        الباركود الأصلي: ${barcode}<br>
                        باركود الصنف: ${result.itemBarcode}<br>
                        الوزن بالجرام: ${result.weightInGrams}<br>
                        الكمية بالكيلو: ${result.quantity.toFixed(3)}<br>
                        باركود مخصص: ${result.isCustomBarcode ? 'نعم' : 'لا'}<br>
                        <br>
                        <strong>تفصيل الباركود:</strong><br>
                        الموضع 0: ${barcode[0]} (رقم البداية)<br>
                        المواضع 1-6: ${barcode.substring(1, 7)} (باركود الصنف)<br>
                        المواضع 7-11: ${barcode.substring(7, 12)} (الوزن بالجرام)<br>
                        الموضع 12: ${barcode[12]} (check digit)
                    </div>
                `;
            } else {
                resultDiv.innerHTML = '<div class="error">فشل في معالجة الباركود - تأكد من أن الباركود يبدأ بـ 2 ويحتوي على 13 رقم</div>';
            }
        }

        function testFullScenario() {
            const barcode = document.getElementById('fullTestBarcode').value;
            const price = parseFloat(document.getElementById('fullTestPrice').value);
            const resultDiv = document.getElementById('fullTestResult');

            if (!barcode || !price) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال الباركود والسعر</div>';
                return;
            }

            const barcodeData = processCustomBarcode(barcode);

            if (!barcodeData) {
                resultDiv.innerHTML = '<div class="error">باركود غير صحيح</div>';
                return;
            }

            const total = barcodeData.quantity * price;

            resultDiv.innerHTML = `
                <div class="success">
                    <strong>اختبار شامل ناجح!</strong><br>
                    الباركود الأصلي: ${barcode}<br>
                    باركود الصنف: ${barcodeData.itemBarcode}<br>
                    الوزن: ${barcodeData.weightInGrams} جرام<br>
                    الكمية: ${barcodeData.quantity.toFixed(3)} كيلو<br>
                    السعر/كيلو: ${price.toFixed(2)} جنيه<br>
                    <strong>الإجمالي المحسوب: ${total.toFixed(2)} جنيه</strong><br>
                    <em>هذا هو المبلغ الذي يجب أن يظهر في الفاتورة</em><br>
                    <br>
                    <strong>المعادلة:</strong> ${barcodeData.quantity.toFixed(3)} × ${price.toFixed(2)} = ${total.toFixed(2)}
                </div>
            `;
        }

        // إضافة مثال تلقائي
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('barcodeInput').value = '2000001001752';
            document.getElementById('fullTestBarcode').value = '2000001001752';
            document.getElementById('fullTestPrice').value = '280';
        });
    </script>
</body>

</html>