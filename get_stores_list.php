<?php
require_once 'security.php';

// التحقق من صلاحية الوصول لوحة التحكم
checkPagePermission('dashboard', 'view');

include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');

// الحصول على معرف المستخدم الحالي
$current_user_id = decrypt($_SESSION['account_id'], $key);

// Get current store ID if provided
$current_store_id = null;
if (isset($_GET['current_store_id']) && !empty($_GET['current_store_id'])) {
    $current_store_id = decrypt($_GET['current_store_id'], $key);
}

// بناء استعلام المخازن حسب الصلاحيات (نفس المنطق المستخدم في stores.php)
$stores_sql = "";
$stores_params = [];
$stores_types = "";

if (hasPermission('dashboard', 'manage_all_stores')) {
    // المستخدم يمكنه رؤية جميع المخازن
    $stores_sql = "SELECT store_id, name FROM stores ORDER BY name";
} elseif (hasPermission('dashboard', 'manage_specific_stores')) {
    // المستخدم يمكنه رؤية مخازن محددة فقط
    $stores_sql = "SELECT s.store_id, s.name FROM stores s 
                   INNER JOIN user_stores us ON s.store_id = us.store_id 
                   WHERE us.user_id = ? AND us.granted = TRUE 
                   ORDER BY s.name";
    $stores_params[] = $current_user_id;
    $stores_types = "i";
} else {
    // المستخدم ليس لديه صلاحية رؤية أي مخازن
    $stores_sql = "SELECT store_id, name FROM stores WHERE 1=0"; // استعلام فارغ
}

// تنفيذ الاستعلام
$stores = [];
if (!empty($stores_params)) {
    $stmt = $conn->prepare($stores_sql);
    $stmt->bind_param($stores_types, ...$stores_params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($stores_sql);
}

if ($result && $result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $encrypted_id = encrypt($row['store_id'], $key);
        $stores[] = [
            'id' => $encrypted_id,
            'name' => $row['name'],
            'current' => ($current_store_id !== null && $row['store_id'] == $current_store_id)
        ];
    }
}

// إغلاق الاستعلام المحضر إذا كان موجوداً
if (isset($stmt)) {
    $stmt->close();
}

echo json_encode(['success' => true, 'stores' => $stores]);

$conn->close();
?>
