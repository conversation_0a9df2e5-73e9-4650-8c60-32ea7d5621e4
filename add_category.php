<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

$response = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['category_name'])) {
    $category_name = $_POST['category_name'];
    $store_id = decrypt($_POST['store_id'], $key);
    
    // Fetch the store name
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
    
    // تحويل store_id إلى عدد صحيح في حال كان ذلك مطلوباً
    $store_id = (int)$store_id;

    // تعديل جملة الإدخال لتناسب الجدول المُعرف والذي يحتوي فقط على (name, store_id)
    $stmt = $conn->prepare("INSERT INTO categories (name, store_id) VALUES (?, ?)");
    $stmt->bind_param("si", $category_name, $store_id);
    
    if ($stmt->execute()) {
        $category_id = $stmt->insert_id; // Get the ID of the newly created category

        // Log the category addition action
        session_start();
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'add', 'categories', ?, ?)";
        $description = "تم إضافة تصنيف جديد باسم $category_name إلى الفرع $store_name";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $category_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        $response['success'] = true;
        $response['message'] = 'تم إضافة التصنيف بنجاح.';
    } else {
        $response['success'] = false;
        $response['message'] = 'حدث خطأ أثناء إضافة التصنيف: ' . $stmt->error;
    }
    $stmt->close();
} else {
    $response['success'] = false;
    $response['message'] = 'بيانات غير صحيحة.';
}

echo json_encode($response);
$conn->close();
?>
