<?php
include 'db_connection.php';
include 'encryption_functions.php';

// Read data from $_POST
$store_id = $_POST['store_id'] ?? null;
$account_id = $_POST['account_id'] ?? null;
$items_json = $_POST['items'] ?? '[]';
$items = json_decode($items_json, true);
$image_paths = $_POST['images'] ?? []; // Receive image paths directly

if (!$store_id || !$account_id || empty($items)) {
    http_response_code(400);
    echo json_encode(['error' => 'Incomplete data']);
    exit();
}

// Calculate total amount
$total_amount = 0;
foreach ($items as $item) {
    $stmt = $conn->prepare("SELECT cost FROM items WHERE item_id = ?");
    $stmt->bind_param("i", $item['id']);
    $stmt->execute();
    $cost = $stmt->get_result()->fetch_assoc()['cost'] ?? 0;
    $total_amount += $cost * $item['quantity'];
}

// Insert into purchase_invoices
$stmt = $conn->prepare("INSERT INTO purchase_invoices (store_id, total_amount, account_id) VALUES (?, ?, ?)");
$stmt->bind_param("idi", $store_id, $total_amount, $account_id);
$stmt->execute();
$invoice_id = $stmt->insert_id;

// Insert into purchases
foreach ($items as $item) {
    $stmt = $conn->prepare("INSERT INTO purchases (store_id, invoice_id, item_id, quantity) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("iiii", $store_id, $invoice_id, $item['id'], $item['quantity']);
    $stmt->execute();
}

// Handle image paths
foreach ($image_paths as $path) {
    $stmt = $conn->prepare("INSERT INTO invoice_images (purchase_invoice_id, img_path) VALUES (?, ?)");
    $stmt->bind_param("is", $invoice_id, $path);
    $stmt->execute();
}

// Fetch the store name
$query = "SELECT name FROM stores WHERE store_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$store = $result->fetch_assoc();
$store_name = $store['name'] ?? 'غير معروف';
$stmt->close();

// Log the invoice confirmation action
$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
            VALUES (?, 'add', 'purchase_invoices', ?, ?)";
$description = "تم إضافة فاتورة جديدة برقم $invoice_id بقيمة $total_amount إلى الفرع $store_name";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
$log_stmt->execute();
$log_stmt->close();

// Delete the JSON file after confirming the invoice
$jsonFilePath = __DIR__ . "/saved_invoices/account_{$account_id}.json";
if (file_exists($jsonFilePath)) {
    unlink($jsonFilePath);
}

header('Content-Type: application/json');
echo json_encode(['success' => true]);
?>
