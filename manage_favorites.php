<?php
require_once 'auth.php';
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['account_id']) || !isset($input['item_id']) || !isset($input['action'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit();
    }
    
    $encrypted_account_id = $input['account_id'];
    $item_id = intval($input['item_id']);
    $action = $input['action']; // 'add' أو 'remove'
    
    // فك تشفير معرف الحساب
    $account_id = decrypt($encrypted_account_id, $key);
    
    if ($account_id === false) {
        echo json_encode(['success' => false, 'message' => 'فشل في فك تشفير معرف الحساب']);
        exit();
    }
    
    try {
        if ($action === 'add') {
            // إضافة إلى المفضلة
            $stmt = $conn->prepare("INSERT IGNORE INTO user_favorites (account_id, item_id) VALUES (?, ?)");
            $stmt->bind_param("ii", $account_id, $item_id);
            $result = $stmt->execute();
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة الصنف إلى المفضلة']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في إضافة الصنف إلى المفضلة']);
            }
            
        } elseif ($action === 'remove') {
            // إزالة من المفضلة
            $stmt = $conn->prepare("DELETE FROM user_favorites WHERE account_id = ? AND item_id = ?");
            $stmt->bind_param("ii", $account_id, $item_id);
            $result = $stmt->execute();
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إزالة الصنف من المفضلة']);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في إزالة الصنف من المفضلة']);
            }
            
        } else {
            echo json_encode(['success' => false, 'message' => 'عملية غير صحيحة']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // جلب قائمة المفضلة للمستخدم
    if (!isset($_GET['account_id'])) {
        echo json_encode(['success' => false, 'message' => 'معرف الحساب مطلوب']);
        exit();
    }
    
    $encrypted_account_id = $_GET['account_id'];
    $account_id = decrypt($encrypted_account_id, $key);
    
    if ($account_id === false) {
        echo json_encode(['success' => false, 'message' => 'فشل في فك تشفير معرف الحساب']);
        exit();
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT uf.item_id, i.name, i.price, 
                   (SELECT COUNT(*) FROM item_images WHERE item_id = i.item_id) as image_count,
                   c.name as category_name
            FROM user_favorites uf
            JOIN items i ON uf.item_id = i.item_id
            JOIN categories c ON i.category_id = c.category_id
            WHERE uf.account_id = ? AND i.status = 'active'
            ORDER BY uf.created_at DESC
        ");
        $stmt->bind_param("i", $account_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $favorites = [];
        while ($row = $result->fetch_assoc()) {
            $favorites[] = $row;
        }
        
        echo json_encode(['success' => true, 'favorites' => $favorites]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
    }
}
?>