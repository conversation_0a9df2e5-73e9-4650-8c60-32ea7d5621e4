<?php
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_POST['account_id'] ?? null; // Use passed encrypted account_id

if (!$encrypted_account_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Account ID is required']);
    exit();
}

$account_id = decrypt($encrypted_account_id, $key); // Decrypt the account_id
if (!$account_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid Account ID']);
    exit();
}

// Decode the items JSON string
$itemsParam = $_POST['items'] ?? null;
$items = json_decode($itemsParam, true);

if (!$items || !is_array($items)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid items data']);
    exit();
}

// Define the path for the temporary JSON file
$tempInvoicesDir = __DIR__ . '/temp_purchase_invoices';
$jsonFilePath = $tempInvoicesDir . "/account_{$account_id}.json";

// Ensure the directory exists
if (!is_dir($tempInvoicesDir)) {
    if (!mkdir($tempInvoicesDir, 0777, true) && !is_dir($tempInvoicesDir)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create directory for temporary invoices']);
        exit();
    }
}

// Save the items to the JSON file
if (file_put_contents($jsonFilePath, json_encode($items)) === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save items to temporary file']);
    exit();
}

echo json_encode(['success' => true]);
?>
