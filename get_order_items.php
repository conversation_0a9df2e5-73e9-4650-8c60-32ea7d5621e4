<?php
include 'db_connection.php';

header('Content-Type: application/json');

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

// Read POST JSON payload
$input = json_decode(file_get_contents('php://input'), true);
if (!isset($input['sale_ids']) || !is_array($input['sale_ids']) || empty($input['sale_ids'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid sale_ids']);
    exit();
}

// Sanitize sale_ids as integers
$sale_ids = array_map('intval', $input['sale_ids']);
$inClause = implode(',', $sale_ids);

// Fetch order items for the provided sale_ids
$sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.total_amount, COALESCE(s.collected, 0) AS collected, s.status
        FROM sales s
        JOIN items i ON s.item_id = i.item_id
        WHERE s.sale_id IN ($inClause)";
$result = $conn->query($sql);

$order_items = [];
while ($row = $result->fetch_assoc()) {
    $order_items[] = $row;
}

$conn->close();
echo json_encode($order_items);
?>
