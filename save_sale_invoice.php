<?php
include 'db_connection.php';
include 'encryption_functions.php';

// تحديد مفتاح التشفير
$key = getenv('ENCRYPTION_KEY');

// التحقق من طلب POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // الحصول على البيانات المرسلة (مشفرة)
    $encrypted_store_id = isset($_POST['store_id']) ? $_POST['store_id'] : null;
    $encrypted_account_id = isset($_POST['account_id']) ? $_POST['account_id'] : null;
    $encrypted_account_buyer_id = isset($_POST['account_buyer_id']) ? $_POST['account_buyer_id'] : null;
    
    // فك تشفير المعرفات
    $store_id = $encrypted_store_id ? decrypt($encrypted_store_id, $key) : null;
    $account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;
    $account_buyer_id = $encrypted_account_buyer_id ? decrypt($encrypted_account_buyer_id, $key) : null;
    
    // التحقق من نجاح فك التشفير
    if ($store_id === false || $account_id === false || $account_buyer_id === false) {
        echo json_encode(['success' => false, 'message' => 'فشل في فك تشفير البيانات']);
        exit();
    }
    
    $itemsJson = isset($_POST['items']) ? $_POST['items'] : '[]';
    $items = json_decode($itemsJson, true);
    
    // التحقق من وجود الصور المرفقة
    $images = isset($_POST['images']) ? $_POST['images'] : [];
    if (is_string($images)) {
        $images = json_decode($images, true);
    }
    
    // التحقق من البيانات المطلوبة
    if (!$store_id || !$account_id || !$account_buyer_id || empty($items)) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير كاملة']);
        exit();
    }
    
    try {
        // بدء المعاملة
        $conn->begin_transaction();
        
        // تحديد وقت الفاتورة الحالي
        $current_time = date('Y-m-d H:i:s');
        $status = "confirmed";
        $total_amount = 0;
        
        // حساب إجمالي الفاتورة
        foreach ($items as $item) {
            $price = floatval($item['price']);
            $quantity = floatval($item['quantity']);
            $total_amount += ($price * $quantity);
        }

        // التحقق من نوع الفاتورة
        $invoice_type = isset($_POST['invoice_type']) ? $_POST['invoice_type'] : 'sale';
        
        if ($invoice_type === 'customer_return') {
            $return_ids = [];
            foreach ($items as &$item) { // Use reference to update the item array
                $item_id = $item['id'];
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);

                // Defensive check: If price is zero, fetch it from the database
                if ($price == 0) {
                    $price_stmt = $conn->prepare("SELECT price FROM items WHERE item_id = ?");
                    $price_stmt->bind_param("i", $item_id);
                    $price_stmt->execute();
                    $price_result = $price_stmt->get_result();
                    if ($price_row = $price_result->fetch_assoc()) {
                        $price = floatval($price_row['price']);
                        $item['price'] = $price; // Update the price in the array
                    }
                    $price_stmt->close();
                }
                $refund_amount = $price * $quantity;
                $notes = isset($item['notes']) ? $item['notes'] : '';
                $discount = isset($item['discount']) ? floatval($item['discount']) : 0;
                
                // إضافة المرتجع إلى جدول returns
                $stmt = $conn->prepare("INSERT INTO returns (item_id, quantity, refund_amount, time, notes, discount, account_id, account_buyer_id, store_id, total_refund) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("iddssdiiid", $item_id, $quantity, $refund_amount, $current_time, $notes, $discount, $account_id, $account_buyer_id, $store_id, $refund_amount);
                $stmt->execute();
                $return_id = $stmt->insert_id;
                $return_ids[] = $return_id;
                
                // زيادة الكمية في جدول items
                $stmt = $conn->prepare("UPDATE items SET quantity = quantity + ? WHERE item_id = ?");
                $stmt->bind_param("di", $quantity, $item_id);
                $stmt->execute();
            }
            
            // تسجيل الحدث في سجل النظام (لأول مرتجع فقط)
            $description = "تم إنشاء فاتورة مرتجع للعميل بقيمة " . $total_amount;
            if (!empty($return_ids)) {
                $stmt = $conn->prepare("INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) VALUES (?, 'create', 'returns', ?, ?)");
                $stmt->bind_param("iis", $account_id, $return_ids[0], $description);
                $stmt->execute();
            }
        } else {
            $sale_ids = [];
            foreach ($items as $item) {
                $item_id = $item['id'];
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);
                
                // فحص دفاعي: إذا كان السعر 0، جلبه من قاعدة البيانات
                if ($price == 0) {
                    $price_stmt = $conn->prepare("SELECT price FROM items WHERE item_id = ?");
                    $price_stmt->bind_param("i", $item_id);
                    $price_stmt->execute();
                    $price_result = $price_stmt->get_result();
                    if ($price_row = $price_result->fetch_assoc()) {
                        $price = floatval($price_row['price']);
                    }
                    $price_stmt->close();
                }
                
                // جلب تكلفة الصنف من جدول items
                $stmt = $conn->prepare("SELECT cost FROM items WHERE item_id = ?");
                $stmt->bind_param("i", $item_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $item_data = $result->fetch_assoc();
                $cost = $item_data ? floatval($item_data['cost']) : 0;
                
                // إضافة العنصر إلى جدول sales
                $stmt = $conn->prepare("INSERT INTO sales (item_id, quantity, cost, price, time, account_id, account_buyer_id, status, store_id, total_amount) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("idddsiisdd", $item_id, $quantity, $cost, $price, $current_time, $account_id, $account_buyer_id, $status, $store_id, $total_amount);
                $stmt->execute();
                $sale_id = $stmt->insert_id;
                $sale_ids[] = $sale_id;
                
                // تحديث الكمية في المخزون من خلال خصم الكمية المباعة
                $stmt = $conn->prepare("UPDATE items SET quantity = quantity - ? WHERE item_id = ?");
                $stmt->bind_param("di", $quantity, $item_id);
                $stmt->execute();
            }
            // تسجيل الحدث في سجل النظام (لأول عملية بيع فقط)
            $description = "تم إنشاء فاتورة مبيعات جديدة للعميل بقيمة " . $total_amount;
            if (!empty($sale_ids)) {
                $stmt = $conn->prepare("INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) VALUES (?, 'create', 'sales', ?, ?)");
                $stmt->bind_param("iis", $account_id, $sale_ids[0], $description);
                $stmt->execute();
            }
        }
        
        // إضافة الصور المرفقة (إذا وجدت)
        if (!empty($images)) {
            $table_name = ($invoice_type === 'customer_return') ? 'returns' : 'sales';
            $ids = ($invoice_type === 'customer_return') ? $return_ids : $sale_ids;
            foreach ($images as $idx => $image_path) {
                $invoice_id = isset($ids[$idx]) ? $ids[$idx] : (isset($ids[0]) ? $ids[0] : null);
                if ($invoice_id) {
                    $stmt = $conn->prepare("INSERT INTO invoice_images (invoice_id, image_path, invoice_type) VALUES (?, ?, ?)");
                    $stmt->bind_param("iss", $invoice_id, $image_path, $table_name);
                    $stmt->execute();
                }
            }
        }
        
        // حذف الملف المؤقت
        $jsonFilePath = __DIR__ . "/saved_invoices/account_{$account_id}.json";
        if (file_exists($jsonFilePath)) {
            unlink($jsonFilePath);
        }
        
        // تأكيد المعاملة
        $conn->commit();
        
        // إرجاع أول معرف كمعرف الفاتورة
        $invoice_id = null;
        if ($invoice_type === 'customer_return' && !empty($return_ids)) {
            $invoice_id = $return_ids[0];
        } elseif (!empty($sale_ids)) {
            $invoice_id = $sale_ids[0];
        }
        echo json_encode(['success' => true, 'message' => 'تم إنشاء الفاتورة بنجاح', 'invoice_id' => $invoice_id]);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage()]);
    }
    
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صالحة']);
}

$conn->close();
?> 