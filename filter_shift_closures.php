<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : null;
$search_query = isset($_GET['search_query']) ? '%' . $_GET['search_query'] . '%' : '%%';

if (!$encrypted_store_id || !$start_date || !$end_date) {
    echo json_encode([]);
    exit();
}

$store_id = decrypt($encrypted_store_id, $key);

// Normalize and validate input values
$status = isset($_GET['status']) ? ucfirst(strtolower(trim($_GET['status']))) : null;
$shift_type = isset($_GET['shift_type']) ? strtolower(trim($_GET['shift_type'])) : null;

// Ensure valid values for status and shift_type
$status = in_array($status, ['Active', 'Inactive']) ? $status : null;
$shift_type = in_array($shift_type, ['morning', 'night']) ? $shift_type : null;

// Debugging: Output distinct values for status and shift_type in the database
if (isset($_GET['debug']) && $_GET['debug'] === 'true') {
    $debug_query = "SELECT DISTINCT status, shift_type FROM shift_closures WHERE store_id = ?";
    $debug_stmt = $conn->prepare($debug_query);
    $debug_stmt->bind_param("i", $store_id);
    $debug_stmt->execute();
    $debug_result = $debug_stmt->get_result();
    while ($row = $debug_result->fetch_assoc()) {
        echo "status: {$row['status']}, shift_type: {$row['shift_type']}<br>";
    }
    $debug_stmt->close();
    exit();
}

$query = "SELECT sc.*, a.username FROM shift_closures sc 
          JOIN accounts a ON sc.account_id = a.account_id 
          WHERE sc.store_id = ? 
          AND sc.shift_date BETWEEN ? AND ? 
          AND (a.username LIKE ? OR sc.notes LIKE ?)";

$params = [$store_id, $start_date, $end_date, $search_query, $search_query];
$types = "issss";

if ($status) {
    $query .= " AND sc.status = ?";
    $params[] = $status;
    $types .= "s";
}

if ($shift_type) {
    $query .= " AND sc.shift_type = ?";
    $params[] = $shift_type;
    $types .= "s";
}

$query .= " ORDER BY sc.shift_date DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

$data = [];
while ($row = $result->fetch_assoc()) {
    $data[] = [
        "closure_id" => $row["closure_id"],
        "shift_type" => $row["shift_type"], // Ensure this field is included and consistent
        "shift_date" => $row["shift_date"],
        "status" => $row["status"],
        "shift_amount" => $row["shift_amount"],
        "purchases" => $row["purchases"], // Include purchases column
        "notes" => $row["notes"],
        "created_at" => $row["created_at"],
        "username" => $row["username"]
    ];
}

$stmt->close();
$conn->close();

echo json_encode($data);
?>
