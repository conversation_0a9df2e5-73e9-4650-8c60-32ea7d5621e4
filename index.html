<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCM</title>
</head>

<body>
    <h1>Firebase Push Notification</h1>
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getMessaging, getToken } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js";
        // TODO: Add SDKs for Firebase products that you want to use
        // https://firebase.google.com/docs/web/setup#available-libraries

        // Your web app's Firebase configuration
        // For Firebase JS SDK v7.20.0 and later, measurementId is optional
        const firebaseConfig = {
            apiKey: "AIzaSyBMKyjp4uC8BVsZHEC0A-q_aK3vD1-3SCo",
            authDomain: "name-87bd3.firebaseapp.com",
            projectId: "name-87bd3",
            storageBucket: "name-87bd3.firebasestorage.app",
            messagingSenderId: "26547736278",
            appId: "1:26547736278:web:4a5b33cb6a0301e7eb4377",
            measurementId: "G-J007Y96RQ6"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const messaging = getMessaging(app);

        navigator.serviceWorker.register("sw.js").then(registration => {
            getToken(messaging, {
                serviceWorkerRegistration: registration,
                vapidKey: 'BCLP8oS4TeC8BMPuq2E6OStQ4htzHFKNZBZoo1xvrWjal27sS0VjlUmatLKr3qKDN9SfttOtda_PQCl7OLftaHw'
            }).then((currentToken) => {
                if (currentToken) {
                    console.log("Token is: " + currentToken);
                    // Send the token to your server and update the UI if necessary
                    // ...
                } else {
                    // Show permission request UI
                    console.log('No registration token available. Request permission to generate one.');
                    // ...
                }
            }).catch((err) => {
                console.log('An error occurred while retrieving token. ', err);
                // ...
            });
        }).catch((err) => {
            console.log('Service Worker registration failed: ', err);
        });

    </script>
</body>

</html>