<?php
header('Content-Type: application/json');
include 'db_connection.php';

if (!isset($_GET['item_id']) || empty($_GET['item_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الصنف مفقود']);
    exit;
}

$item_id = intval($_GET['item_id']);

if ($item_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الصنف غير صحيح']);
    exit;
}

try {
    // Get item images
    $images_sql = "SELECT img_id, img_path FROM item_images WHERE item_id = ? ORDER BY img_id ASC";
    $images_stmt = $conn->prepare($images_sql);
    $images_stmt->bind_param("i", $item_id);
    $images_stmt->execute();
    $images_result = $images_stmt->get_result();
    
    $images = [];
    while ($image_row = $images_result->fetch_assoc()) {
        $images[] = [
            'img_id' => $image_row['img_id'],
            'img_path' => $image_row['img_path']
        ];
    }
    $images_stmt->close();
    
    echo json_encode([
        'success' => true,
        'images' => $images,
        'count' => count($images)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في قاعدة البيانات',
        'error' => $e->getMessage()
    ]);
}

$conn->close();
?>