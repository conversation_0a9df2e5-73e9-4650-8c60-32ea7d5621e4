<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب الكمية والإجمالي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        .quantity-input {
            width: 80px;
            padding: 5px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>اختبار حساب الكمية والإجمالي</h1>

        <div class="test-section">
            <h3>اختبار الباركود المخصص:</h3>
            <input type="text" id="barcodeInput" class="test-input" placeholder="أدخل الباركود المخصص (مثال: 2000001002308)" maxlength="13">
            <button onclick="testBarcode()">اختبار الباركود</button>
            <div id="barcodeResult"></div>
        </div>

        <div class="test-section">
            <h3>محاكاة إضافة الأصناف:</h3>
            <table id="itemsTable">
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>السعر (جنيه/كيلو)</th>
                        <th>الكمية (كيلو)</th>
                        <th>الإجمالي (جنيه)</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div><strong>إجمالي الفاتورة: <span id="totalAmount">0.00</span> جنيه</strong></div>
        </div>

        <div class="test-section">
            <h3>أمثلة للاختبار:</h3>
            <button onclick="testExample('2000001002308')">جبنة تركي - 0.230 كيلو</button>
            <button onclick="testExample('2000002001759')">لحم بقري - 0.175 كيلو</button>
            <button onclick="testExample('2000003007501')">دجاج - 0.750 كيلو</button>
            <button onclick="clearTable()">مسح الجدول</button>
        </div>
    </div>

    <script>
        // بيانات الأصناف التجريبية
        const items = {
            '000001': {
                name: 'جبنة تركي',
                price: 280
            },
            '000002': {
                name: 'لحم بقري',
                price: 450
            },
            '000003': {
                name: 'دجاج',
                price: 85
            }
        };

        let addedItems = [];

        // دالة معالجة الباركود المخصص
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(8, 12));
            const quantityInKg = weightInGrams / 1000;

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams
            };
        }

        function testBarcode() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            const resultDiv = document.getElementById('barcodeResult');

            if (!barcode) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال باركود للاختبار</div>';
                return;
            }

            const customBarcodeData = processCustomBarcode(barcode);

            if (customBarcodeData) {
                const item = items[customBarcodeData.itemBarcode];
                if (item) {
                    addItemToTable(customBarcodeData.itemBarcode, item.name, item.price, customBarcodeData.quantity);
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ تم إضافة الصنف بنجاح!</h4>
                            <p><strong>الصنف:</strong> ${item.name}</p>
                            <p><strong>الكمية:</strong> ${customBarcodeData.quantity.toFixed(3)} كيلو</p>
                            <p><strong>السعر:</strong> ${item.price} جنيه/كيلو</p>
                            <p><strong>الإجمالي:</strong> ${(customBarcodeData.quantity * item.price).toFixed(2)} جنيه</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ صنف غير موجود!</h4>
                            <p><strong>باركود الصنف:</strong> ${customBarcodeData.itemBarcode}</p>
                        </div>
                    `;
                }
            } else {
                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>ℹ️ باركود عادي</h4>
                        <p>هذا ليس باركود مخصص</p>
                    </div>
                `;
            }
        }

        function addItemToTable(itemId, itemName, itemPrice, quantity) {
            // التحقق من وجود الصنف مسبقاً
            const existingIndex = addedItems.findIndex(item => item.id === itemId);

            if (existingIndex !== -1) {
                // تحديث الكمية
                addedItems[existingIndex].quantity = quantity;
                updateTableRow(existingIndex);
            } else {
                // إضافة صنف جديد
                addedItems.push({
                    id: itemId,
                    name: itemName,
                    price: itemPrice,
                    quantity: quantity
                });
                addTableRow(addedItems.length - 1);
            }

            updateTotal();
        }

        function addTableRow(index) {
            const item = addedItems[index];
            const tbody = document.querySelector('#itemsTable tbody');
            const row = document.createElement('tr');
            row.dataset.index = index;

            row.innerHTML = `
                <td>${item.name}</td>
                <td>${item.price}</td>
                <td><input type="number" class="quantity-input" value="${item.quantity.toFixed(3)}" min="0.001" step="0.001" onchange="updateQuantity(${index}, this.value)"></td>
                <td class="total-cell">${(item.quantity * item.price).toFixed(2)}</td>
                <td><button onclick="removeItem(${index})" style="background-color: #dc3545;">حذف</button></td>
            `;

            tbody.appendChild(row);
        }

        function updateTableRow(index) {
            const item = addedItems[index];
            const row = document.querySelector(`tr[data-index="${index}"]`);
            if (row) {
                const quantityInput = row.querySelector('.quantity-input');
                const totalCell = row.querySelector('.total-cell');

                quantityInput.value = item.quantity.toFixed(3);
                totalCell.textContent = (item.quantity * item.price).toFixed(2);
            }
        }

        function updateQuantity(index, newQuantity) {
            addedItems[index].quantity = parseFloat(newQuantity) || 0;
            updateTableRow(index);
            updateTotal();
        }

        function removeItem(index) {
            addedItems.splice(index, 1);
            rebuildTable();
            updateTotal();
        }

        function rebuildTable() {
            const tbody = document.querySelector('#itemsTable tbody');
            tbody.innerHTML = '';
            addedItems.forEach((item, index) => {
                addTableRow(index);
            });
        }

        function updateTotal() {
            const total = addedItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            document.getElementById('totalAmount').textContent = total.toFixed(2);
        }

        function testExample(barcode) {
            document.getElementById('barcodeInput').value = barcode;
            testBarcode();
        }

        function clearTable() {
            addedItems = [];
            document.querySelector('#itemsTable tbody').innerHTML = '';
            updateTotal();
            document.getElementById('barcodeResult').innerHTML = '';
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testExample('2000001002308');
        };
    </script>
</body>

</html>