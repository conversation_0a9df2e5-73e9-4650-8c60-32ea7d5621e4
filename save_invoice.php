<?php
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_GET['account_id'] ?? null;
$itemsParam = $_GET['items'] ?? null;
$invoiceType = $_GET['invoice_type'] ?? null;
$branchId = $_GET['branch_id'] ?? null;
$accountBuyerId = $_GET['account_buyer_id'] ?? null;

if (!$encrypted_account_id || !$itemsParam || !$invoiceType) {
    http_response_code(400);
    echo json_encode(['error' => 'Account ID, items, and invoice type are required']);
    exit();
}

$account_id = decrypt($encrypted_account_id, $key);
if (!$account_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid Account ID']);
    exit();
}

$items = json_decode($itemsParam, true);
if (!is_array($items)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid items data']);
    exit();
}

// Define the path for the JSON file
$savedInvoicesDir = __DIR__ . '/saved_invoices';
$jsonFilePath = $savedInvoicesDir . "/account_{$account_id}.json";

// Ensure the directory exists
if (!is_dir($savedInvoicesDir)) {
    if (!mkdir($savedInvoicesDir, 0777, true) && !is_dir($savedInvoicesDir)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create directory for saved invoices']);
        exit();
    }
}

// Check directory permissions
if (!is_writable($savedInvoicesDir)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Directory is not writable',
        'directory' => $savedInvoicesDir,
        'permissions' => substr(sprintf('%o', fileperms($savedInvoicesDir)), -4),
        'owner' => posix_getpwuid(fileowner($savedInvoicesDir)),
        'group' => posix_getgrgid(filegroup($savedInvoicesDir)),
    ]);
    exit();
}

// Prepare data to save
$dataToSave = [
    'invoice_type' => $invoiceType,
    'branch_id' => $branchId,
    'account_buyer_id' => $accountBuyerId,
    'items' => $items
];

// Save the data to the JSON file
if (file_put_contents($jsonFilePath, json_encode($dataToSave)) === false) {
    // Log detailed error information
    $errorDetails = [
        'error' => 'Failed to save items to file',
        'file_path' => $jsonFilePath,
        'directory_exists' => is_dir($savedInvoicesDir),
        'is_writable_directory' => is_writable($savedInvoicesDir),
        'is_writable_file' => is_writable($jsonFilePath),
        'file_exists' => file_exists($jsonFilePath),
    ];
    http_response_code(500);
    echo json_encode($errorDetails);
    exit();
}

echo json_encode(['success' => true]);
?>
