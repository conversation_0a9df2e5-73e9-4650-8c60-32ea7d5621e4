<?php
/**
 * Register FCM Token After Login
 * 
 * This file is called after successful login to register the FCM token
 * for the newly logged in user
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

include 'db_connection.php';
require_once 'encryption_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if user is logged in
    if (!isset($_SESSION['account_id']) || !isset($_SESSION['store_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'User not logged in'
        ]);
        exit;
    }

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['token'])) {
        echo json_encode([
            'success' => false,
            'message' => 'No token provided'
        ]);
        exit;
    }

    $token = $input['token'];

    // Get store_id and account_id from session
    $key = getenv('ENCRYPTION_KEY');
    $encrypted_store_id = $_SESSION['store_id'];
    $encrypted_account_id = $_SESSION['account_id'];

    $store_id = decrypt($encrypted_store_id, $key);
    $account_id = decrypt($encrypted_account_id, $key);

    if (!$store_id || !$account_id) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid session data'
        ]);
        exit;
    }

    // Check if token already exists for this account
    $check_query = "SELECT id FROM fcm_tokens WHERE account_id = ? AND token = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("is", $account_id, $token);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows > 0) {
        // Token already exists, update last_updated
        $update_query = "UPDATE fcm_tokens SET last_updated = NOW(), store_id = ? WHERE account_id = ? AND token = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("iis", $store_id, $account_id, $token);
        
        if ($update_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Token updated successfully',
                'action' => 'updated'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update token'
            ]);
        }
        $update_stmt->close();
    } else {
        // Token doesn't exist, insert new record
        $insert_query = "INSERT INTO fcm_tokens (account_id, store_id, token, created_at, last_updated) VALUES (?, ?, ?, NOW(), NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("iis", $account_id, $store_id, $token);
        
        if ($insert_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Token registered successfully',
                'action' => 'inserted'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to register token'
            ]);
        }
        $insert_stmt->close();
    }

    $check_stmt->close();

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
