<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

$response = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_item_id'])) {
    $delete_item_id = $_POST['delete_item_id'];

    // Check if the item is linked to any purchase or wholesale transactions
    $purchase_check_sql = "SELECT COUNT(*) FROM purchases WHERE item_id = ?";
    $wholesale_check_sql = "SELECT COUNT(*) FROM whosales WHERE item_id = ?";

    $stmt = $conn->prepare($purchase_check_sql);
    $stmt->bind_param("i", $delete_item_id);
    $stmt->execute();
    $stmt->bind_result($purchase_count);
    $stmt->fetch();
    $stmt->close();

    $stmt = $conn->prepare($wholesale_check_sql);
    $stmt->bind_param("i", $delete_item_id);
    $stmt->execute();
    $stmt->bind_result($wholesale_count);
    $stmt->fetch();
    $stmt->close();

    if ($purchase_count > 0 || $wholesale_count > 0) {
        $response['success'] = false;
        $response['message'] = 'لا يمكن حذف الصنف لأنه مرتبط بعمليات شراء أو بيع بالجملة.';
    } else {
        // Delete the item from the operations table
        $delete_operations_sql = "DELETE FROM itemtransactions WHERE item_id = ?";
        $stmt = $conn->prepare($delete_operations_sql);
        $stmt->bind_param("i", $delete_item_id);
        $stmt->execute();
        $stmt->close();

        // Delete the item from the monthly inventory items table
        $delete_inventory_sql = "DELETE FROM monthly_inventory_items WHERE item_id = ?";
        $stmt = $conn->prepare($delete_inventory_sql);
        $stmt->bind_param("i", $delete_item_id);
        $stmt->execute();
        $stmt->close();

        // Fetch the item name before deletion
        $fetch_item_name_sql = "SELECT name FROM items WHERE item_id = ?";
        $stmt = $conn->prepare($fetch_item_name_sql);
        $stmt->bind_param("i", $delete_item_id);
        $stmt->execute();
        $stmt->bind_result($item_name);
        $stmt->fetch();
        $stmt->close();

        // Delete the item from the items table
        $delete_item_sql = "DELETE FROM items WHERE item_id = ?";
        $stmt = $conn->prepare($delete_item_sql);
        $stmt->bind_param("i", $delete_item_id);
        $stmt->execute();
        $stmt->close();

        // Log the item deletion action
        session_start();
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'delete', 'items', ?, ?)";
        $description = "تم حذف الصنف $item_name";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $delete_item_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        $response['success'] = true;
        $response['message'] = 'تم حذف الصنف بنجاح.';
    }
} else {
    $response['success'] = false;
    $response['message'] = 'بيانات غير صحيحة.';
}

echo json_encode($response);
$conn->close();
?>
