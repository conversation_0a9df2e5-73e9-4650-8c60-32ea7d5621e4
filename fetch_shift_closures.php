<?php
include 'db_connection.php';

$inventory_id = isset($_GET['inventory_id']) ? intval($_GET['inventory_id']) : 0;

if ($inventory_id <= 0) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT account_name, shift_date, shift_type, shift_amount, purchases, notes 
        FROM inventory_shift_closures 
        WHERE inventory_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$result = $stmt->get_result();

$shift_closures = [];
while ($row = $result->fetch_assoc()) {
    $shift_closures[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($shift_closures);
?>
