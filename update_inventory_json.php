<?php
// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to user, log them instead

include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Get the POST data with error handling
$input = file_get_contents('php://input');
if ($input === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'فشل في قراءة البيانات المرسلة']);
    exit;
}

$data = json_decode($input, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON decode error: " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'البيانات المرسلة غير صحيحة']);
    exit;
}

// Handle both single item update and bulk update
if (isset($data['quantities']) && isset($data['inventory_id'])) {
    // Bulk update - save all quantities at once
    $quantities = $data['quantities'];
    $inventory_id = $data['inventory_id'];
    
    // Log the received data for debugging
    error_log("Received bulk update request for inventory_id: $inventory_id");
    error_log("Quantities received: " . json_encode($quantities));
    
    if ($inventory_id && is_array($quantities)) {
        // Fetch the store name using store_id
        $stmt = $conn->prepare("SELECT s.name AS store_name FROM stores s JOIN monthly_inventory mi ON s.store_id = mi.store_id WHERE mi.inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->bind_result($store_name);
        $stmt->fetch();
        $stmt->close();

        // Define the JSON file path
        $json_file_path = __DIR__ . "/inventory/{$store_name}_{$inventory_id}.json";

        // Create directory if it doesn't exist
        $inventory_dir = __DIR__ . "/inventory";
        if (!is_dir($inventory_dir)) {
            mkdir($inventory_dir, 0755, true);
        }

        // Load existing data to preserve any previous entries
        $existing_quantities = [];
        if (file_exists($json_file_path)) {
            $file_content = file_get_contents($json_file_path);
            if ($file_content !== false && !empty($file_content)) {
                $existing_quantities = json_decode($file_content, true) ?: [];
            }
        }

        // Only merge non-empty quantities to avoid overwriting with empty values
        $merged_quantities = $existing_quantities;
        foreach ($quantities as $item_id => $quantity) {
            // Only update if the quantity is not empty or if it's explicitly set to zero
            if ($quantity !== '' || $quantity === '0') {
                $merged_quantities[$item_id] = $quantity;
                error_log("Updated item_id $item_id with quantity: $quantity");
            }
        }
        
        error_log("Final merged quantities: " . json_encode($merged_quantities));

        // Save back to the JSON file with proper error handling
        $json_data = json_encode($merged_quantities, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        if ($json_data !== false) {
            $result = file_put_contents($json_file_path, $json_data, LOCK_EX);
            if ($result === false) {
                error_log("Failed to write to inventory JSON file: " . $json_file_path);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'فشل في حفظ البيانات']);
                exit;
            }
        } else {
            error_log("Failed to encode JSON data for inventory: " . $inventory_id);
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'فشل في تحويل البيانات']);
            exit;
        }
        
        echo json_encode(['success' => true, 'message' => 'تم حفظ البيانات بنجاح']);
    }
} else {
    // Single item update - legacy support
    $item_id = $data['item_id'] ?? null;
    $quantity = $data['quantity'] ?? null;
    $inventory_id = $data['inventory_id'] ?? null;

    if ($item_id && $quantity !== null && $inventory_id) {
        // Fetch the store name using store_id
        $stmt = $conn->prepare("SELECT s.name AS store_name FROM stores s JOIN monthly_inventory mi ON s.store_id = mi.store_id WHERE mi.inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->bind_result($store_name);
        $stmt->fetch();
        $stmt->close();

        // Define the JSON file path
        $json_file_path = __DIR__ . "/inventory/{$store_name}_{$inventory_id}.json";

        // Create directory if it doesn't exist
        $inventory_dir = __DIR__ . "/inventory";
        if (!is_dir($inventory_dir)) {
            mkdir($inventory_dir, 0755, true);
        }

        // Load existing data with better error handling
        $quantities = [];
        if (file_exists($json_file_path)) {
            $file_content = file_get_contents($json_file_path);
            if ($file_content !== false && !empty($file_content)) {
                $quantities = json_decode($file_content, true) ?: [];
            }
        }

        // Update the quantity for the given item
        $quantities[$item_id] = $quantity;

        // Save back to the JSON file with file locking to prevent corruption
        $json_data = json_encode($quantities, JSON_UNESCAPED_UNICODE);
        if ($json_data !== false) {
            $result = file_put_contents($json_file_path, $json_data, LOCK_EX);
            if ($result === false) {
                error_log("Failed to write to inventory JSON file: " . $json_file_path);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'فشل في حفظ البيانات']);
                exit;
            }
        } else {
            error_log("Failed to encode JSON data for inventory: " . $inventory_id);
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'فشل في تحويل البيانات']);
            exit;
        }
        
        echo json_encode(['success' => true, 'message' => 'تم حفظ البيانات بنجاح']);
    }
}
?>
