<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// التحقق من صلاحية الوصول لوحدة التقارير
checkPagePermission('reports', 'view');

// فحص الصلاحيات المختلفة لوحدة التقارير
$canExportExcel = hasPermission('reports', 'export_excel');
$canViewComprehensiveReport = hasPermission('reports', 'comprehensive_report');
$canViewQueries = hasPermission('reports', 'view_queries');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);

    $sql = "SELECT items.item_id, items.name, items.type, items.quantity, items.cost, items.price 
            FROM items 
            JOIN categories ON items.category_id = categories.category_id 
            WHERE categories.store_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
} else {
    echo "Store ID is missing.";
    exit();
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* أنماط أزرار التقارير */
        .reports-actions-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .export-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .summary-btn {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
        }
        
        .summary-btn:hover {
            background: linear-gradient(135deg, #0056b3, #520dc2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .queries-btn {
            background: linear-gradient(135deg, #fd7e14, #e83e8c);
            color: white;
        }
        
        .queries-btn:hover {
            background: linear-gradient(135deg, #e8650e, #d91a72);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
        }
        
        /* أنماط التقرير الشامل */
        .summary-stats {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-label {
            font-weight: 600;
            color: #495057;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-value.profit {
            color: #28a745;
        }
        
        /* أنماط نافذة الاستعلامات */
        .queries-content {
            margin-top: 20px;
        }
        
        .query-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .query-section h3 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .query-results {
            display: grid;
            gap: 10px;
        }
        
        .query-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 3px solid #007bff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .query-item.low-stock {
            border-left-color: #dc3545;
        }
        
        .item-name {
            font-weight: 600;
            color: #495057;
        }
        
        .item-profit {
            color: #28a745;
            font-weight: bold;
        }
        
        .item-quantity {
            color: #dc3545;
            font-weight: bold;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-box .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        /* تحسينات شاملة للوضع المظلم */
        [data-theme="dark"] .permissions-warning {
            background: #856404 !important;
            color: #fff3cd !important;
            border-color: #ffeaa7 !important;
        }
        
        /* أنماط الإحصائيات في الوضع المظلم */
        [data-theme="dark"] .stat-item {
            background: #21262d;
            color: #c9d1d9;
            border-left-color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-label {
            color: #8b949e;
        }
        
        [data-theme="dark"] .stat-value {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-value.profit {
            color: #3fb950;
        }
        
        /* أنماط نوافذ الاستعلامات في الوضع المظلم */
        [data-theme="dark"] .query-section {
            background: #21262d;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] .query-section h3 {
            color: #c9d1d9;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] .query-item {
            background: #161b22;
            color: #c9d1d9;
            border-left-color: var(--color-primary);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }
        
        [data-theme="dark"] .query-item.low-stock {
            border-left-color: #f85149;
        }
        
        [data-theme="dark"] .item-name {
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .item-profit {
            color: #3fb950;
        }
        
        [data-theme="dark"] .item-quantity {
            color: #f85149;
        }
        
        [data-theme="dark"] .stat-box {
            background: #161b22;
            color: #c9d1d9;
            border: 1px solid #30363d;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        [data-theme="dark"] .stat-number {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-box .stat-label {
            color: #8b949e;
        }
        
        /* أنماط الأزرار في الوضع المظلم */
        [data-theme="dark"] .export-btn {
            background: linear-gradient(135deg, #238636, #2ea043);
        }
        
        [data-theme="dark"] .export-btn:hover {
            background: linear-gradient(135deg, #1f7a2e, #26973b);
            box-shadow: 0 4px 15px rgba(35, 134, 54, 0.4);
        }
        
        [data-theme="dark"] .summary-btn {
            background: linear-gradient(135deg, #1f6feb, #8b5cf6);
        }
        
        [data-theme="dark"] .summary-btn:hover {
            background: linear-gradient(135deg, #1a5cd8, #7c3aed);
            box-shadow: 0 4px 15px rgba(31, 111, 235, 0.4);
        }
        
        [data-theme="dark"] .queries-btn {
            background: linear-gradient(135deg, #fb8500, #d63384);
        }
        
        [data-theme="dark"] .queries-btn:hover {
            background: linear-gradient(135deg, #e07600, #c2185b);
            box-shadow: 0 4px 15px rgba(251, 133, 0, 0.4);
        }
        
        /* أنماط الجداول في الوضع المظلم */
        [data-theme="dark"] table {
            background-color: #161b22;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] th {
            background-color: var(--color-primary);
            color: #ffffff;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] td {
            border-bottom-color: #30363d;
            color: #c9d1d9;
        }
        
        [data-theme="dark"] tr:hover {
            background-color: #21262d;
            color: #c9d1d9;
        }
        
        /* أنماط النوافذ المنبثقة في الوضع المظلم */
        [data-theme="dark"] .modal-content.queries-modal {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .modal-content.summary-report {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .queries-content {
            color: #c9d1d9;
        }
        
        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .reports-actions-container {
                flex-direction: column;
            }
            
            .action-btn {
                width: 100%;
                justify-content: center;
            }
            
            .quick-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            // Apply stored theme or system preference on load
            const storedTheme = localStorage.getItem("theme");
            if (storedTheme) {
                document.documentElement.setAttribute("data-theme", storedTheme);
            } else if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
                document.documentElement.setAttribute("data-theme", "dark");
            }
        });
    </script>
    <style>
        /* أنماط إضافية للمود الدارك في تقارير الأصناف */
        [data-theme="dark"] .search-bar {
            background: linear-gradient(145deg, #21262d, #161b22);
            border-color: #30363d;
            color: #c9d1d9;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                        inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }
        
        [data-theme="dark"] .search-bar:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 
                        0 12px 35px rgba(0, 0, 0, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            background: linear-gradient(145deg, #262c36, #1c2128);
        }
        
        [data-theme="dark"] .search-bar::placeholder {
            color: #7d8590;
            opacity: 0.9;
        }
        
        /* أنماط النوافذ المنبثقة للمعاملات في الوضع المظلم */
        [data-theme="dark"] #transactionsModal .modal-content {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] #transactionsModal table {
            background-color: #161b22;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] #transactionsModal th {
            background-color: var(--color-primary);
            color: #ffffff;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] #transactionsModal td {
            border-bottom-color: #30363d;
            color: #c9d1d9;
        }
        
        [data-theme="dark"] #transactionsModal tr:hover {
            background-color: #21262d;
        }
        
        /* أنماط حاوية الجدول في الوضع المظلم */
        [data-theme="dark"] .table-container {
            background-color: #0d1117;
        }
        
        [data-theme="dark"] .table-responsive {
            border: 1px solid #30363d;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        /* أنماط رسائل الخ��أ في الوضع المظلم */
        [data-theme="dark"] .error-message {
            color: #f85149;
        }
        
        /* أنماط النصوص التوضيحية في الوضع المظلم */
        [data-theme="dark"] p {
            color: #8b949e;
        }
    </style>
</head>
<body>

<?php include 'sidebar.php'; ?>

<div class="container">
    <h2>تقارير الأصناف</h2>
    
    <!-- أزرار العمليات حسب الصلاحيات -->
    <div class="reports-actions-container" style="margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
        <?php if ($canExportExcel): ?>
        <form action="export_excel.php" method="post" style="display: inline;">
            <input type="hidden" name="store_id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">
            <button type="submit" class='action-btn export-btn' title="تصدير البيانات إلى ملف Excel">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </button>
        </form>
        <?php endif; ?>
        
        <?php if ($canViewComprehensiveReport): ?>
        <button class='action-btn summary-btn' onclick="showSummaryReport()" title="عرض التقرير الشامل للأصناف">
            <i class="fas fa-chart-pie"></i> تقرير شامل
        </button>
        <?php endif; ?>
        
        <?php if ($canViewQueries): ?>
        <button class='action-btn queries-btn' onclick="showItemQueries()" title="عرض استعلامات الأصناف">
            <i class="fas fa-search"></i> استعلامات الأصناف
        </button>
        <?php endif; ?>
    </div>

    <input type="text" id="searchField" class="search-bar" placeholder="ابحث عن صنف...">

    <div class="table-container">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>نوع الصنف</th>
                        <th>الكمية</th>
                        <th>سعر الجملة</th>
                        <th>سعر البيع</th>
                        <th>ربح القطعة الواحدة</th>
                        <th>إجمالي جملة الصنف</th>
                        <th>الربح الصافي</th>
                        <?php if ($canViewQueries): ?>
                        <th>إجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody id="itemsTable">
                    <?php
                    $total_item_price = 0;
                    $total_item_cost = 0;
                    $total_net_profit = 0;

                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            $profit_per_piece = $row['price'] - $row['cost'];
                            $total_cost = $row['cost'] * $row['quantity'];
                            $total_profit = $profit_per_piece * $row['quantity'];

                            $total_item_price += $row['price'] * $row['quantity'];
                            $total_item_cost += $total_cost;
                            $total_net_profit += $total_profit;

                            $encrypted_item_id = encrypt($row['item_id'], $key);
                            echo "<tr>
                                    <td>" . htmlspecialchars($row['name']) . "</td>
                                    <td>" . htmlspecialchars($row['type']) . "</td>
                                    <td>" . htmlspecialchars($row['quantity']) . "</td>
                                    <td>" . htmlspecialchars($row['cost']) . "</td>
                                    <td>" . htmlspecialchars($row['price']) . "</td>
                                    <td>" . htmlspecialchars($profit_per_piece) . "</td>
                                    <td>" . htmlspecialchars($total_cost) . "</td>
                                    <td>" . htmlspecialchars($total_profit) . "</td>";
                            
                            if ($canViewQueries) {
                                echo "<td>
                                        <button class='action-btn' onclick='showTransactions(\"" . htmlspecialchars($encrypted_item_id) . "\")'>
                                            <i class='fas fa-eye'></i>
                                        </button>
                                      </td>";
                            }
                            
                            echo "</tr>";
                        }
                    } else {
                        $colspan = $canViewQueries ? '9' : '8';
                        echo "<tr><td colspan='$colspan'>لا توجد أصناف حالياً</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="transactionsModal" class="modal">
    <div class="modal-content" >
        <span class="close" onclick="closeTransactionsModal()">&times;</span>
        <h2>تفاصيل العمليات</h2>
        <div class="modal-table-container">
            <table>
                <thead>
                    <tr>
                        <th>نوع العملية</th>
                        <th>الكمية</th>
                        <th>تاريخ العملية</th>
                    </tr>
                </thead>
                <tbody id="transactionsTable">
                    <!-- سيتم تعبئة البيانات هنا باستخدام JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if ($canViewComprehensiveReport): ?>
<div id="summaryReportModal" class="modal">
    <div class="modal-content summary-report" >
        <span class="close" onclick="closeSummaryReportModal()">&times;</span><br>
        <h2><i class="fas fa-chart-pie"></i> تقرير شامل</h2>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-label">إجمالي سعر الأصناف:</div>
                <div class="stat-value"><?php echo number_format($total_item_price, 2); ?> جنيه</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">إجمالي تكلفة الأصناف:</div>
                <div class="stat-value"><?php echo number_format($total_item_cost, 2); ?> جنيه</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">إجمالي الربح الصافي:</div>
                <div class="stat-value profit"><?php echo number_format($total_net_profit, 2); ?> جنيه</div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($canViewQueries): ?>
<div id="itemQueriesModal" class="modal">
    <div class="modal-content queries-modal">
        <span class="close" onclick="closeItemQueriesModal()">&times;</span><br>
        <h2><i class="fas fa-search"></i> استعلامات الأصناف</h2>
        <div class="queries-content">
            <div class="query-section">
                <h3>الأصناف الأكثر ربحية</h3>
                <div id="mostProfitableItems"></div>
            </div>
            <div class="query-section">
                <h3>الأصناف منخفضة المخزون</h3>
                <div id="lowStockItems"></div>
            </div>
            <div class="query-section">
                <h3>إحصائيات سريعة</h3>
                <div id="quickStats"></div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function showTransactions(encryptedItemId) {
    fetch(`get_transactions.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const transactionsTable = document.getElementById('transactionsTable');
                transactionsTable.innerHTML = '';
                data.transactions.forEach(transaction => {
                    let transactionTypeAr;
                    switch (transaction.transaction_type) {
                        case 'purchase':
                            transactionTypeAr = 'شراء';
                            break;
                        case 'wholesale_purchase':
                            transactionTypeAr = 'شراء جملة';
                            break;
                        case 'wholesale_sale':
                            transactionTypeAr = 'بيع جملة';
                            break;
                        case 'edit':
                            transactionTypeAr = 'تعديل';
                            break;
                        default:
                            transactionTypeAr = 'رصيد افتتاحي';
                    }

                    const transactionDate = new Date(transaction.transaction_date).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                        second: 'numeric',
                        hour12: true
                    });

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${transactionTypeAr}</td>
                        <td>${transaction.quantity}</td>
                        <td>${transactionDate}</td>
                    `;
                    transactionsTable.appendChild(row);
                });
                document.getElementById('transactionsModal').classList.add('active'); // Use "active" class
            } else {
                alert('Failed to fetch transactions.');
            }
        })
        .catch(error => {
            console.error('Error fetching transactions:', error);
        });
}

function closeTransactionsModal() {
    document.getElementById('transactionsModal').classList.remove('active'); // Use "active" class
}

function showSummaryReport() {
    <?php if ($canViewComprehensiveReport): ?>
    document.getElementById('summaryReportModal').classList.add('active');
    <?php else: ?>
    alert('ليس لديك صلاحية لعرض التقرير الشامل');
    <?php endif; ?>
}

function closeSummaryReportModal() {
    <?php if ($canViewComprehensiveReport): ?>
    document.getElementById('summaryReportModal').classList.remove('active');
    <?php endif; ?>
}

function showItemQueries() {
    <?php if ($canViewQueries): ?>
    document.getElementById('itemQueriesModal').classList.add('active');
    loadItemQueries();
    <?php else: ?>
    alert('ليس لديك صلاحية لعرض استعلامات الأصناف');
    <?php endif; ?>
}

function closeItemQueriesModal() {
    <?php if ($canViewQueries): ?>
    document.getElementById('itemQueriesModal').classList.remove('active');
    <?php endif; ?>
}

function loadItemQueries() {
    <?php if ($canViewQueries): ?>
    // تحميل الأصناف الأكثر ربحية
    const storeId = '<?php echo htmlspecialchars($encrypted_store_id); ?>';
    
    fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=most_profitable`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="query-results">';
                data.items.forEach(item => {
                    html += `
                        <div class="query-item">
                            <span class="item-name">${item.name}</span>
                            <span class="item-profit">${parseFloat(item.profit).toFixed(2)} جنيه</span>
                        </div>
                    `;
                });
                html += '</div>';
                document.getElementById('mostProfitableItems').innerHTML = html;
            }
        })
        .catch(error => {
            document.getElementById('mostProfitableItems').innerHTML = '<p>حدث خطأ في تحميل البيانات</p>';
        });

    // تحميل الأصناف منخفضة المخزون
    fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=low_stock`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="query-results">';
                data.items.forEach(item => {
                    html += `
                        <div class="query-item low-stock">
                            <span class="item-name">${item.name}</span>
                            <span class="item-quantity">${item.quantity} قطعة</span>
                        </div>
                    `;
                });
                html += '</div>';
                document.getElementById('lowStockItems').innerHTML = html;
            }
        })
        .catch(error => {
            document.getElementById('lowStockItems').innerHTML = '<p>حدث خطأ في تحميل البيانات</p>';
        });

    // تحميل الإحصائيات السريعة
    fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=quick_stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `
                    <div class="quick-stats">
                        <div class="stat-box">
                            <div class="stat-number">${data.stats.total_items}</div>
                            <div class="stat-label">إجمالي الأصناف</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">${data.stats.out_of_stock}</div>
                            <div class="stat-label">أصناف نفدت</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">${parseFloat(data.stats.avg_profit).toFixed(2)}</div>
                            <div class="stat-label">متوسط الربح</div>
                        </div>
                    </div>
                `;
                document.getElementById('quickStats').innerHTML = html;
            }
        })
        .catch(error => {
            document.getElementById('quickStats').innerHTML = '<p>حدث خطأ في تحميل البيانات</p>';
        });
    <?php endif; ?>
}

document.getElementById('searchField').onkeyup = function() {
    var filter = this.value.toLowerCase();
    var rows = document.querySelectorAll('#itemsTable tr');
    rows.forEach(row => {
        var name = row.cells[0].textContent.toLowerCase();
        var type = row.cells[1].textContent.toLowerCase();
        if (name.includes(filter) || type.includes(filter)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
