# تعديلات نظام الباركود المخصص

## الوصف
تم إضافة دعم للباركود المخصص الذي يبدأ بالرقم 2 وطوله 13 رقم، حيث يحتوي على باركود الصنف والوزن.

## تركيب الباركود المخصص
```
2XXXXXXYZZZZ#
```
- `2`: رقم ثابت يشير إلى أنه باركود مخصص
- `XXXXXX`: باركود الصنف (6 أرقام)
- `Y`: رقم فاصل (رقم واحد)
- `ZZZZ`: الوزن بالجرام (4 أرقام)
- `#`: رقم تحقق أو إضافي (لا يستخدم في الحساب)

## مثال
```
2000001002308
```
- `2`: باركود مخصص
- `000001`: بار<PERSON><PERSON><PERSON> الصنف
- `0`: فاصل
- `0230`: الوزن = 230 جرام = 0.230 كيلو
- `8`: رقم تحقق (لا يستخدم)

## الملفات المعدلة

### 1. add_invoice.php
- **الموقع**: السطور 3022-3060
- **التعديلات**:
  - إضافة دالة `processCustomBarcode()` لمعالجة الباركود المخصص
  - تعديل معالج الباركود العام لدعم الباركود المخصص
  - تحديث الكمية تلقائياً بناءً على الوزن المستخرج من الباركود

### 2. js/invoice_responsive.js
- **الموقع**: السطور 324-348 (دالة processCustomBarcode)
- **الموقع**: السطور 230-255 (معالج الباركود العام)
- **الموقع**: السطور 700-730 (معالج حقل البحث بالباركود)
- **الموقع**: السطور 1004-1007, 1017, 1021, 1036, 1058 (دالة addItemToSelectedList)
- **التعديلات**:
  - إضافة دالة `processCustomBarcode()` لمعالجة الباركود المخصص
  - تعديل معالج الباركود العام لدعم الباركود المخصص
  - تعديل معالج حقل البحث بالباركود لدعم الباركود المخصص
  - تعديل دالة `addItemToSelectedList()` لدعم الكمية المخصصة

## الوظائف الجديدة

### processCustomBarcode(barcode)
```javascript
function processCustomBarcode(barcode) {
    if (!barcode.startsWith('2') || barcode.length !== 13) {
        return null;
    }

    const itemBarcode = barcode.substring(1, 7);
    const weightInGrams = parseInt(barcode.substring(8, 12));
    const quantityInKg = weightInGrams / 1000;
    
    return {
        itemBarcode: itemBarcode,
        quantity: quantityInKg,
        weightInGrams: weightInGrams
    };
}
```

## كيفية العمل

### 1. قراءة الباركود
- عند قراءة باركود يبدأ بـ 2 وطوله 13 رقم
- يتم استخراج باركود الصنف (6 أرقام بعد الرقم 2)
- يتم استخراج الوزن (4 أرقام من الموضع 8 إلى 11، الرقم 13 ليس جزء من الوزن)
- يتم تحويل الوزن من جرام إلى كيلوجرام

### 2. البحث عن الصنف
- يتم البحث عن الصنف باستخدام باركود الصنف المستخرج
- إذا تم العثور على الصنف، يتم إضافته بالكمية المحسوبة
- إذا لم يتم العثور على الصنف، يتم عرض رسالة خطأ

### 3. إضافة الصنف
- يتم إضافة الصنف إلى الفاتورة
- يتم تحديد الكمية بناءً على الوزن المستخرج
- يتم عرض رسالة تأكيد تتضمن اسم الصنف والكمية

## ملف الاختبار
تم إنشاء ملف `test_barcode.html` لاختبار وظيفة الباركود المخصص.

## أمثلة للاختبار
- `2000001002308`: جبنة تركي - 0.230 كيلو (230 جرام)
- `2000002001759`: لحم بقري - 0.175 كيلو (175 جرام)
- `2000003007501`: دجاج - 0.750 كيلو (750 جرام)

## الإصلاحات المطبقة

### إصلاح مشكلة استخراج الكمية
- **المشكلة**: كان الكود يأخذ 5 أرقام للوزن بدلاً من 4
- **الحل**: تم تعديل `substring(8, 13)` إلى `substring(8, 12)`
- **النتيجة**: الآن 175 جرام تظهر كـ 0.175 كيلو بدلاً من 1.175

### إصلاح مشكلة تحديث الإجمالي
- **المشكلة**: الإجمالي لا يتحدث عند تغيير الكمية
- **الحل**: إضافة معالج أحداث للكمية في add_invoice.php
- **النتيجة**: الإجمالي يتحدث تلقائياً عند تغيير الكمية

### إضافة ملف اختبار
- تم إنشاء `test_quantity_calculation.html` لاختبار حساب الكمية والإجمالي

## ملاحظات
- الباركود العادي يعمل كما هو بدون تغيير
- النظام يدعم كلاً من الباركود العادي والمخصص
- يتم عرض رسائل مختلفة للباركود المخصص تتضمن الكمية
- الكمية تظهر بدقة 3 أرقام عشرية (مثال: 0.230 كيلو)
- الإجمالي يحسب تلقائياً: الكمية × السعر