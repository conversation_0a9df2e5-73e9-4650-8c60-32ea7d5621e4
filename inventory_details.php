<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Receive the encrypted inventory ID
$encrypted_inventory_id = isset($_GET['inventory_id']) ? $_GET['inventory_id'] : null;
$inventory_id = decrypt($encrypted_inventory_id, $key);

// Fetch inventory details
$inventory_sql = "SELECT * FROM monthly_inventory WHERE inventory_id = ?";
$stmt = $conn->prepare($inventory_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$inventory_result = $stmt->get_result();
$inventory = $inventory_result->fetch_assoc();
$stmt->close();

// Fetch the store name using store_id
$stmt = $conn->prepare("SELECT s.name AS store_name FROM stores s JOIN monthly_inventory mi ON s.store_id = mi.store_id WHERE mi.inventory_id = ?");
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$stmt->bind_result($store_name);
$stmt->fetch();
$stmt->close();

// تنظيف اسم المتجر من الأحرف الخاصة
$safe_store_name = preg_replace('/[^a-zA-Z0-9_\-\x{0600}-\x{06FF}]/u', '_', $store_name);
// Define the JSON file path
$json_file_path = __DIR__ . "/inventory/{$safe_store_name}_{$inventory_id}.json";

// Load existing quantities from the JSON file with error handling
$quantities_from_file = [];
if (file_exists($json_file_path)) {
    $file_content = file_get_contents($json_file_path);
    if ($file_content !== false && !empty(trim($file_content))) {
        $decoded_data = json_decode($file_content, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_data)) {
            $quantities_from_file = $decoded_data;
        } else {
            // Log the error and create a backup
            error_log("JSON decode error in inventory file $json_file_path: " . json_last_error_msg());
            
            // Create backup of corrupted file
            $backup_path = $json_file_path . '.backup.' . date('Y-m-d_H-i-s');
            copy($json_file_path, $backup_path);
            
            // Reset to empty array
            $quantities_from_file = [];
            
            // Try to fix the file
            file_put_contents($json_file_path, json_encode([], JSON_UNESCAPED_UNICODE), LOCK_EX);
        }
    }
}

// Fetch items for the selected store
$items_sql = "SELECT item_id, name, cost, price, quantity, barcode FROM items WHERE store_id = ?";
$stmt = $conn->prepare($items_sql);
$stmt->bind_param("i", $inventory['store_id']);
$stmt->execute();
$items_result = $stmt->get_result();
$items = [];
if ($items_result->num_rows > 0) {
    while($item = $items_result->fetch_assoc()) {
        $items[] = $item;
    }
}
$stmt->close();

// Add a helper function in PHP to convert Arabic numerals to English
function convertToEnglishNumbers($number) {
    $arabic = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
    $english = ['0','1','2','3','4','5','6','7','8','9'];
    return str_replace($arabic, $english, $number);
}

// Check if there are existing records in monthly_inventory_items for the current inventory
$stmt = $conn->prepare("SELECT COUNT(*) FROM monthly_inventory_items WHERE inventory_id = ?");
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$stmt->bind_result($existing_records_count);
$stmt->fetch();
$stmt->close();

// Handle inventory item submission with improved transaction handling
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['item_id']) && isset($_POST['closing_quantity'])) {
    // بدء المعاملة (Transaction)
    $conn->autocommit(FALSE);
    
    try {
        $quantities_to_save = [];
        $operations_log = [];
        
        // تسجيل بداية العملية
        $operations_log[] = "بدء عملية حفظ الجرد - " . date('Y-m-d H:i:s');
        
        // 1. حفظ بيانات الأصناف
        $operations_log[] = "بدء حفظ بيانات الأصناف";
        $items_saved = 0;
        
        foreach ($_POST['item_id'] as $index => $item_id) {
            $closing_quantity = $_POST['closing_quantity'][$index];
            $total_recorded_quantity = $_POST['total_recorded_quantity'][$index];
            $cost = $_POST['cost'][$index];
            $price = $_POST['price'][$index];

            $stmt = $conn->prepare("INSERT INTO monthly_inventory_items (inventory_id, item_id, total_recorded_quantity, closing_quantity, cost, price) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("iiissd", $inventory_id, $item_id, $total_recorded_quantity, $closing_quantity, $cost, $price);
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في حفظ بيانات الصنف رقم: " . $item_id . " - " . $stmt->error);
            }
            
            $stmt->close();
            $items_saved++;
            $quantities_to_save[$item_id] = $closing_quantity;
        }
        
        $operations_log[] = "تم حفظ {$items_saved} صنف بنجاح";

        // 2. حفظ ملف JSON
        $operations_log[] = "بدء حفظ ملف JSON";
        $operations_log[] = "مسار الملف: " . $json_file_path;
        $operations_log[] = "عدد العناصر للحفظ: " . count($quantities_to_save);
        
        // التحقق من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
        $directory = dirname($json_file_path);
        if (!is_dir($directory)) {
            if (!mkdir($directory, 0755, true)) {
                throw new Exception("فشل في إنشاء مجلد inventory: " . $directory);
            }
            $operations_log[] = "تم إنشاء مجلد inventory";
        }
        
        // التحقق من صلاحيات الكتابة
        if (!is_writable($directory)) {
            throw new Exception("لا توجد صلاحيات كتابة في مجلد inventory: " . $directory);
        }
        
        $json_data = json_encode($quantities_to_save, JSON_UNESCAPED_UNICODE);
        if ($json_data === false) {
            throw new Exception("فشل في تحويل البيانات إلى JSON: " . json_last_error_msg());
        }
        
        $bytes_written = file_put_contents($json_file_path, $json_data, LOCK_EX);
        if ($bytes_written === false) {
            $error = error_get_last();
            throw new Exception("فشل في حفظ ملف JSON. آخر خطأ: " . ($error['message'] ?? 'غير معروف'));
        }
        
        $operations_log[] = "تم حفظ ملف JSON بنجاح ({$bytes_written} bytes)";

        // 3. نقل بيانات المصروفات
        $operations_log[] = "بدء نقل بيانات المصروفات";
        $expenses_saved = 0;
        
        $expenses_sql = "SELECT expense_name, expense_type, amount, expense_date FROM expenses WHERE store_id = ?";
        $stmt = $conn->prepare($expenses_sql);
        $stmt->bind_param("i", $inventory['store_id']);
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في استعلام المصروفات: " . $stmt->error);
        }
        
        $expenses_result = $stmt->get_result();
        if ($expenses_result->num_rows > 0) {
            while ($expense = $expenses_result->fetch_assoc()) {
                $insert_expense_sql = "INSERT INTO inventory_expenses (inventory_id, expense_name, expense_type, amount, expense_date) VALUES (?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_expense_sql);
                $insert_stmt->bind_param(
                    "issds",
                    $inventory_id,
                    $expense['expense_name'],
                    $expense['expense_type'],
                    $expense['amount'],
                    $expense['expense_date']
                );
                
                if (!$insert_stmt->execute()) {
                    throw new Exception("فشل في حفظ المصروف: " . $expense['expense_name'] . " - " . $insert_stmt->error);
                }
                
                $insert_stmt->close();
                $expenses_saved++;
            }
        }
        $stmt->close();
        $operations_log[] = "تم نقل {$expenses_saved} مصروف بنجاح";

        // 4. نقل بيانات إقفال الورديات
        $operations_log[] = "بدء نقل بيانات إقفال الورديات";
        $shifts_saved = 0;
        
        $shift_closures_sql = "SELECT account_id, shift_date, shift_type, notes, purchases, shift_amount, created_at FROM shift_closures WHERE store_id = ? AND status = 'active'";
        $stmt = $conn->prepare($shift_closures_sql);
        $stmt->bind_param("i", $inventory['store_id']);
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في استعلام إقفال الورديات: " . $stmt->error);
        }
        
        $shift_closures_result = $stmt->get_result();
        if ($shift_closures_result->num_rows > 0) {
            while ($shift_closure = $shift_closures_result->fetch_assoc()) {
                // الحصول على اسم الحساب
                $account_name_sql = "SELECT name FROM accounts WHERE account_id = ?";
                $account_stmt = $conn->prepare($account_name_sql);
                $account_stmt->bind_param("i", $shift_closure['account_id']);
                
                if (!$account_stmt->execute()) {
                    throw new Exception("فشل في الحصول على اسم الحساب رقم: " . $shift_closure['account_id']);
                }
                
                $account_stmt->bind_result($account_name);
                $account_stmt->fetch();
                $account_stmt->close();

                $insert_shift_closure_sql = "INSERT INTO inventory_shift_closures (inventory_id, account_id, account_name, shift_date, shift_type, shift_amount, notes, purchases, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_shift_closure_sql);
                $insert_stmt->bind_param(
                    "iisssddds",
                    $inventory_id,
                    $shift_closure['account_id'],
                    $account_name,
                    $shift_closure['shift_date'],
                    $shift_closure['shift_type'],
                    $shift_closure['shift_amount'],
                    $shift_closure['notes'],
                    $shift_closure['purchases'],
                    $shift_closure['created_at']
                );
                
                if (!$insert_stmt->execute()) {
                    throw new Exception("فشل في حفظ إقفال الوردية للحساب: " . $account_name . " - " . $insert_stmt->error);
                }
                
                $insert_stmt->close();
                $shifts_saved++;
            }
        }
        $stmt->close();
        $operations_log[] = "تم نقل {$shifts_saved} إقفال وردية بنجاح";

        // 5. نقل بيانات تحويلات الرصيد
        $operations_log[] = "بدء نقل بيانات تحويلات الرصيد";
        $transfers_saved = 0;
        
        $balance_transfers_sql = "SELECT account_id, provider, cost, sale_price, value, created_at FROM balance_transfers WHERE store_id = ?";
        $stmt = $conn->prepare($balance_transfers_sql);
        $stmt->bind_param("i", $inventory['store_id']);
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في استعلام تحويلات الرصيد: " . $stmt->error);
        }
        
        $balance_transfers_result = $stmt->get_result();
        if ($balance_transfers_result->num_rows > 0) {
            while ($transfer = $balance_transfers_result->fetch_assoc()) {
                // الحصول على اسم الحساب
                $account_name_sql = "SELECT name FROM accounts WHERE account_id = ?";
                $account_stmt = $conn->prepare($account_name_sql);
                $account_stmt->bind_param("i", $transfer['account_id']);
                
                if (!$account_stmt->execute()) {
                    throw new Exception("فشل في الحصول على اسم الحساب رقم: " . $transfer['account_id']);
                }
                
                $account_stmt->bind_result($account_name);
                $account_stmt->fetch();
                $account_stmt->close();

                $insert_transfer_sql = "INSERT INTO inventory_balance_transfers (inventory_id, account_id, account_name, provider, cost, sale_price, value, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_transfer_sql);
                $insert_stmt->bind_param(
                    "iissddds",
                    $inventory_id,
                    $transfer['account_id'],
                    $account_name,
                    $transfer['provider'],
                    $transfer['cost'],
                    $transfer['sale_price'],
                    $transfer['value'],
                    $transfer['created_at']
                );
                
                if (!$insert_stmt->execute()) {
                    throw new Exception("فشل في حفظ تحويل الرصيد للحساب: " . $account_name . " - " . $insert_stmt->error);
                }
                
                $insert_stmt->close();
                $transfers_saved++;
            }
        }
        $stmt->close();
        $operations_log[] = "تم نقل {$transfers_saved} تحويل رصيد بنجاح";

        // 6. نقل بيانات فواتير الشراء
        $operations_log[] = "بدء نقل بيانات فواتير الشراء";
        $invoices_saved = 0;
        $invoice_items_saved = 0;
        
        $purchase_invoices_sql = "SELECT invoice_id, total_amount, created_at, account_id FROM purchase_invoices WHERE store_id = ?";
        $stmt = $conn->prepare($purchase_invoices_sql);
        $stmt->bind_param("i", $inventory['store_id']);
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في استعلام فواتير الشراء: " . $stmt->error);
        }
        
        $purchase_invoices_result = $stmt->get_result();
        if ($purchase_invoices_result->num_rows > 0) {
            while ($invoice = $purchase_invoices_result->fetch_assoc()) {
                // الحصول على اسم الحساب
                $account_name_sql = "SELECT name FROM accounts WHERE account_id = ?";
                $account_stmt = $conn->prepare($account_name_sql);
                $account_stmt->bind_param("i", $invoice['account_id']);
                
                if (!$account_stmt->execute()) {
                    throw new Exception("فشل في الحصول على اسم الحساب رقم: " . $invoice['account_id']);
                }
                
                $account_stmt->bind_result($account_name);
                $account_stmt->fetch();
                $account_stmt->close();

                $insert_invoice_sql = "INSERT INTO inventory_purchase_invoices (inventory_id, total_amount, created_at, account_id, account_name) VALUES (?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_invoice_sql);
                $insert_stmt->bind_param(
                    "idsis",
                    $inventory_id,
                    $invoice['total_amount'],
                    $invoice['created_at'],
                    $invoice['account_id'],
                    $account_name
                );
                
                if (!$insert_stmt->execute()) {
                    throw new Exception("فشل في حفظ فاتورة الشراء رقم: " . $invoice['invoice_id'] . " - " . $insert_stmt->error);
                }
                
                $inventory_invoice_id = $insert_stmt->insert_id;
                $insert_stmt->close();
                $invoices_saved++;

                // نقل أصناف الفاتورة
                $invoice_items_sql = "SELECT item_id, quantity, time FROM purchases WHERE invoice_id = ?";
                $items_stmt = $conn->prepare($invoice_items_sql);
                $items_stmt->bind_param("i", $invoice['invoice_id']);
                
                if (!$items_stmt->execute()) {
                    throw new Exception("فشل في استعلام أصناف الفاتورة رقم: " . $invoice['invoice_id']);
                }
                
                $invoice_items_result = $items_stmt->get_result();
                if ($invoice_items_result->num_rows > 0) {
                    while ($item = $invoice_items_result->fetch_assoc()) {
                        $insert_item_sql = "INSERT INTO inventory_invoice_items (inventory_invoice_id, item_id, quantity, time) VALUES (?, ?, ?, ?)";
                        $insert_item_stmt = $conn->prepare($insert_item_sql);
                        $insert_item_stmt->bind_param(
                            "iiis",
                            $inventory_invoice_id,
                            $item['item_id'],
                            $item['quantity'],
                            $item['time']
                        );
                        
                        if (!$insert_item_stmt->execute()) {
                            throw new Exception("فشل في حفظ صنف الفاتورة - الصنف رقم: " . $item['item_id'] . " - " . $insert_item_stmt->error);
                        }
                        
                        $insert_item_stmt->close();
                        $invoice_items_saved++;
                    }
                }
                $items_stmt->close();
            }
        }
        $stmt->close();
        $operations_log[] = "تم نقل {$invoices_saved} فاتورة شراء و {$invoice_items_saved} صنف فاتورة بنجاح";

        // 7. تسجيل العملية في سجل النظام
        $operations_log[] = "انتهت عملية حفظ الجرد بنجاح - " . date('Y-m-d H:i:s');
        $log_content = implode("\n", $operations_log);
        
        // حفظ السجل في ملف
        $log_file = __DIR__ . "/logs/inventory_operations_" . date('Y-m-d') . ".log";
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        file_put_contents($log_file, $log_content . "\n\n", FILE_APPEND | LOCK_EX);

        // تأكيد المعاملة
        $conn->commit();
        $conn->autocommit(TRUE);
        
        // إعادة التوجيه بعد النجاح
        header("Location: inventory_summary.php?inventory_id=" . urlencode($encrypted_inventory_id) . "&success=1");
        exit();
        
    } catch (Exception $e) {
        // التراجع عن جميع العمليات في حالة الخطأ
        $conn->rollback();
        $conn->autocommit(TRUE);
        
        // تسجيل الخطأ
        $error_log = [
            "خطأ في عملية حفظ الجرد - " . date('Y-m-d H:i:s'),
            "رسالة الخطأ: " . $e->getMessage(),
            "تم التراجع عن جميع العمليات",
            "العمليات المكتملة قبل الخطأ:"
        ];
        $error_log = array_merge($error_log, $operations_log);
        
        $error_content = implode("\n", $error_log);
        $error_file = __DIR__ . "/logs/inventory_errors_" . date('Y-m-d') . ".log";
        $error_dir = dirname($error_file);
        if (!is_dir($error_dir)) {
            mkdir($error_dir, 0755, true);
        }
        file_put_contents($error_file, $error_content . "\n\n", FILE_APPEND | LOCK_EX);
        
        // إظهار رسالة خطأ للمستخدم
        $error_message = "حدث خطأ أثناء حفظ الجرد: " . $e->getMessage();
        echo "<script>
            alert('$error_message');
            window.location.href = 'inventory_details.php?inventory_id=" . urlencode($encrypted_inventory_id) . "&error=1';
        </script>";
        exit();
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>تفاصيل الجرد</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/inventory.css">
    <link rel="stylesheet" href="web_css/inventory_dark_mode_enhancements.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- CSS مخصص لشريط البحث الثابت -->
    
    
    
    <script>
        // تعيين المتغيرات العامة للاستخدام في ملف JavaScript الخارجي
        window.inventoryId = "<?php echo $inventory_id; ?>";
        window.quantitiesFromFile = <?php echo json_encode($quantities_from_file); ?>;
        window.hasExistingData = <?php echo !empty($quantities_from_file) ? 'true' : 'false'; ?>;
    </script>
</head>
<body>

<div class="container my-5">
    <?php
    // عرض رسائل النجاح والخطأ
    if (isset($_GET['success']) && $_GET['success'] == '1') {
        echo '<div class="alert alert-success alert-dismissible fade show" role="alert" style="border-radius: 10px; border-right: 4px solid #28a745;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-check-circle"></i>
                <div>
                    <strong>تم بنجاح!</strong>
                    تم حفظ الجرد ونقل جميع البيانات بين الجداول بنجاح
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>';
    }
    
    if (isset($_GET['error']) && $_GET['error'] == '1') {
        echo '<div class="alert alert-danger alert-dismissible fade show" role="alert" style="border-radius: 10px; border-right: 4px solid #dc3545;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>حدث خطأ!</strong>
                    فشل في حفظ الجرد - تم التراجع عن جميع العمليات. يرجى المحاولة مرة أخرى.
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>';
    }
    ?>
    
    <!-- تنبيه حالة الحفظ -->
    <div id="saveStatusAlert" class="alert alert-info" style="display: none; margin-bottom: 20px; border-radius: 10px; border-right: 4px solid #17a2b8;">
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-info-circle"></i>
            <div>
                <strong>معلومة هامة:</strong>
                <span id="saveStatusText">يتم حفظ البيانات تلقائياً أثناء الكتابة</span>
            </div>
        </div>
    </div>

    <!-- رأس الصفحة المحسن -->
    <div >
        <h2 >
            <i class="fas fa-clipboard-list"></i>
            تفاصيل الجرد الشهري
            <!-- مؤشر حالة الاتصال -->
            <div id="connectionStatus" class="connection-status online" style="display: inline-block; margin-right: 15px;">
                <span id="connectionStatusText"><i class="fas fa-wifi"></i> متصل</span>
            </div>
        </h2>
        <p class="text-center mb-0 mt-2" style="opacity: 0.9;">
            قم بإدخال الكميات المتبقية لكل صنف لإكمال عملية الجرد
            <br>
            <small style="font-size: 14px; opacity: 0.8;">
                <i class="fas fa-save"></i> يتم حفظ البيانات تلقائياً - لا تقلق من فقدان عملك
            </small>
        </p>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="inventory-stats">
        <div class="stat-card">
            <div class="stat-number" id="total-items">0</div>
            <div class="stat-label">إجمالي الأصناف</div>
            <div class="status-indicator status-pending"></div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="completed-items">0</div>
            <div class="stat-label">أصناف مسجلة</div>
            <div class="status-indicator status-completed"></div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="remaining-items">0</div>
            <div class="stat-label">غير مسجلة</div>
            <div class="status-indicator status-empty"></div>
        </div>
    </div>

    <!-- أزرار الإجراءات الرئيسية -->
    <div class="action-buttons-container">
        <h5 style="margin-bottom: 20px; color: #495057; font-weight: 600; text-align: center;">
            <i class="fas fa-tools"></i> إجراءات الجرد
        </h5>
        <div class="action-buttons-row">

            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-primary" onclick="downloadExcel()">
                    <i class="fas fa-file-excel"></i>
                    <span>تنزيل Excel</span>
                </button>
            </div>
            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-danger" onclick="window.location.href='generate_inventory_pdf.php?inventory_id=<?php echo urlencode($encrypted_inventory_id); ?>'">
                    <i class="fas fa-file-pdf"></i>
                    <span>تنزيل PDF</span>
                </button>
            </div>
            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-info" onclick="showLocalStorageInfo()">
                    <i class="fas fa-info-circle"></i>
                    <span>معلومات التخزين</span>
                </button>
            </div>
        </div>
        
        <!-- أزرار إضافية -->
        <div class="action-buttons-row" style="margin-top: 15px;">
            <div class="action-btn-wrapper">
                <?php if ($existing_records_count == 0): ?>
                    <button type="button" class="action-btn btn btn-success btn-lg" onclick="preventMultipleSubmissions()">
                        <i class="fas fa-check-circle"></i>
                        <span>إكمال الجرد</span>
                    </button>
                <?php else: ?>
                    <button type="button" class="action-btn btn btn-secondary btn-lg" disabled>
                        <i class="fas fa-check-circle"></i>
                        <span>تم إكمال الجرد</span>
                    </button>
                <?php endif; ?>
            </div>
            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-warning" id="syncButton" onclick="manualSync()">
                    <i class="fas fa-sync-alt"></i>
                    <span>مزامنة البيانات</span>
                    <span id="pendingCount" class="pending-badge" style="display: none;">0</span>
                </button>
            </div>
            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-secondary" onclick="scrollToTop()">
                    <i class="fas fa-arrow-up"></i>
                    <span>العودة للأعلى</span>
                </button>
            </div>
            <div class="action-btn-wrapper">
                <button type="button" class="action-btn btn btn-outline-primary" onclick="window.location.href='inventory.php'">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة للجرد</span>
                </button>
            </div>
        </div>
    </div>

    <!-- شريط التقدم -->
    <div class="progress-container">
        <h5 class="mb-3">
            <i class="fas fa-chart-line"></i>
            تقدم الجرد
        </h5>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
        <div class="progress-text text-center">0 من 0 (0%)</div>
    </div>

    <!-- شريط البحث المحسن -->
    <div class="search-container-enhanced">
        <div class="search-input-wrapper">
            <i class="fas fa-search"></i>
            <input type="text" id="searchField" class="input-field" placeholder="ابحث عن صنف أو امسح الباركود..." onkeyup="filterItems()">
            <button type="button" id="clearSearchBtn" onclick="clearSearch()" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="quick-actions">
            <button class="quick-btn active" data-filter="all" onclick="filterByStatus('all')">
                <i class="fas fa-list"></i> جميع الأصناف
            </button>
            <button class="quick-btn" data-filter="completed" onclick="filterByStatus('completed')">
                <i class="fas fa-check-circle"></i> مسجلة
            </button>
            <button class="quick-btn" data-filter="pending" onclick="filterByStatus('pending')">
                <i class="fas fa-clock"></i> غير مسجلة
            </button>
            <button type="button" class="quick-btn" onclick="toggleCalculator()">
                <i class="fas fa-calculator"></i> آلة حاسبة
            </button>
            <button type="button" class="quick-btn" onclick="manualSync()" id="syncButton">
                <i class="fas fa-sync-alt"></i> مزامنة البيانات
                <span id="pendingCount" class="pending-badge" style="display: none;">0</span>
            </button>
        </div>
    </div>
    
    <form method="POST" action="" onsubmit="return preventMultipleSubmissions();" id="inventoryForm">
        <div class="table-container-enhanced">
            <div class="table-header-enhanced">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i>
                    جدول الأصناف
                </h5>
                <small>استخدم قارئ الباركود أو البحث للعثور على الأصناف</small>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered text-center">
                    <thead class="table-dark">
                        <tr>
                            <th>
                                <i class="fas fa-box"></i>
                                اسم الصنف
                            </th>
                            <th class="barcode-col">الباركود</th>
                            <th>
                                <i class="fas fa-dollar-sign"></i>
                                سعر الجملة
                            </th>
                            <th>
                                <i class="fas fa-warehouse"></i>
                                الكمية الحالية
                            </th>
                            <th>
                                <i class="fas fa-edit"></i>
                                الكمية المتبقية
                            </th>
                            <th>
                                <i class="fas fa-shopping-cart"></i>
                                الكمية المباعة
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($items as $item) {
                            echo "<tr class='item-row' data-item-id='{$item['item_id']}' data-barcode='{$item['barcode']}'>
                                    <td style='font-weight: 600;'>{$item['name']}</td>
                                    <td class='barcode-col'>{$item['barcode']}</td>
                                    <td>" . convertToEnglishNumbers($item['cost']) . " ج.م</td>
                                    <td><span class='badge badge-info'>" . convertToEnglishNumbers($item['quantity']) . "</span></td>
                                    <td><input type='number' name='closing_quantity[]' class='quantity-input-enhanced' placeholder='أدخل الكمية' step='0.01' min='0'></td>
                                    <td><span class='sold_value badge badge-secondary'>0</span></td>

                                    <input type='hidden' name='item_id[]' value='{$item['item_id']}'>
                                    <input type='hidden' name='total_recorded_quantity[]' value='" . convertToEnglishNumbers($item['quantity']) . "'>
                                    <input type='hidden' name='cost[]' value='" . convertToEnglishNumbers($item['cost']) . "'>
                                    <input type='hidden' name='price[]' value='" . convertToEnglishNumbers($item['price']) . "'>
                                  </tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
        

    </form>
</div>

<!-- العناصر العائمة -->
<div class="floating-summary">
    <div class="summary-content">
        <div style="font-weight: bold; margin-bottom: 5px;">تقدم الجرد</div>
        <div>0/0</div>
        <div style="font-size: 0.8em; color: #666;">0% مكتمل</div>
    </div>
</div>

<button class="back-to-top" onclick="scrollToTop()">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- تعديل: إضافة زر إغلاق داخل رأس الآلة الحاسبة -->
<div id="calculator" class="draggable" style="display:none;">
  <div id="calcHeader">
      Calculator 
      <span id="calcCloseBtn" onclick="closeCalculator()" style="cursor:pointer; float:right; margin-right:5px;">✖</span>
  </div>
  <input type="text" id="calcDisplay" oninput="liveCompute()" placeholder="0">
  <div id="calcResult">0</div>
  <div id="calcButtons">
    <button onclick="calcInput('7')">7</button>
    <button onclick="calcInput('8')">8</button>
    <button onclick="calcInput('9')">9</button>
    <button onclick="calcOperation('/')">÷</button>
    <button onclick="calcInput('4')">4</button>
    <button onclick="calcInput('5')">5</button>
    <button onclick="calcInput('6')">6</button>
    <button onclick="calcOperation('*')">×</button>
    <button onclick="calcInput('1')">1</button>
    <button onclick="calcInput('2')">2</button>
    <button onclick="calcInput('3')">3</button>
    <button onclick="calcOperation('-')">-</button>
    <button onclick="calcInput('0')">0</button>
    <button onclick="calcInput('.')">.</button>
    <button onclick="calcCompute()">=</button>
    <button onclick="calcOperation('+')">+</button>
    <button onclick="calcUndo()">تراجع</button> <!-- زر التراجع المضاف -->
    <button onclick="calcClear()" style="grid-column: span 4; background: #b71c1c;">Clear</button>
  </div>
</div>



<script src="js/inventory_details.js"></script>
<script src="js/inventory_completion.js"></script>
<script src="js/theme.js"></script>


<!-- زر العودة للأعلى -->
    <button class="back-to-top" onclick="scrollToTop()" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- الملخص العائم -->
    <div class="floating-summary">
        <div class="summary-content">
            <div style="font-weight: bold; margin-bottom: 5px;">تقدم الجرد</div>
            <div>0 مسجل/0</div>
            <div style="font-size: 0.8em; color: #666;">0% مكتمل</div>
        </div>
    </div>

    

</body>
</html>

<?php
$conn->close();
?>
