<?php
include 'security.php';

// تعيين ترميز قاعدة البيانات
if (isset($conn)) {
    $conn->set_charset("utf8mb4");
}

// التحقق من صلاحية إدارة الصلاحيات
// السماح للمدير بالوصول دائماً
if ($_SESSION['role'] !== 'admin') {
    checkPagePermission('accounts', 'manage');
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // تنظيف أي output سابق
    if (ob_get_level()) {
        ob_clean();
    }

    header('Content-Type: application/json; charset=utf-8');

    try {
        switch ($_POST['action']) {
            case 'grant_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
                $notes = $_POST['notes'] ?? null;

                try {
                    $result = $permissions_system->grantUserPermission($user_id, $module_name, $permission_name, $expires_at, $notes);
                    echo json_encode(['success' => $result, 'message' => 'تم منح الصلاحية بنجاح']);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
                }
                break;

            case 'revoke_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $reason = $_POST['reason'] ?? null;

                try {
                    $result = $permissions_system->revokeUserPermission($user_id, $module_name, $permission_name, $reason);
                    echo json_encode(['success' => $result, 'message' => 'تم سحب الصلاحية بنجاح']);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
                }
                break;

            case 'get_user_permissions':
                $user_id = (int)$_POST['user_id'];

                // الحصول على نوع الوصول للمستخدم
                $access_type_query = "SELECT access_type FROM accounts WHERE account_id = ?";
                $stmt = $conn->prepare($access_type_query);
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $user_access_type = 'cashier_system';
                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    $user_access_type = $row['access_type'] ?? 'cashier_system';
                }
                $stmt->close();

                $permissions = $permissions_system->getUserPermissions($user_id);

                // تصفية الصلاحيات حسب نوع الوصول
                if ($user_access_type === 'cashier_system') {
                    // عرض وحدات نظام الكاشير فقط
                    $cashier_modules = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
                    $filtered_permissions = [];
                    foreach ($cashier_modules as $module) {
                        if (isset($permissions[$module])) {
                            $filtered_permissions[$module] = $permissions[$module];
                        }
                    }
                    $permissions = $filtered_permissions;
                }

                echo json_encode(['success' => true, 'permissions' => $permissions, 'access_type' => $user_access_type]);
                break;

            case 'grant_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("INSERT IGNORE INTO user_stores (user_id, store_id, granted) VALUES (?, ?, TRUE)");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم منح الوصول للفرع بنجاح']);
                break;

            case 'revoke_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("DELETE FROM user_stores WHERE user_id = ? AND store_id = ?");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم سحب الوصول للفرع بنجاح']);
                break;

            case 'get_user_stores':
                $user_id = (int)$_POST['user_id'];

                $stmt = $conn->prepare("SELECT store_id FROM user_stores WHERE user_id = ? AND granted = TRUE");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                $user_stores = [];
                while ($row = $result->fetch_assoc()) {
                    $user_stores[] = $row['store_id'];
                }
                $stmt->close();

                echo json_encode(['success' => true, 'stores' => $user_stores]);
                break;

            case 'change_access_type':
                $user_id = (int)$_POST['user_id'];
                $access_type = $_POST['access_type'];

                // التحقق من صحة نوع الوصول
                if (!in_array($access_type, ['admin_panel', 'cashier_system'])) {
                    echo json_encode(['success' => false, 'message' => 'نوع وصول غير صحيح']);
                    break;
                }

                $stmt = $conn->prepare("UPDATE accounts SET access_type = ? WHERE account_id = ?");
                $stmt->bind_param("si", $access_type, $user_id);
                $result = $stmt->execute();
                $stmt->close();

                $access_type_ar = $access_type == 'cashier_system' ? 'نظام الكاشير' : 'النظام الإداري';
                echo json_encode(['success' => $result, 'message' => "تم تغيير نوع الوصول إلى $access_type_ar بنجاح"]);
                break;

            case 'get_user_access_type':
                $user_id = (int)$_POST['user_id'];

                $stmt = $conn->prepare("SELECT access_type FROM accounts WHERE account_id = ?");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    echo json_encode(['success' => true, 'access_type' => $row['access_type']]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
                }
                $stmt->close();
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// جلب جميع المستخدمين النشطين (المدير يمكنه إدارة جميع المستخدمين)
$users_query = "SELECT account_id, username, name, role, access_type FROM accounts 
                WHERE status = 'active' 
                ORDER BY 
                    CASE 
                        WHEN role = 'admin' THEN 1 
                        WHEN role = 'user' THEN 2 
                        ELSE 3 
                    END, 
                    name";
$users_result = $conn->query($users_query);

// جلب قائمة الوحدات
$modules_query = "SELECT * FROM modules WHERE is_active = TRUE ORDER BY sort_order, module_name_ar";
$modules_result = $conn->query($modules_query);

// جلب قائمة الصلاحيات
$permissions_query = "SELECT * FROM permissions ORDER BY permission_name_ar";
$permissions_result = $conn->query($permissions_query);

// جلب قائمة الفروع
$stores_query = "SELECT store_id, name FROM stores ORDER BY name";
$stores_result = $conn->query($stores_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        /* استخدام متغيرات الألوان من style_web.css */
        .permissions-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .user-selector {
            background: var(--color-secondary);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }

        [data-theme="dark"] .user-selector {
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .module-card {
            background: var(--color-secondary);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        [data-theme="dark"] .module-card {
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .module-header {
            background: linear-gradient(135deg, #3f51b5, #5c6bc0);
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .module-permissions {
            padding: 15px;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .permission-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-user-selected {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 18px;
        }

        .tabs-container {
            background: var(--color-secondary);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        [data-theme="dark"] .tabs-container {
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .tabs {
            display: flex;
            background: var(--color-primary);
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: var(--color-secondary);
            color: #3f51b5;
            border-bottom: 3px solid #3f51b5;
        }

        .tab-button:hover {
            background: var(--hover-color);
        }

        .tab-button.active:hover {
            background: var(--color-secondary);
        }

        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .store-card {
            background: var(--color-secondary);
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .store-card:hover {
            border-color: #3f51b5;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        [data-theme="dark"] .store-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .store-card.selected {
            border-color: #4CAF50;
            background: var(--success-bg);
        }

        .store-card .store-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #3f51b5;
        }

        .store-card.selected .store-icon {
            color: #4CAF50;
        }

        .module-toggle-card {
            background: var(--color-secondary);
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-toggle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        [data-theme="dark"] .module-toggle-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .module-toggle-card.module-enabled {
            border-color: #4CAF50;
            background: var(--success-bg);
        }

        .module-toggle-card.module-disabled {
            border-color: #f44336;
            background: var(--error-bg);
        }

        .module-toggle-card .module-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .module-toggle-card.module-enabled .module-icon {
            color: #4CAF50;
        }

        .module-toggle-card.module-disabled .module-icon {
            color: #f44336;
        }

        .module-toggle-card .module-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .module-toggle-card .module-status {
            font-size: 12px;
            font-weight: bold;
        }

        .dashboard-section {
            margin-bottom: 30px;
        }

        .operations-section {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }

        .access-type-card {
            background: var(--color-secondary);
            border: 2px solid var(--border-color);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .access-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        [data-theme="dark"] .access-type-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .access-type-card.selected {
            border-color: #4CAF50;
            background: var(--success-bg);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .access-type-card.selected::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #27ae60);
        }

        .access-type-icon {
            margin-bottom: 20px;
        }

        .access-type-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-color);
            margin-bottom: 15px;
        }

        .access-type-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .access-type-features {
            text-align: right;
            margin-bottom: 20px;
        }

        .access-type-features div {
            padding: 8px 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .access-type-features i {
            color: #4CAF50;
            margin-left: 8px;
            width: 16px;
        }

        .access-type-status {
            font-weight: bold;
            padding: 10px;
            border-radius: 8px;
            background: var(--color-primary);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .access-type-card.selected .access-type-status {
            background: var(--success-bg);
            border-color: #4CAF50;
            color: #2e7d32;
        }

        .permissions-section {
            background: var(--color-secondary);
        }

        [data-theme="dark"] .permissions-section {
            box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
        }

        .operation-item {
            background: var(--color-secondary);
        }

        /* تحسينات شاملة للوضع ا��مظلم وتجربة المستخدم */
        .module-toggle-card .module-name {
            color: var(--color-fg);
            font-weight: 600;
        }

        .no-user-selected {
            color: var(--color-fg);
            opacity: 0.7;
        }

        .loading {
            color: var(--color-fg);
            opacity: 0.7;
        }

        /* تحسين الألوان والتأثيرات في الوضع المظلم */
        [data-theme="dark"] .permissions-section {
            background: #161b22;
            border: 1px solid #30363d;
        }

        [data-theme="dark"] .module-toggle-card.module-enabled {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.08) 100%);
            border-color: #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        [data-theme="dark"] .module-toggle-card.module-disabled {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(220, 53, 69, 0.08) 100%);
            border-color: #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        [data-theme="dark"] .store-card.selected {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.08) 100%);
            border-color: #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        [data-theme="dark"] .access-type-card.selected {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.08) 100%);
            border-color: #28a745;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        /* تحسين الألوان للعناصر التفاعلية */
        [data-theme="dark"] .operation-item {
            background: #21262d;
            border-color: #30363d;
        }

        [data-theme="dark"] .operation-item:hover {
            background: #262c36;
            border-color: var(--color-primary);
        }

        /* تحسين ألوان النصوص */
        [data-theme="dark"] .module-toggle-card .module-status {
            font-weight: 600;
        }

        [data-theme="dark"] .access-type-title {
            color: #e6edf3;
        }

        [data-theme="dark"] .access-type-description {
            color: #8b949e;
        }

        [data-theme="dark"] .access-type-features div {
            color: #8b949e;
        }

        /* تحسين التبويبات */
        [data-theme="dark"] .tabs {
            background: #0d1117;
            border-bottom: 1px solid #30363d;
        }

        [data-theme="dark"] .tab-button {
            color: #8b949e;
        }

        [data-theme="dark"] .tab-button.active {
            background: #161b22;
            color: var(--color-primary);
            border-bottom-color: var(--color-primary);
        }

        [data-theme="dark"] .tab-button:hover {
            background: #21262d;
            color: #c9d1d9;
        }

        [data-theme="dark"] .tab-button.active:hover {
            background: #161b22;
            color: var(--color-primary);
        }

        /* تحسين الانتقالات والتأثيرات */
        .module-toggle-card,
        .store-card,
        .access-type-card,
        .operation-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .module-toggle-card:hover,
        .store-card:hover,
        .access-type-card:hover {
            transform: translateY(-3px) scale(1.02);
        }

        [data-theme="dark"] .module-toggle-card:hover,
        [data-theme="dark"] .store-card:hover,
        [data-theme="dark"] .access-type-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        /* تحسين الأيقونات */
        .module-toggle-card .module-icon,
        .store-card .store-icon,
        .access-type-icon i {
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .module-toggle-card:hover .module-icon,
        .store-card:hover .store-icon,
        .access-type-card:hover .access-type-icon i {
            transform: scale(1.1);
        }

        /* تحسين مظهر المفاتيح */
        .slider {
            background: linear-gradient(135deg, #ccc 0%, #e0e0e0 100%);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .slider {
            background: linear-gradient(135deg, #30363d 0%, #21262d 100%);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        input:checked + .slider {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        [data-theme="dark"] input:checked + .slider {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
        }

        .slider:before {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        [data-theme="dark"] .slider:before {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        }

        /* تحسين الرسائل التوضيحية */
        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 1px solid #2196f3;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border-right: 4px solid #2196f3;
        }

        [data-theme="dark"] .info-box {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%);
            border-color: var(--color-primary);
            border-right-color: var(--color-primary);
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .permissions-container {
                padding: 15px;
            }

            .module-toggle-card,
            .store-card,
            .access-type-card {
                padding: 20px;
            }

            .access-type-card {
                padding: 20px;
            }

            .access-type-title {
                font-size: 20px;
            }

            .access-type-description {
                font-size: 13px;
            }
        }

        /* تحسين إمكانية الوصول */
        .module-toggle-card:focus,
        .store-card:focus,
        .access-type-card:focus {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        [data-theme="dark"] .module-toggle-card:focus,
        [data-theme="dark"] .store-card:focus,
        [data-theme="dark"] .access-type-card:focus {
            outline-color: var(--color-primary);
        }

        /* مؤشر التحميل المحسن */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        [data-theme="dark"] .loading-spinner {
            border-color: rgba(139, 148, 158, 0.3);
            border-top-color: var(--color-primary);
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* تحسين رسائل النجاح والخطأ */
        .success-message {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInDown 0.3s ease-out;
        }

        [data-theme="dark"] .success-message {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%);
            color: #4caf50;
            border-color: rgba(40, 167, 69, 0.3);
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInDown 0.3s ease-out;
        }

        [data-theme="dark"] .error-message {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.1) 100%);
            color: #f44336;
            border-color: rgba(220, 53, 69, 0.3);
        }

        @keyframes slideInDown {
            from {
                transform: translateY(-10px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* تحسين شكل محدد المستخدم */
        .user-selector select {
            background: var(--color-secondary);
            color: var(--color-fg);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .user-selector select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
            outline: none;
        }

        [data-theme="dark"] .user-selector select:focus {
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.2);
        }

        .user-selector select option {
            background: var(--color-secondary);
            color: var(--color-fg);
            padding: 10px;
        }

        /* تحسين العناوين */
        .permissions-container h2 {
            background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .permissions-container h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
            border-radius: 2px;
        }

        /* تحسين الأزرار */
        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(63, 81, 181, 0.4);
        }

        [data-theme="dark"] .btn-primary {
            box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
        }

        [data-theme="dark"] .btn-primary:hover {
            box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
        }

        /* تحسين الشب��ات */
        .modules-grid,
        .stores-grid {
            display: grid;
            gap: 20px;
            margin: 20px 0;
        }

        .modules-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .stores-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        @media (max-width: 768px) {
            .modules-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 15px;
            }

            .stores-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* تحسين الحالات الفارغة */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--color-fg);
            opacity: 0.7;
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--color-fg);
        }

        .empty-state p {
            font-size: 16px;
            line-height: 1.6;
            max-width: 400px;
            margin: 0 auto;
        }

        /* تحسينات إضافية للوضع المظلم */
        [data-theme="dark"] .cashier-info {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(52, 152, 219, 0.1) 100%);
            border-color: rgba(39, 174, 96, 0.3);
        }

        /* تحسين ألوان بطاقات دليل الكاشير في الوضع المظلم */
        [data-theme="dark"] .cashier-info > div > div {
            background: rgba(255, 255, 255, 0.05) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        /* تحسين ��لنصائح في الوضع المظلم */
        [data-theme="dark"] .cashier-info > div:last-child {
            background: rgba(52, 152, 219, 0.1) !important;
            border-color: rgba(52, 152, 219, 0.3) !important;
        }

        /* تحسين الشفافية للعناصر التفاعلية */
        .module-toggle-card,
        .store-card,
        .access-type-card {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        [data-theme="dark"] .module-toggle-card,
        [data-theme="dark"] .store-card,
        [data-theme="dark"] .access-type-card {
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* تحسين الظلال للوضع المظلم */
        [data-theme="dark"] .permissions-section,
        [data-theme="dark"] .user-selector,
        [data-theme="dark"] .tabs-container {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
        }

        /* تحسين التباين للنصوص */
        [data-theme="dark"] h3,
        [data-theme="dark"] h4,
        [data-theme="dark"] h5 {
            color: #f0f6fc;
        }

        /* تحسين الحدود في الوضع المظلم */
        [data-theme="dark"] .operations-section {
            border-top-color: #30363d;
        }

        [data-theme="dark"] .dashboard-section h4 {
            border-bottom-color: var(--color-primary);
        }

        /* تحسين مظهر القوائم المنسدلة */
        [data-theme="dark"] .user-selector select {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23c9d1d9' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 12px center;
            background-size: 16px;
            padding-left: 40px;
        }

        /* تحسين الانتقالات السلسة */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }

        .module-toggle-card *,
        .store-card *,
        .access-type-card *,
        .operation-item * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .page-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 3000 !important;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="container">
        <div class="permissions-container">
            <h2><i class="fas fa-user-shield"></i> إدارة الصلاحيات</h2>

            <div class="user-selector">
                <h3>اختيار المستخدم</h3>
                <select id="userSelect" class="input-field" style="width: 100%; max-width: 400px;">
                    <option value="">-- اختر مستخدم --</option>
                    <?php while ($user = $users_result->fetch_assoc()): ?>
                        <option value="<?php echo $user['account_id']; ?>" data-role="<?php echo $user['role']; ?>" data-access-type="<?php echo $user['access_type'] ?? 'admin_panel'; ?>">
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['username'] . ')'); ?>
                            - <?php echo htmlspecialchars($user['role']); ?>
                            <?php if (isset($user['access_type'])): ?>
                                (<?php echo $user['access_type'] === 'cashier_system' ? 'كاشير' : 'إداري'; ?>)
                            <?php endif; ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>

            <!-- تبويبات إدارة الصلاحيات -->
            <div class="tabs-container" id="tabsContainer" style="display: none;">
                <div class="tabs">
                    <button class="tab-button active" onclick="showTab('permissions')">
                        <i class="fas fa-key"></i> صلاحيات الوحدات
                    </button>
                    <button class="tab-button" onclick="showTab('stores')">
                        <i class="fas fa-store"></i> صلاحيات الفروع
                    </button>
                    <button class="tab-button" onclick="showTab('access_type')">
                        <i class="fas fa-desktop"></i> نوع الوصول
                    </button>
                </div>
            </div>

            <div id="permissionsContent">
                <div class="empty-state">
                    <i class="fas fa-user-shield"></i>
                    <h3>إدارة الصلاحيات</h3>
                    <p>اختر مستخدماً من القائمة أعلاه لبدء إدارة صلاحياته وتخصيص الوصول للوحدات والفروع المختلفة</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let currentUserId = null;
        let userPermissions = {};
        let userStores = [];
        let currentTab = 'permissions';
        let currentUserAccessType = 'cashier_system';

        document.getElementById('userSelect').addEventListener('change', function() {
            const userId = this.value;
            if (userId) {
                currentUserId = userId;
                document.getElementById('tabsContainer').style.display = 'block';
                loadUserData(userId);
            } else {
                currentUserId = null;
                document.getElementById('tabsContainer').style.display = 'none';
                showNoUserSelected();
            }
        });

        function showTab(tabName) {
            currentTab = tabName;

            // تحديث أزرار التبويبات
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // عرض المحتوى المناسب
            if (tabName === 'permissions') {
                renderPermissions();
            } else if (tabName === 'stores') {
                renderStores();
            } else if (tabName === 'access_type') {
                renderAccessType();
            }
        }

        function loadUserData(userId) {
            loadUserPermissions(userId);
            loadUserStores(userId);
            loadUserAccessType(userId);
        }

        function loadUserPermissions(userId) {
            // ��رض مؤشر التحميل
            if (currentTab === 'permissions') {
                document.getElementById('permissionsContent').innerHTML = `
                    <div class="empty-state">
                        <div class="loading-spinner"></div>
                        <h3>جاري تحميل الصلاحيات...</h3>
                        <p>يرجى الانتظار بينما نقوم بتحميل صلاحيات المستخدم</p>
                    </div>
                `;
            }

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_permissions&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userPermissions = data.permissions;
                    currentUserAccessType = data.access_type || 'cashier_system';
                    if (currentTab === 'permissions') {
                        renderPermissions();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في تحميل الصلاحيات',
                        text: data.message,
                        confirmButtonText: 'حسناً'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ أثناء تحميل الصلاحيات. يرجى المحاولة مرة أخرى.',
                    confirmButtonText: 'حسناً'
                });
            });
        }

        function loadUserStores(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_stores&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userStores = data.stores;
                    if (currentTab === 'stores') {
                        renderStores();
                    }
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الفروع', 'error');
            });
        }

        function renderPermissions() {
            const allModules = <?php
                $modules_result->data_seek(0);
                $modules_array = [];
                while ($module = $modules_result->fetch_assoc()) {
                    $modules_array[] = $module;
                }
                echo json_encode($modules_array);
            ?>;

            const permissions = <?php
                $permissions_result->data_seek(0);
                $permissions_array = [];
                while ($permission = $permissions_result->fetch_assoc()) {
                    $permissions_array[] = $permission;
                }
                echo json_encode($permissions_array);
            ?>;

            // تصفية الوحدات حسب نوع الوصول
            let modules = allModules;
            let systemTitle = 'النظام الإداري';
            let systemIcon = 'fas fa-cogs';
            let systemColor = '#3498db';

            if (currentUserAccessType === 'cashier_system') {
                // عرض وحدات نظام الكاشير فقط
                const cashierModuleNames = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
                modules = allModules.filter(module => cashierModuleNames.includes(module.module_name));
                systemTitle = 'نظام الكاشير';
                systemIcon = 'fas fa-cash-register';
                systemColor = '#27ae60';
            } else {
                // للنظام الإداري، عرض الوحدات الإدارية (استبعاد وحدات الكاشير)
                const cashierModuleNames = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
                const adminModuleNames = ['dashboard', 'store_management', 'reports', 'categories', 'items', 'purchase_invoices', 'wholesale_invoices', 'inventory', 'accounts', 'transfer_items', 'expired_items', 'expenses', 'shift_closures', 'balance_transfers', 'notifications', 'send_notifications'];
                modules = allModules.filter(module => adminModuleNames.includes(module.module_name));
            }

            let html = '<div class="permissions-section" style="padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += `<h3><i class="${systemIcon}"></i> إدارة صلاحيات ${systemTitle}</h3>`;
            html += `<p style="color: var(--text-secondary); margin-bottom: 20px;">اختر الوحدات والعمليات المسموحة للمستخدم في ${systemTitle}</p>`;

            // قسم لوحة التحكم - الوحدات الرئيسية
            html += '<div class="dashboard-section">';
            html += `<h4 style="color: ${systemColor}; margin-bottom: 15px; border-bottom: 2px solid ${systemColor}; padding-bottom: 5px;"><i class="${systemIcon}"></i> ${systemTitle} - الوحدات المتاحة</h4>`;
            html += '<div class="modules-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">';

            modules.forEach(module => {
                const hasAccess = userPermissions[module.module_name] && userPermissions[module.module_name]['access'];
                const moduleClass = hasAccess ? 'module-enabled' : 'module-disabled';

                html += `
                    <div class="module-toggle-card ${moduleClass}" onclick="toggleModuleAccess('${module.module_name}', !${hasAccess})">
                        <div class="module-icon">
                            <i class="${module.icon_class || 'fas fa-cog'}"></i>
                        </div>
                        <div class="module-name">${module.module_name_ar}</div>
                        <div class="module-status">
                            ${hasAccess ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل' : '<i class="fas fa-times-circle" style="color: #f44336;"></i> معطل'}
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';

            // قسم العمليات المتاحة للوحدات المفعلة
            const enabledModules = modules.filter(module =>
                userPermissions[module.module_name] && userPermissions[module.module_name]['access']
            );

            if (enabledModules.length > 0) {
                html += '<div class="operations-section">';
                html += '<h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;"><i class="fas fa-cogs"></i> العمليات المتاحة في الوحدات المفعلة</h4>';

                enabledModules.forEach(module => {
                    html += `
                        <div class="module-operations" style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 15px; border-left: 4px solid #4CAF50;">
                            <h5 style="color: #333; margin-bottom: 10px;">
                                <i class="${module.icon_class || 'fas fa-cog'}"></i> ${module.module_name_ar}
                            </h5>
                            <div class="operations-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                    `;

                    // تحديد العمليات المناسبة لكل وحدة
                    let allowedPermissions = [];
                    
                    if (currentUserAccessType === 'cashier_system') {
                        // تحديد العمليات المناسبة لكل وحدة في نظام الكاشير
                        switch(module.module_name) {
                            case 'cashier_home':
                                allowedPermissions = ['view', 'view_images', 'view_prices'];
                                break;
                            case 'cashier_invoices':
                                allowedPermissions = ['create_purchase', 'create_wholesale'];
                                break;
                            case 'cashier_shift_closure':
                                allowedPermissions = ['view'];
                                break;
                            case 'cashier_account':
                                allowedPermissions = ['view', 'edit_profile', 'switch_store'];
                                break;
                            default:
                                allowedPermissions = ['view'];
                        }
                    } else {
                        // للنظام الإداري، العمليات العامة (استبعاد عمليات الك��شير الخاصة)
                        // للنظام الإداري، العمليات الإدارية فقط (استبعاد جميع عمليات الكاشير)
                        // للنظام الإداري، تحديد العمليات المناسبة لكل وحدة
                        switch(module.module_name) {
                            case 'dashboard':
                                allowedPermissions = ['view', 'manage_all_stores', 'manage_specific_stores', 'add_store'];
                                break;
                            case 'store_management':
                                allowedPermissions = ['view'];
                                break;
                            case 'reports':
                                allowedPermissions = ['view', 'export_excel', 'comprehensive_report', 'view_queries'];
                                break;
                            case 'categories':
                                allowedPermissions = ['view', 'add_category', 'delete_category', 'edit_category', 'add_item_to_category', 'view_category_items'];
                                break;
                            case 'items':
                                allowedPermissions = ['view', 'add_item_from_items', 'edit_item', 'delete_item', 'change_item_status'];
                                break;
                            case 'purchase_invoices':
                                allowedPermissions = ['view', 'add_purchase_invoice', 'edit_purchase_invoice', 'delete_purchase_invoice', 'view_invoice_items', 'comprehensive_report', 'export_excel', 'confirm_invoice'];
                                break;
                            case 'wholesale_invoices':
                                allowedPermissions = ['view', 'add_wholesale_invoice', 'edit_wholesale_invoice', 'delete_wholesale_invoice', 'view_wholesale_items', 'comprehensive_report', 'export_excel', 'confirm_invoice'];
                                break;
                            case 'inventory':
                                allowedPermissions = ['view', 'add_inventory', 'open_inventory', 'open_inventory_details', 'delete_inventory'];
                                break;
                            case 'accounts':
                                allowedPermissions = ['view', 'add_account', 'edit_account', 'delete_account', 'change_account_status', 'manage_account_permissions'];
                                break;
                            case 'transfer_items':
                                allowedPermissions = ['access', 'transfer_permission'];
                                break;
                            case 'expired_items':
                                allowedPermissions = ['access', 'expiry_permission'];
                                break;
                            case 'expenses':
                                allowedPermissions = ['view', 'add_expense', 'edit_expense', 'delete_expense', 'comprehensive_report'];
                                break;
                            case 'shift_closures':
                                allowedPermissions = ['view', 'add_shift', 'edit_shift', 'delete_shift', 'confirm_shift', 'comprehensive_report'];
                                break;
                            case 'balance_transfers':
                                allowedPermissions = ['view', 'add_transfer', 'edit_transfer', 'delete_transfer', 'add_supplier', 'delete_supplier', 'comprehensive_report'];
                                break;
                            case 'notifications':
                                allowedPermissions = ['access', 'read_notifications'];
                                break;
                            case 'send_notifications':
                                allowedPermissions = ['access', 'send_notifications'];
                                break;
                            default:
                                allowedPermissions = ['view'];
                        }
                    }

                    // عرض العمليات المسموحة فقط
                    permissions.forEach(permission => {
                        if (!allowedPermissions.includes(permission.permission_name)) return;

                        const hasPermission = userPermissions[module.module_name] &&
                                            userPermissions[module.module_name][permission.permission_name];

                        html += `
                            <div class="operation-item" style="display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; border-radius: 6px; border: 1px solid var(--border-color);">
                                <span style="font-size: 14px;">${permission.permission_name_ar}</span>
                                <label class="permission-toggle" style="transform: scale(0.8);">
                                    <input type="checkbox"
                                           ${hasPermission ? 'checked' : ''}
                                           onchange="togglePermission('${module.module_name}', '${permission.permission_name}', this.checked)">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        `;
                    });

                    html += '</div></div>';
                });

                html += '</div>';
            } else {
                html += '<div style="text-align: center; padding: 40px; color: #999;">';
                html += '<i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 15px;"></i>';
                html += `<p>لم يتم تفعيل أي وحدة في ${systemTitle} بعد. يرجى تفعيل الوحدات المطلوبة أولاً.</p>`;
                html += '</div>';
            }

            // إضافة معلومات خاصة بنظام الكاشير
            if (currentUserAccessType === 'cashier_system') {
                html += '<div class="info-box cashier-info" style="margin-top: 20px;">';
                html += '<h4 style="color: #27ae60; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">';
                html += '<i class="fas fa-cash-register" style="font-size: 24px;"></i>';
                html += '<span>دليل نظام الكاشير</span>';
                html += '</h4>';
                html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">';
                
                // بطاقة الرئيسية
                html += '<div style="background: rgba(39, 174, 96, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #27ae60;">';
                html += '<div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">';
                html += '<i class="fas fa-home" style="color: #27ae60; font-size: 20px;"></i>';
                html += '<strong style="color: #27ae60;">الصفحة الرئيسية</strong>';
                html += '</div>';
                html += '<p style="margin: 0; color: var(--color-fg); font-size: 14px;">عرض الأصناف مع الصور والأسعار بواجهة سهلة الاستخدام</p>';
                html += '</div>';
                
                // بطاقة الفواتير
                html += '<div style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #3498db;">';
                html += '<div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">';
                html += '<i class="fas fa-file-invoice" style="color: #3498db; font-size: 20px;"></i>';
                html += '<strong style="color: #3498db;">إدارة الفواتير</strong>';
                html += '</div>';
                html += '<p style="margin: 0; color: var(--color-fg); font-size: 14px;">إنشاء فواتير الشراء والبيع بالجملة بسرعة وسهولة</p>';
                html += '</div>';
                
                // بطاقة الوردية
                html += '<div style="background: rgba(155, 89, 182, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #9b59b6;">';
                html += '<div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">';
                html += '<i class="fas fa-clock" style="color: #9b59b6; font-size: 20px;"></i>';
                html += '<strong style="color: #9b59b6;">إدارة الوردية</strong>';
                html += '</div>';
                html += '<p style="margin: 0; color: var(--color-fg); font-size: 14px;">عرض بيانات الوردية والمبيعات (عرض فقط)</p>';
                html += '</div>';
                
                // بطاقة الحساب
                html += '<div style="background: rgba(230, 126, 34, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #e67e22;">';
                html += '<div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">';
                html += '<i class="fas fa-user-cog" style="color: #e67e22; font-size: 20px;"></i>';
                html += '<strong style="color: #e67e22;">إدارة الحساب</strong>';
                html += '</div>';
                html += '<p style="margin: 0; color: var(--color-fg); font-size: 14px;">تعديل البيانات الشخصية والتبديل بين الفروع</p>';
                html += '</div>';
                
                html += '</div>';
                
                // نصائح إضافية
                html += '<div style="background: rgba(52, 152, 219, 0.05); padding: 15px; border-radius: 10px; margin-top: 15px; border: 1px dashed #3498db;">';
                html += '<h5 style="color: #3498db; margin-bottom: 10px; display: flex; align-items: center; gap: 8px;">';
                html += '<i class="fas fa-lightbulb"></i> نصائح مهمة';
                html += '</h5>';
                html += '<ul style="margin: 0; padding-right: 20px; color: var(--color-fg); font-size: 14px; line-height: 1.6;">';
                html += '<li>واجهة مبسطة ومصممة خصيصاً للكاشيرين</li>';
                html += '<li>عمليات سريعة ومباشرة بدون تعقيدات</li>';
                html += '<li>حماية من العمليات الحساسة (مثل الحذف والتعديل)</li>';
                html += '<li>تركيز على المبيعات والعمليات اليومية</li>';
                html += '</ul>';
                html += '</div>';
                
                html += '</div>';
            }

            html += '</div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function renderStores() {
            const stores = <?php
                $stores_result->data_seek(0);
                $stores_array = [];
                while ($store = $stores_result->fetch_assoc()) {
                    $stores_array[] = $store;
                }
                echo json_encode($stores_array);
            ?>;

            let html = '<div class="permissions-section" style="padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += '<h3><i class="fas fa-store"></i> إدارة صلاحيات الفروع</h3>';
            html += '<p style="color: var(--text-secondary); margin-bottom: 20px;">اختر الفروع التي يمكن للمستخدم الوصول إليها</p>';
            html += '<div class="stores-grid">';

            stores.forEach(store => {
                const hasAccess = userStores.includes(parseInt(store.store_id));
                const selectedClass = hasAccess ? 'selected' : '';

                html += `
                    <div class="store-card ${selectedClass}" onclick="toggleStoreAccess(${store.store_id}, !${hasAccess})">
                        <div class="store-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div style="font-weight: bold; margin-bottom: 5px;">${store.name}</div>
                        <div style="color: var(--text-secondary); font-size: 12px;">
                            ${hasAccess ? 'مسموح' : 'غير مسموح'}
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        let userAccessType = 'cashier_system';

        function loadUserAccessType(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_access_type&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userAccessType = data.access_type || 'cashier_system';
                    if (currentTab === 'access_type') {
                        renderAccessType();
                    }
                } else {
                    console.error('فشل في تحميل نوع الوصول:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        function renderAccessType() {
            let html = '<div class="permissions-section" style="padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += '<h3><i class="fas fa-desktop"></i> إدارة نوع الوصول للمستخدم</h3>';
            html += '<p style="color: var(--text-secondary); margin-bottom: 20px;">حدد نوع النظام الذي سيصل إليه المستخدم عند تسجيل الدخول</p>';

            html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">';

            // خيار النظام الإداري
            const isAdminPanel = userAccessType === 'admin_panel';
            const adminClass = isAdminPanel ? 'selected' : '';

            html += `
                <div class="access-type-card ${adminClass}" onclick="changeAccessType('admin_panel')">
                    <div class="access-type-icon">
                        <i class="fas fa-cogs" style="font-size: 48px; color: #3498db;"></i>
                    </div>
                    <div class="access-type-title">النظام الإداري</div>
                    <div class="access-type-description">
                        الوصول للوحة التحكم الإدارية مع جميع الوحدات والصلاحيات المخصصة للمستخدم
                    </div>
                    <div class="access-type-features">
                        <div><i class="fas fa-check"></i> إدارة الفواتير والأصناف</div>
                        <div><i class="fas fa-check"></i> التقارير والإحصائيات</div>
                        <div><i class="fas fa-check"></i> إدارة المخزون</div>
                    </div>
                    <div class="access-type-status">
                        ${isAdminPanel ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل حالياً' : '<i class="fas fa-circle" style="color: #ccc;"></i> غير مفعل'}
                    </div>
                </div>
            `;

            // خيار نظام الكاشير
            const isCashierSystem = userAccessType === 'cashier_system';
            const cashierClass = isCashierSystem ? 'selected' : '';

            html += `
                <div class="access-type-card ${cashierClass}" onclick="changeAccessType('cashier_system')">
                    <div class="access-type-icon">
                        <i class="fas fa-cash-register" style="font-size: 48px; color: #27ae60;"></i>
                    </div>
                    <div class="access-type-title">نظام الكاشير</div>
                    <div class="access-type-description">
                        واجهة مبسطة للكاشير مع التركيز على عمليات البيع والمبيعات اليومية
                    </div>
                    <div class="access-type-features">
                        <div><i class="fas fa-check"></i> واجهة سهلة الاستخدام</div>
                        <div><i class="fas fa-check"></i> عمليات البيع السريعة</div>
                        <div><i class="fas fa-check"></i> إدارة المبيعات اليومية</div>
                    </div>
                    <div class="access-type-status">
                        ${isCashierSystem ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل حالياً' : '<i class="fas fa-circle" style="color: #ccc;"></i> غير مفعل'}
                    </div>
                </div>
            `;

            html += '</div>';

            // معلومات إضافية
            html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; border-right: 4px solid #3498db;">';
            html += '<h4 style="color: #3498db; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> ملاحظات مهمة:</h4>';
            html += '<ul style="margin-right: 20px; line-height: 1.8;">';
            html += '<li><strong>النظام الإداري:</strong> مناسب للمديرين والموظفين الإداريين</li>';
            html += '<li><strong>نظام الكاشير:</strong> مناسب للكاشيرين والمستخدمين العاديين</li>';
            html += '<li><strong>تغيير النوع:</strong> يتطلب إعادة تسجيل دخول المستخدم ليصبح ساري المفعول</li>';
            html += '<li><strong>الصلاحيات:</strong> تطبق حسب نوع النظام المختار</li>';
            html += '</ul>';
            html += '</div>';

            html += '</div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function changeAccessType(accessType) {
            if (!currentUserId) return;

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=change_access_type&user_id=${currentUserId}&access_type=${accessType}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userAccessType = accessType;
                    currentUserAccessType = accessType;

                    // إعادة تحميل الصلاحيات لعرض الوحدات المناسبة
                    loadUserPermissions(currentUserId);

                    renderAccessType();

                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 2000,
                        toast: true,
                        position: 'top-end'
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تغيير نوع الوصول', 'error');
            });
        }

        function togglePermission(moduleName, permissionName, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_permission' : 'revoke_permission';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&module_name=${moduleName}&permission_name=${permissionName}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (!userPermissions[moduleName]) {
                        userPermissions[moduleName] = {};
                    }
                    userPermissions[moduleName][permissionName] = granted;

                    // إعادة عرض الصلاحيات إذا كانت صلاحية الوصول
                    if (permissionName === 'access') {
                        renderPermissions();
                    }

                    Swal.fire({
                        icon: 'success',
                        title: 'تم التحديث!',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 1500,
                        toast: true,
                        position: 'top-end'
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                    // إعادة تعيين الحالة السابقة
                    loadUserPermissions(currentUserId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث الصلاحية', 'error');
                // إعادة تعيين الحالة السابقة
                loadUserPermissions(currentUserId);
            });
        }

        function toggleModuleAccess(moduleName, granted) {
            if (!currentUserId) return;

            // تبديل صلاحية الوصول للوحدة
            togglePermission(moduleName, 'access', granted);
        }

        function toggleStoreAccess(storeId, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_store_access' : 'revoke_store_access';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&store_id=${storeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (granted) {
                        if (!userStores.includes(storeId)) {
                            userStores.push(storeId);
                        }
                    } else {
                        userStores = userStores.filter(id => id !== storeId);
                    }

                    // إعادة عرض الفروع
                    renderStores();

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث صلاحية الفرع', 'error');
            });
        }

        function showNoUserSelected() {
            document.getElementById('permissionsContent').innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-user-shield"></i>
                    <h3>إدارة الصلاحيات</h3>
                    <p>اختر مستخدماً من القائمة أعلاه لبدء إدارة صلاحياته وتخصيص الوصول للوحدات والفروع المختلفة</p>
                </div>
            `;
        }
    </script>
</body>
</html>
