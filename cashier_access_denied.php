<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ممنوع الوصول - نظام الكاشير</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .access-denied-container {
            background: white;
            border-radius: 20px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .access-denied-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        .error-icon {
            font-size: 80px;
            color: #e74c3c;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .error-title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .error-subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-right: 4px solid #e74c3c;
        }

        .error-details h4 {
            color: #e74c3c;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .error-details ul {
            text-align: right;
            margin-right: 20px;
            color: #555;
            line-height: 1.8;
        }

        .error-details li {
            margin-bottom: 8px;
        }

        .buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            min-width: 180px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .cashier-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-right: 4px solid #27ae60;
        }

        .cashier-info h4 {
            color: #27ae60;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .cashier-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            text-align: right;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-right: 3px solid #27ae60;
        }

        .feature-item i {
            color: #27ae60;
            margin-left: 10px;
            font-size: 18px;
        }

        .feature-item span {
            color: #2c3e50;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .access-denied-container {
                padding: 30px 20px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-subtitle {
                font-size: 16px;
            }

            .buttons-container {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="error-icon">
            <i class="fas fa-ban"></i>
        </div>
        
        <h1 class="error-title">ممنوع الوصول</h1>
        <p class="error-subtitle">ليس لديك صلاحية للوصول لهذه الصفحة في نظام الكاشير</p>
        
        <div class="error-details">
            <h4><i class="fas fa-info-circle"></i> أسباب منع الوصول:</h4>
            <ul>
                <li>قد تكون تحاول الوصول لصفحة غير مخصصة لدورك</li>
                <li>صلاحياتك في نظام الكاشير لا تشمل هذه العملية</li>
                <li>قد تحتاج لطلب صلاحيات إضافية من المدير</li>
                <li>تأكد من أنك تستخدم النظام الصحيح</li>
            </ul>
        </div>

        <div class="cashier-info">
            <h4><i class="fas fa-cash-register"></i> الصلاحيات المتاحة في نظام الكاشير:</h4>
            <div class="cashier-features">
                <div class="feature-item">
                    <i class="fas fa-home"></i>
                    <span>الصفحة الرئيسية</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-file-invoice"></i>
                    <span>إضافة الفواتير</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-cash-register"></i>
                    <span>تقفيل الوردية</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-user-cog"></i>
                    <span>إدارة الحساب</span>
                </div>
            </div>
        </div>

        <div class="buttons-container">
            <a href="users.php?account_id=<?= urlencode($_SESSION['account_id'] ?? '') ?>" class="btn btn-primary">
                <i class="fas fa-home"></i>
                العودة للرئيسية
            </a>
            
            <a href="user_account.php?account_id=<?= urlencode($_SESSION['account_id'] ?? '') ?>" class="btn btn-success">
                <i class="fas fa-user"></i>
                إدارة الحساب
            </a>
            
            <a href="logout.php" class="btn btn-secondary">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير الظهور التدريجي
            const container = document.querySelector('.access-denied-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(50px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
