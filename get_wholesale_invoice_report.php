<?php
/**
 * ملف جلب تقرير فواتير البيع بالجملة
 * يقوم بجلب إحصائيات شاملة عن فواتير البيع بالجملة للفرع المحدد
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية عرض التقارير الشاملة
if (!hasPermission('wholesale_invoices', 'comprehensive_report')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لعرض التقارير الشاملة']);
    exit();
}

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_GET['store_id'], $key);

$response = ['success' => false];

try {
    // جلب إحصائيات الفواتير حسب الحالة
    $stmt = $conn->prepare("SELECT status, COUNT(*) AS count, SUM(total_amount) AS total 
                            FROM wholesale_invoices 
                            WHERE store_id = ? 
                            GROUP BY status");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();

    $response['success'] = true;
    $response['total_invoices'] = 0;
    $response['total_amount'] = 0;
    $response['confirmed_count'] = 0;
    $response['confirmed_total'] = 0;
    $response['pending_count'] = 0;
    $response['pending_total'] = 0;

    while ($row = $result->fetch_assoc()) {
        $response['total_invoices'] += $row['count'];
        $response['total_amount'] += $row['total'];
        if (strtolower($row['status']) === 'confirmed') {
            $response['confirmed_count'] = $row['count'];
            $response['confirmed_total'] = $row['total'];
        } elseif (strtolower($row['status']) === 'pending') {
            $response['pending_count'] = $row['count'];
            $response['pending_total'] = $row['total'];
        }
    }

    // جلب إحصائيات إضافية خاصة بفواتير البيع بالجملة
    
    // عدد الفروع المشترية
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT buyer) AS buyer_stores_count 
                            FROM wholesale_invoices 
                            WHERE store_id = ? AND buyer REGEXP '^[0-9]+$'");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $buyer_data = $result->fetch_assoc();
    $response['buyer_stores_count'] = $buyer_data['buyer_stores_count'] ?? 0;
    $stmt->close();

    // عدد الأشخاص المشترين
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT account_id_buyer) AS buyer_persons_count 
                            FROM wholesale_invoices 
                            WHERE store_id = ? AND account_id_buyer IS NOT NULL");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $buyer_persons_data = $result->fetch_assoc();
    $response['buyer_persons_count'] = $buyer_persons_data['buyer_persons_count'] ?? 0;
    $stmt->close();

    // أكبر فاتورة
    $stmt = $conn->prepare("SELECT MAX(total_amount) AS max_invoice_amount 
                            FROM wholesale_invoices 
                            WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $max_data = $result->fetch_assoc();
    $response['max_invoice_amount'] = $max_data['max_invoice_amount'] ?? 0;
    $stmt->close();

    // متوسط قيمة الفاتورة
    $stmt = $conn->prepare("SELECT AVG(total_amount) AS avg_invoice_amount 
                            FROM wholesale_invoices 
                            WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $avg_data = $result->fetch_assoc();
    $response['avg_invoice_amount'] = round($avg_data['avg_invoice_amount'] ?? 0, 2);
    $stmt->close();

    // تنسيق الأرقام للعرض
    $response['total_amount'] = number_format($response['total_amount'], 2);
    $response['confirmed_total'] = number_format($response['confirmed_total'], 2);
    $response['pending_total'] = number_format($response['pending_total'], 2);
    $response['max_invoice_amount'] = number_format($response['max_invoice_amount'], 2);
    $response['avg_invoice_amount'] = number_format($response['avg_invoice_amount'], 2);

} catch (Exception $e) {
    $response['message'] = 'حدث خطأ أثناء جلب التقرير: ' . $e->getMessage();
}

header('Content-Type: application/json; charset=utf-8');
echo json_encode($response);
$conn->close();
?>