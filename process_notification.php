<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

// Check if user is logged in
if (!isset($_SESSION['account_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get store_id and account_id from session
$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';

if (!$encrypted_store_id || !$encrypted_account_id) {
    echo json_encode(['success' => false, 'message' => 'Session data missing']);
    exit;
}

$store_id = decrypt($encrypted_store_id, $key);
$account_id = decrypt($encrypted_account_id, $key);

// Check if user has admin privileges
$role_query = "SELECT role FROM accounts WHERE account_id = ?";
$role_stmt = $conn->prepare($role_query);
$role_stmt->bind_param("i", $account_id);
$role_stmt->execute();
$role_result = $role_stmt->get_result();
$role_data = $role_result->fetch_assoc();

if (!$role_data || $role_data['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get notification data from POST
$title = isset($_POST['title']) ? $_POST['title'] : '';
$message = isset($_POST['message']) ? $_POST['message'] : '';
$sendToAll = isset($_POST['sendToAll']) ? filter_var($_POST['sendToAll'], FILTER_VALIDATE_BOOLEAN) : true;

if (empty($title) || empty($message)) {
    echo json_encode(['success' => false, 'message' => 'Title and message are required']);
    exit;
}

// Insert notification into database
$insert_query = "INSERT INTO notifications (message, store_id, created_by) VALUES (?, ?, ?)";
$insert_stmt = $conn->prepare($insert_query);
$insert_stmt->bind_param("sii", $message, $store_id, $account_id);

if (!$insert_stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Failed to save notification to database']);
    exit;
}

$notification_id = $insert_stmt->insert_id;

// Get FCM tokens for the store
$tokens_query = $sendToAll
    ? "SELECT token FROM fcm_tokens WHERE store_id = ?"
    : "SELECT token FROM fcm_tokens WHERE store_id = ? AND account_id = ?";

$tokens_stmt = $conn->prepare($tokens_query);

if ($sendToAll) {
    $tokens_stmt->bind_param("i", $store_id);
} else {
    $tokens_stmt->bind_param("ii", $store_id, $account_id);
}

$tokens_stmt->execute();
$tokens_result = $tokens_stmt->get_result();

$tokens = [];
while ($row = $tokens_result->fetch_assoc()) {
    $tokens[] = $row['token'];
}

if (empty($tokens)) {
    echo json_encode([
        'success' => true,
        'message' => 'Notification saved to database, but no FCM tokens found to send push notifications'
    ]);
    exit;
}

// Use Firebase Admin SDK with service account
// Note: We're using a custom JWT implementation instead of requiring the Firebase Admin SDK
// This avoids the need for Composer dependencies

// Path to service account key file
$serviceAccountPath = __DIR__ . '/firebase-adminsdk.json';

// Check if service account key file exists
if (!file_exists($serviceAccountPath)) {
    echo json_encode([
        'success' => false,
        'message' => 'Service account key file not found at: ' . $serviceAccountPath
    ]);
    exit;
}

// Include the OAuth 2.0 access token helper
require_once 'get_access_token.php';

try {
    // Get OAuth 2.0 access token
    $accessTokenInfo = getAccessToken($serviceAccountPath);
    $accessToken = $accessTokenInfo['token'];

    // Get project ID from service account file
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

    // Check if service account file is valid
    if (!isset($serviceAccount['project_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid service account file. Missing project_id field.',
            'debug' => [
                'file_exists' => file_exists($serviceAccountPath),
                'file_size' => filesize($serviceAccountPath),
                'file_content_sample' => substr(file_get_contents($serviceAccountPath), 0, 100) . '...',
                'json_last_error' => json_last_error_msg()
            ]
        ]);
        exit;
    }

    // Prepare FCM message
    $fcmMessage = [
        'message' => [
            'token' => $tokens[0], // Send to first token (we'll loop through all tokens below)
            'notification' => [
                'title' => $title,
                'body' => $message
            ],
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ],
                'notification' => [
                    'title' => $title,
                    'body' => $message,
                    'icon' => 'https://' . $_SERVER['HTTP_HOST'] . '/uploads/img/logo2.png',
                    'badge' => 'https://' . $_SERVER['HTTP_HOST'] . '/uploads/img/logo2.png',
                    'click_action' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php',
                    'tag' => 'elwaled-notification',
                    'requireInteraction' => false,
                    'silent' => false,
                    'vibrate' => [200, 100, 200]
                ],
                'fcm_options' => [
                    'link' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php'
                ]
            ],
            'data' => [
                'notification_id' => (string)$notification_id
            ]
        ]
    ];

    $url = 'https://fcm.googleapis.com/v1/projects/' . $serviceAccount['project_id'] . '/messages:send';
    $headers = [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ];

    // Initialize success counter
    $successCount = 0;
    $failureCount = 0;
    $errors = [];
    $detailedError = [];

    // Send notification to each token
    foreach ($tokens as $token) {
        // Update token in message
        $fcmMessage['message']['token'] = $token;

        // Send request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmMessage));

        // Enable verbose debugging
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);

        $result = curl_exec($ch);

        // Get verbose information
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);

        // Get HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($result === false) {
            $failureCount++;
            $errors[] = 'cURL error: ' . curl_error($ch);

            // Save detailed error information for the first error
            if (empty($detailedError)) {
                $detailedError = [
                    'verbose_log' => $verboseLog,
                    'http_code' => $httpCode,
                    'url' => $url,
                    'access_token_sample' => substr($accessToken, 0, 20) . '...',
                    'token' => $token
                ];
            }
        } else {
            $response = json_decode($result, true);
            if (isset($response['name'])) {
                $successCount++;
            } else {
                $failureCount++;
                $errorMessage = isset($response['error']) ? $response['error']['message'] : 'Unknown error';
                $errors[] = $errorMessage;

                // Save detailed error information for the first error
                if (empty($detailedError)) {
                    $detailedError = [
                        'verbose_log' => $verboseLog,
                        'http_code' => $httpCode,
                        'response' => $response,
                        'request' => [
                            'url' => $url,
                            'token' => $token,
                            'message' => $message
                        ],
                        'error' => $errorMessage
                    ];
                }
            }
        }

        curl_close($ch);
    }

    if ($successCount > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Notification sent successfully to ' . $successCount . ' devices' .
                        ($failureCount > 0 ? ' (Failed: ' . $failureCount . ')' : '')
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to send notifications: ' . implode(', ', $errors),
            'debug' => $detailedError
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

// JWT function is now in get_access_token.php

$insert_stmt->close();
$tokens_stmt->close();
$conn->close();
?>
