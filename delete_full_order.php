<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $account_id = decrypt($data['account_id'], getenv('ENCRYPTION_KEY'));
    $order_date = $data['order_date'];

    // Fetch all items in the order
    $stmt = $conn->prepare("SELECT sale_id, item_id, quantity, status FROM sales WHERE account_id = ? AND DATE(time) = ?");
    $stmt->bind_param("is", $account_id, $order_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $sale_id = $row['sale_id'];
        $item_id = $row['item_id'];
        $quantity = $row['quantity'];
        $status = $row['status'];

        if ($status == 'pending') {
            // Delete the item directly
            $stmt_delete = $conn->prepare("DELETE FROM sales WHERE sale_id = ?");
            $stmt_delete->bind_param("i", $sale_id);
            $stmt_delete->execute();
            $stmt_delete->close();
        } else {
            // Update item quantity
            $stmt_update = $conn->prepare("UPDATE items SET quantity = quantity + ? WHERE item_id = ?");
            $stmt_update->bind_param("ii", $quantity, $item_id);
            $stmt_update->execute();
            $stmt_update->close();

            if ($status == 'confirmed') {
                // Delete from item transactions
                $stmt_delete_tx = $conn->prepare("DELETE FROM itemtransactions WHERE transaction_id_ref = ?");
                $stmt_delete_tx->bind_param("i", $sale_id);
                $stmt_delete_tx->execute();
                $stmt_delete_tx->close();
            }

            // Delete the item
            $stmt_delete = $conn->prepare("DELETE FROM sales WHERE sale_id = ?");
            $stmt_delete->bind_param("i", $sale_id);
            $stmt_delete->execute();
            $stmt_delete->close();
        }
    }

    $stmt->close();
    $conn->close();

    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
