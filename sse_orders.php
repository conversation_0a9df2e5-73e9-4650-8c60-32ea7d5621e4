<?php
include 'db_connection.php';
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

$store_id = isset($_GET['store_id']) ? decrypt($_GET['store_id'], $key) : null;

$sql = "SELECT COUNT(*) AS order_count FROM sales WHERE status = 'pending'";
if ($store_id) {
    $sql .= " AND store_id = ?";
}

$stmt = $conn->prepare($sql);
if ($store_id) {
    $stmt->bind_param("i", $store_id);
}
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();
$order_count = $row['order_count'];

$stmt->close();
$conn->close();

echo "data: " . json_encode(['order_count' => $order_count]) . "\n\n";
flush();
?>
