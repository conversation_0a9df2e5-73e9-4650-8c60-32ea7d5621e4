<?php
/**
 * توليد تقرير تحويلات الرصيد - مع فحص الصلاحيات
 * الصلاحية المطلوبة: comprehensive_report في وحدة balance_transfers
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

// فحص صلاحية عرض التقارير
if (!hasPermission('balance_transfers', 'comprehensive_report')) {
    echo json_encode(['success' => false, 'error' => 'ليس لديك صلاحية لعرض التقارير']);
    exit();
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;

if (!$encrypted_store_id) {
    echo json_encode(['success' => false, 'error' => 'Store ID is missing or invalid.']);
    exit();
}

$store_id = $encrypted_store_id ? decrypt($encrypted_store_id, $key) : null;

if (!$store_id) {
    echo json_encode(['success' => false, 'error' => 'Store ID is missing, invalid, or decryption failed.']);
    exit();
}


$stmt = $conn->prepare("SELECT provider, cost, sale_price, value FROM balance_transfers WHERE store_id = ?");
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

$total_value = 0;
$total_profit = 0;
$provider_data = [];

while ($row = $result->fetch_assoc()) {
    $total_value += $row['value'];
    $profit = ($row['sale_price'] - $row['cost']) * $row['value'];
    $total_profit += $profit;

    $provider = $row['provider'];
    if (!isset($provider_data[$provider])) {
        $provider_data[$provider] = ['count' => 0, 'profit' => 0];
    }
    $provider_data[$provider]['count']++;
    $provider_data[$provider]['profit'] += $profit;
}

$stmt->close();

// Format provider data
$providers = [];
foreach ($provider_data as $provider => $data) {
    $providers[] = [
        'name' => $provider,
        'count' => $data['count'],
        'profit' => number_format($data['profit'], 2)
    ];
}

echo json_encode([
    'success' => true,
    'total_value' => number_format($total_value, 2),
    'total_profit' => number_format($total_profit, 2),
    'providers' => $providers
]);

$conn->close();
?>
