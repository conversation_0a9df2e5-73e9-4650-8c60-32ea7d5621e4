@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Cairo", sans-serif;
}

body {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    /* Change background to white */
    direction: rtl;
    /* Add RTL support */
}

.box {
    position: relative;
    width: 380px;
    height: 400px;
    background: #f0f0f0;
    /* Change background to light grey */
    border-radius: 10px;
    overflow: hidden;
}

.box::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 380px;
    height: 400px;
    background: linear-gradient(0deg, transparent, transparent, #00bfff, #00bfff, #00bfff);
    /* Lighter blue */
    z-index: 1;
    transform-origin: bottom right;
    animation: animate 6s linear infinite;
}

.box::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 380px;
    height: 400px;
    background: linear-gradient(0deg, transparent, transparent, #00bfff, #00bfff, #00bfff);
    /* Lighter blue */
    z-index: 1;
    transform-origin: bottom right;
    animation: animate 6s linear infinite;
    animation-delay: -3s;
}

.border-line {
    position: absolute;
    top: 0;
    inset: 0;
}

.border-line::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 380px;
    height: 400px;
    background: linear-gradient(0deg, transparent, transparent, #ff69b4, #ff69b4, #ff69b4);
    /* Lighter pink */
    z-index: 1;
    transform-origin: bottom right;
    animation: animate 6s linear infinite;
    animation-delay: -1.5s;
}

.border-line::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 380px;
    height: 400px;
    background: linear-gradient(0deg, transparent, transparent, #ff69b4, #ff69b4, #ff69b4);
    /* Lighter pink */
    z-index: 1;
    transform-origin: bottom right;
    animation: animate 6s linear infinite;
    animation-delay: -4.5s;
}

@keyframes animate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.box form {
    position: absolute;
    inset: 4px;
    background: #ffffff;
    /* Change background to white */
    padding: 50px 40px;
    border-radius: 10px;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

.box form h2 {
    color: #333;
    /* Darker text color */
    font-size: 26px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 1px;
}

.input-box {
    position: relative;
    width: 300px;
    margin-top: 20px;
}

.input-box input {
    position: relative;
    width: 100%;
    padding: 20px 10px 10px;
    background: transparent;
    border: none;
    outline: none;
    box-shadow: none;
    color: #ffffff;
    /* Darker text color */
    font-size: 16px;
    letter-spacing: 1px;
    transition: 0.5s;
    z-index: 10;
}

.input-box input::placeholder {
    color: #999;
    /* Lighter placeholder text color */
}

.input-box span {
    position: absolute;
    right: 0;
    /* Align text to the right */
    padding: 20px 0px 10px;
    pointer-events: none;
    color: #666;
    /* Lighter grey */
    font-size: 16px;
    letter-spacing: 1px;
    transition: 0.5s;
    z-index: 10;
}

.input-box input:valid~span,
.input-box input:focus~span {
    color: #333;
    /* Darker text color */
    font-size: 12px;
    transform: translateY(-34px);
}

.input-box i {
    position: absolute;
    right: 0;
    /* Align to the right */
    bottom: 0;
    width: 100%;
    height: 2px;
    background: #00bfff;
    /* Darker line color */
    border-radius: 4px;
    overflow: hidden;
    transition: 0.5s;
    pointer-events: none;
}

.input-box input:valid~i,
.input-box input:focus~i {
    height: 44px;
}

.imp-links {
    display: flex;
    justify-content: center;
    /* Center the link */
}

.imp-links a {
    color: #666;
    /* Lighter grey */
    font-size: 14px;
    text-decoration: none;
    margin: 15px 0;
}

.imp-links a:hover {
    color: #333;
    /* Darker text color */
}

.btn {
    width: 50%;
    border: none;
    outline: none;
    padding: 10px;
    border-radius: 45px;
    font-size: 16px;
    font-weight: 800;
    letter-spacing: 1px;
    margin-top: 10px;
    cursor: pointer;
    background-color: #00bfff;
    /* Lighter blue */
    color: #fff;
    /* White text */
}

.btn:active {
    opacity: 0.8;
}