<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';
$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_account_id'])) {
    $delete_account_id = $_POST['delete_account_id'];

    // Delete the account
    $stmt = $conn->prepare("DELETE FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $delete_account_id);
    $stmt->execute();
    $stmt->close();

    // Return JSON response for deletion
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => true]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['accept_account_id'])) {
    $accept_account_id = decrypt($_POST['accept_account_id'], $key);
    $store_id = $_POST['store_id'];
    $status = 'active';

    $stmt = $conn->prepare("UPDATE accounts SET store_id = ?, status = ? WHERE account_id = ?");
    $stmt->bind_param("isi", $store_id, $status, $accept_account_id);
    if ($stmt->execute()) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => true]);
    } else {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'message' => $stmt->error]);
    }
    $stmt->close();
    exit();
}

$sql = "SELECT * FROM accounts WHERE status = 'requested'";
$result = $conn->query($sql);

$stores_sql = "SELECT * FROM stores";
$stores_result = $conn->query($stores_sql);
$stores = [];
if ($stores_result->num_rows > 0) {
    while($store = $stores_result->fetch_assoc()) {
        $stores[] = $store;
    }
}
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : null;
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات المطلوبة</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        #sidebar ul li.branch {
    padding: 10px 15px;
    background-color: #2c3e50; /* لون خلفية مميز */
    color: #ecf0f1;
    border-radius: 5px;
    margin: 10px 0;
  }
  .branch-container {
    display: flex;
    align-items: center;
  }
  .branch-icon {
    margin-right: 8px;
  }
  .branch-text {
    font-size: 1.1rem;
    font-weight: bold;
  }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container">
    <h2>إدارة طلبات الحسابات </h2>
    
    <table>
        <thead>
            <tr>
                <th>اسم المستخدم</th>
                <th>الدور</th>
                <th>الاسم</th>
                <th>الهاتف</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php
            if ($result->num_rows > 0) {
                while($row = $result->fetch_assoc()) {
                    $encrypted_account_id = encrypt($row['account_id'], $key);
                    echo "<tr id='account-{$row['account_id']}'>
                            <td>{$row['username']}</td>
                            <td>{$row['role']}</td>
                            <td>{$row['name']}</td>
                            <td>{$row['phone']}</td>
                            <td><div class='status orange'>طلب</div></td>
                            <td>
                                <button class='action-btn' type='button' onclick='acceptAccount(\"" . htmlspecialchars($encrypted_account_id) . "\")'>
                                    <i class='fas fa-check'></i>
                                </button>
                                <button class='action-btn' type='button' onclick='deleteAccount(" . htmlspecialchars($row['account_id']) . ")'>
                                    <i class='fas fa-trash-alt'></i>
                                </button>
                            </td>
                          </tr>";
                }
            } else {
                echo "<tr><td colspan='6'>لا توجد حسابات مطلوبة حالياً</td></tr>";
            }
            ?>
        </tbody>
    </table>
</div>

<div id="acceptAccountModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>قبول الحساب</h2>
        <form id="acceptForm" method="POST" action="">
            <input type="hidden" name="accept_account_id" id="accept_account_id">
            <select name="store_id" id="accept_store_id" class="input-field" required>
                <?php
                foreach ($stores as $store) {
                    echo "<option value='{$store['store_id']}'>{$store['name']}</option>";
                }
                ?>
            </select>
            <button type="submit" class="add-btn">تأكيد</button>
        </form>
    </div>
</div>

<script>
    var acceptAccountModal = document.getElementById("acceptAccountModal");
    var acceptAccountSpan = document.getElementsByClassName("close")[0];

    acceptAccountSpan.onclick = function() {
        acceptAccountModal.classList.remove("active");
    }

    window.onclick = function(event) {
        if (event.target.classList.contains("modal")) {
            event.target.classList.remove("active");
        }
    }

    function acceptAccount(encryptedAccountId) {
        document.getElementById('accept_account_id').value = encryptedAccountId;
        acceptAccountModal.classList.add("active");
    }

    function deleteAccount(accountId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفه!'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('delete_account_id', accountId);

                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`account-${accountId}`).remove();
                        Swal.fire(
                            'تم الحذف!',
                            'تم حذف الحساب بنجاح.',
                            'success'
                        );
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء حذف الحساب.'
                    });
                });
            }
        });
    }

    document.getElementById('acceptForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        fetch('', { method: 'POST', body: formData })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم قبول الحساب بنجاح',
                    timer: 2000
                }).then(() => location.reload());
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.message
                });
            }
        })
        .catch(err => console.error(err));
    });
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
