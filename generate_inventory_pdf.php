<?php
require_once __DIR__ . '/vendor/autoload.php'; // Use Composer autoloader
require_once 'db_connection.php';
require_once 'auth_check.php';
require_once 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$inventory_id = decrypt($_GET['inventory_id'], $key);

// Fetch inventory details
$stmt = $conn->prepare("SELECT * FROM monthly_inventory WHERE inventory_id = ?");
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$inventory = $stmt->get_result()->fetch_assoc();
$stmt->close();

// Fetch store name
$stmt = $conn->prepare("SELECT s.name AS store_name FROM stores s JOIN monthly_inventory mi ON s.store_id = mi.store_id WHERE mi.inventory_id = ?");
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$stmt->bind_result($store_name);
$stmt->fetch();
$stmt->close();

// Fetch items
$stmt = $conn->prepare("SELECT * FROM items WHERE store_id = ?");
$stmt->bind_param("i", $inventory['store_id']);
$stmt->execute();
$items_result = $stmt->get_result();
$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}
$stmt->close();

// Create PDF
$pdf = new \TCPDF(); // Use the TCPDF class from the Composer autoloader
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Inventory System');
$pdf->SetTitle('اصناف الجرد');
$pdf->SetHeaderData('', 0, 'اصناف الجرد', "الفرع: $store_name\nالتاريخ: " . date('Y-m-d'));
$pdf->setHeaderFont(['aealarabiya', '', 12]); // Change font to 'aealarabiya'
$pdf->setFooterFont(['aealarabiya', '', 10]);
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(15, 27, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);
$pdf->SetAutoPageBreak(TRUE, 25);
$pdf->setFontSubsetting(true);
$pdf->SetFont('aealarabiya', '', 12); // Change font to 'aealarabiya'
$pdf->AddPage();

// Table content
$html = '<h3 style="text-align: center;">اصناف الجرد</h3>';
$html .= '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%; text-align: center;">';
$html .= '<thead>
            <tr style="background-color: #f2f2f2;">
                <th>الكمية المتبقية</th>
                <th>الكمية الحالية</th>
                <th>سعر جملة الصنف</th>
                <th>اسم الصنف</th>
            </tr>
          </thead>';
$html .= '<tbody>';
foreach ($items as $item) {
    $html .= '<tr>
                <td></td> <!-- Leave "الكمية المتبقية" empty -->
                <td>' . htmlspecialchars($item['quantity']) . '</td>
                <td>' . htmlspecialchars($item['cost']) . '</td>
                <td>' . htmlspecialchars($item['name']) . '</td>
              </tr>';
}
$html .= '</tbody></table>';

// Log the PDF generation action
session_start();
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');
$account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
            VALUES (?, 'export', 'monthly_inventory', ?)";
$description = "تم إصدار ملف PDF للجرد الخاص بالفرع $store_name";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("is", $account_id, $description);
$log_stmt->execute();
$log_stmt->close();

// Output PDF
$pdf->writeHTML($html, true, false, true, false, '');
$pdf->Output('inventory_details.pdf', 'D');
?>
