<?php
session_start();
include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_POST['store_id'], $key);
$invoice_id = decrypt($_POST['invoice_id'], $key);
$account_id = decrypt($_SESSION['account_id'], $key);

$total_amount = $_POST['total_amount'];

$response = [];

$conn->begin_transaction();

try {
    $stmt = $conn->prepare("UPDATE purchase_invoices SET total_amount = ? WHERE invoice_id = ?");
    $stmt->bind_param("di", $total_amount, $invoice_id);
    $stmt->execute();
    $stmt->close();

    // Fetch the store name
    $stmt = $conn->prepare("SELECT s.name AS store_name FROM stores s JOIN purchase_invoices pi ON s.store_id = pi.store_id WHERE pi.invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();

    // Check if the invoice status is Confirmed
    $stmt = $conn->prepare("SELECT status FROM purchase_invoices WHERE invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    $stmt->close();

    if ($invoice['status'] === 'Confirmed') {
        // Fetch existing purchases to adjust item quantities
        $stmt = $conn->prepare("SELECT item_id, quantity FROM purchases WHERE invoice_id = ?");
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();
        $existing_purchases = $stmt->get_result();
        $stmt->close();

        // Adjust item quantities based on existing purchases
        while ($purchase = $existing_purchases->fetch_assoc()) {
            $stmt = $conn->prepare("UPDATE items SET quantity = quantity - ? WHERE item_id = ?");
            $stmt->bind_param("di", $purchase['quantity'], $purchase['item_id']);
            $stmt->execute();
            $stmt->close();
        }
    }

    $stmt = $conn->prepare("DELETE FROM purchases WHERE invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $stmt->close();

    if (isset($_POST['items']) && is_array($_POST['items'])) {
        foreach ($_POST['items'] as $item_id => $item) {
            $quantity = floatval($item['quantity']);
            $cost = isset($item['cost']) ? floatval($item['cost']) : 0; // Ensure cost is set
            $total = $quantity * $cost;

            $stmt = $conn->prepare("INSERT INTO purchases (invoice_id, store_id, item_id, quantity, total_amount) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("iiidd", $invoice_id, $store_id, $item_id, $quantity, $total);
            $stmt->execute();
            $purchase_id = $stmt->insert_id;
            $stmt->close();

            $stmt = $conn->prepare("INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'purchase', ?, ?)");
            $stmt->bind_param("iid", $item_id, $purchase_id, $quantity);
            $stmt->execute();
            $stmt->close();

            // Update the quantity in the items table only if the invoice is Confirmed
            if ($invoice['status'] === 'Confirmed') {
                $stmt = $conn->prepare("UPDATE items SET quantity = quantity + ? WHERE item_id = ?");
                $stmt->bind_param("di", $quantity, $item_id);
                $stmt->execute();
                $stmt->close();
            }
        }
    }

    if (!empty($_POST['removed_images'])) {
        $removedImages = explode(',', rtrim($_POST['removed_images'], ','));
        foreach ($removedImages as $imgPath) {
            $stmt = $conn->prepare("DELETE FROM invoice_images WHERE purchase_invoice_id = ? AND img_path = ?");
            $stmt->bind_param("is", $invoice_id, $imgPath);
            $stmt->execute();
            $stmt->close();
            if (file_exists($imgPath)) {
                unlink($imgPath); // Delete the image file from the server
            }
        }
    }

    if (!empty($_FILES['invoice_images']['name'][0])) {
        $targetDir = "uploads/invoices/";

        // Ensure the directory exists and is writable
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true); // Create the directory with proper permissions
        }

        foreach ($_FILES['invoice_images']['tmp_name'] as $index => $tmpName) {
            $fileName = basename($_FILES['invoice_images']['name'][$index]);
            $targetFile = $targetDir . uniqid() . "_" . $fileName;

            if (move_uploaded_file($tmpName, $targetFile)) {
                $stmt = $conn->prepare("INSERT INTO invoice_images (purchase_invoice_id, img_path) VALUES (?, ?)");
                $stmt->bind_param("is", $invoice_id, $targetFile);
                $stmt->execute();
                $stmt->close();
            } else {
                error_log("Failed to move uploaded file: $tmpName to $targetFile");
            }
        }
    }

    // Log the invoice update action
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'update', 'purchase_invoices', ?, ?)";
    $description = "تم تحديث الفاتورة رقم $invoice_id بقيمة جديدة $total_amount في الفرع $store_name";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    $conn->commit();
    $response['success'] = true;
    $response['message'] = 'تم تحديث الفاتورة بنجاح.';
} catch (Exception $e) {
    $conn->rollback();
    $response['success'] = false;
    $response['message'] = 'فشل في تحديث الفاتورة: ' . $e->getMessage();
}

$conn->close();
header('Content-Type: application/json');
echo json_encode($response);
?>
