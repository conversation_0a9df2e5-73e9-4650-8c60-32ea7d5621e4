// Service Worker for Firebase Cloud Messaging with COMPLETE NO CACHE policy
const CACHE_VERSION = 'v2'; // Updated cache version
const CACHE_NAME = 'elwaled-market-no-cache-' + CACHE_VERSION;
const OFFLINE_URL = '/offline.html';

// ONLY essential files for offline functionality (very minimal)
const ESSENTIAL_CACHE_FILES = [
  '/offline.html',
  '/sounds/notification.mp3'
];

// URLs that should bypass service worker completely (external resources)
const BYPASS_URLS = [
  'fonts.googleapis.com',
  'fonts.gstatic.com',
  'cdn.jsdelivr.net',
  'www.gstatic.com'
];

// NO CACHE for EVERYTHING else - complete bypass

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js');

// Firebase configuration
firebase.initializeApp({
  apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
  authDomain: "macm-84114.firebaseapp.com",
  projectId: "macm-84114",
  storageBucket: "macm-84114.firebasestorage.app",
  messagingSenderId: "860043675105",
  appId: "1:860043675105:web:72586005d5bd035ff8bea0"
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[Service Worker] Received background message ', payload);

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icon.png',
    data: payload.data
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Helper function to check if URL should be cached - COMPLETE NO CACHE POLICY
function shouldCache(url) {
  // ONLY cache essential offline files (very minimal)
  if (ESSENTIAL_CACHE_FILES.some(pattern => url.includes(pattern))) {
    return true;
  }

  // NO CACHE for EVERYTHING else - including CSS, JS, images, PHP files, etc.
  return false;
}

// Install event - cache only essential files
self.addEventListener('install', event => {
  console.log('[Service Worker] Installing NO-CACHE Service Worker...');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[Service Worker] Caching ESSENTIAL files only');
        // Cache only essential files individually with proper error handling
        return Promise.allSettled(
          ESSENTIAL_CACHE_FILES.map(url => {
            // Create proper request for relative URLs
            const request = new Request(url, {
              mode: 'same-origin',
              credentials: 'same-origin'
            });

            return cache.add(request).catch(error => {
              console.warn('[Service Worker] Failed to cache essential file:', url, error);
              // Try to fetch and cache manually
              return fetch(request).then(response => {
                if (response.ok) {
                  return cache.put(request, response);
                }
                throw new Error(`Failed to fetch ${url}: ${response.status}`);
              }).catch(fetchError => {
                console.warn('[Service Worker] Manual fetch also failed for:', url, fetchError);
                return null;
              });
            });
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] NO-CACHE Service Worker installed successfully');
        console.log('[Service Worker] Only essential files cached:', ESSENTIAL_CACHE_FILES);
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[Service Worker] Installation failed:', error);
        return self.skipWaiting();
      })
  );
});

// Activate event - aggressively clean up ALL old caches
self.addEventListener('activate', event => {
  console.log('[Service Worker] Activating NO-CACHE Service Worker...');

  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        console.log('[Service Worker] Found caches:', cacheNames);
        return Promise.all(
          cacheNames.filter(cacheName => {
            // Delete all caches except the current one
            return cacheName !== CACHE_NAME;
          }).map(cacheName => {
            console.log('[Service Worker] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] All old caches deleted');
        console.log('[Service Worker] Current cache:', CACHE_NAME);
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
      .then(() => {
        // Force reload all clients to ensure no cached content
        return self.clients.matchAll().then(clients => {
          clients.forEach(client => {
            console.log('[Service Worker] Sending reload message to client');
            client.postMessage({
              type: 'CACHE_CLEARED',
              message: 'Service Worker cache cleared, please refresh if needed'
            });
          });
        });
      })
  );
});

// Fetch event - COMPLETE NO CACHE policy for ALL files
self.addEventListener('fetch', event => {
  // Only handle GET requests
  if (event.request.method !== 'GET') return;

  const requestUrl = event.request.url;

  // Check if this is an external resource that should bypass service worker
  const shouldBypass = BYPASS_URLS.some(domain => requestUrl.includes(domain));

  if (shouldBypass) {
    console.log('[Service Worker] Bypassing external resource:', requestUrl);
    // Let the browser handle external resources directly without interference
    return;
  }

  console.log('[Service Worker] COMPLETE NO CACHE - Processing request for:', requestUrl);

  // Check if this is an essential offline file
  const isEssentialFile = ESSENTIAL_CACHE_FILES.some(pattern => requestUrl.includes(pattern));

  if (isEssentialFile) {
    console.log('[Service Worker] Essential offline file - checking cache:', requestUrl);

    // Only for essential offline files, use cache
    event.respondWith(
      caches.match(event.request)
        .then(cachedResponse => {
          if (cachedResponse) {
            console.log('[Service Worker] Serving essential file from cache:', requestUrl);
            return cachedResponse;
          }

          // Fetch and cache essential file
          return fetch(event.request)
            .then(response => {
              if (response && response.status === 200) {
                const responseToCache = response.clone();
                caches.open(CACHE_NAME)
                  .then(cache => {
                    cache.put(event.request, responseToCache);
                  });
              }
              return response;
            });
        })
    );
    return;
  }

  // Handle all other files with "No Cache" policy
  event.respondWith(
    fetch(event.request)
      .then(response => {
        // Handle successful responses
        if (response.ok) {
          // Create a new response with no-cache headers
          const newHeaders = new Headers(response.headers);
          newHeaders.set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
          newHeaders.set('Pragma', 'no-cache');
          newHeaders.set('Expires', '0');

          return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: newHeaders
          });
        }

        return response;
      })
      .catch(error => {
        console.error('[Service Worker] Fetch failed:', error);

        // Provide fallback responses based on resource type
        if (event.request.mode === 'navigate') {
          return caches.match(OFFLINE_URL);
        } else if (event.request.destination === 'style') {
          return new Response('/* Fallback CSS */', {
            status: 200,
            headers: { 'Content-Type': 'text/css' }
          });
        } else if (event.request.destination === 'script') {
          return new Response('// Fallback JS', {
            status: 200,
            headers: { 'Content-Type': 'application/javascript' }
          });
        } else if (requestUrl.includes('favicon.ico')) {
          // Return empty response for favicon to avoid 404 errors
          return new Response('', {
            status: 204,
            statusText: 'No Content'
          });
        }

        // Generic fallback for other resources
        return new Response('Network Error - Complete No Cache Policy', {
          status: 408,
          statusText: 'Network Error - No Cache Available'
        });
      })
  );
});

// Message handler for cache management
self.addEventListener('message', event => {
  console.log('[Service Worker] Received message:', event.data);

  if (event.data && event.data.type === 'CLEAR_ALL_CACHE') {
    console.log('[Service Worker] Clearing all caches...');

    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          console.log('[Service Worker] Deleting cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      console.log('[Service Worker] All caches cleared');
      event.ports[0].postMessage({
        type: 'CACHE_CLEARED',
        success: true,
        message: 'All caches have been cleared'
      });
    }).catch(error => {
      console.error('[Service Worker] Error clearing caches:', error);
      event.ports[0].postMessage({
        type: 'CACHE_CLEARED',
        success: false,
        message: 'Error clearing caches: ' + error.message
      });
    });
  }

  if (event.data && event.data.type === 'GET_CACHE_INFO') {
    caches.keys().then(cacheNames => {
      const cacheInfo = {
        cacheNames: cacheNames,
        currentCache: CACHE_NAME,
        essentialFiles: ESSENTIAL_CACHE_FILES,
        bypassUrls: BYPASS_URLS // Fixed reference
      };

      event.ports[0].postMessage({
        type: 'CACHE_INFO',
        data: cacheInfo
      });
    });
  }
});

// Periodic cache cleanup (every hour)
setInterval(() => {
  console.log('[Service Worker] Performing periodic cache cleanup...');

  caches.keys().then(cacheNames => {
    // Keep only the current cache
    const oldCaches = cacheNames.filter(name => name !== CACHE_NAME);

    if (oldCaches.length > 0) {
      console.log('[Service Worker] Found old caches to delete:', oldCaches);
      return Promise.all(
        oldCaches.map(cacheName => caches.delete(cacheName))
      );
    }
  }).then(() => {
    console.log('[Service Worker] Periodic cache cleanup completed');
  }).catch(error => {
    console.error('[Service Worker] Error during periodic cleanup:', error);
  });
}, 3600000); // Every hour

console.log('[Service Worker] COMPLETE NO-CACHE Service Worker loaded successfully');
console.log('[Service Worker] Cache name:', CACHE_NAME);
console.log('[Service Worker] Essential files (ONLY these will be cached):', ESSENTIAL_CACHE_FILES);
console.log('[Service Worker] POLICY: NO CACHE for ALL files except essential offline files');
console.log('[Service Worker] ALL PHP, CSS, JS, Images, and other files will be fetched fresh from network');