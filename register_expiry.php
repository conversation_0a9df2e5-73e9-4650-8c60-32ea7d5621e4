<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);

// Fetch items based on store_id
$query = "SELECT item_id, name FROM items WHERE store_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

$message = '';
$success = false;
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $item_id = $_POST['item_id'];
    $expiry_date = $_POST['expiry_date'];
    $quantity = $_POST['quantity'];

    $stmt = $conn->prepare("INSERT INTO item_expiry (item_id, expiry_date, quantity, store_id) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("isii", $item_id, $expiry_date, $quantity, $store_id);
    if ($stmt->execute()) {
        $message = "تم تسجيل صلاحية الصنف بنجاح.";
        $success = true;
    } else {
        $message = "حدث خطأ أثناء تسجيل صلاحية الصنف.";
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل صلاحية الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        
        .form-control, .form-select {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            transition: border-color 0.3s ease;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-control:focus, .form-select:focus {
            border-color: #6fa3ef;
            box-shadow: 0 0 5px rgba(111, 163, 239, 0.5);
        }
        .btn-primary {
            background-color: #3B82F6;
            color: white;
            border: none;
            padding: 12px 25px;
            cursor: pointer;
            border-radius: 10px;
            font-size: 16px;
            margin-top: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        .btn-primary:hover {
            background-color: #2563EB;
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
        }
        .btn-primary:active {
            background-color: #1D4ED8;
            transform: translateY(0);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .alert-info {
            background-color: #3B82F6;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
            padding-top: 60px;
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            color: #06162b;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        .selected-item-frame {
            margin-top: 10px;
            padding: 10px;
            border: 2px solid #3B82F6;
            border-radius: 10px;
            background-color: #1a2537;
            color: #fdb813;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="text-center">تسجيل صلاحية الأصناف</h2>
        <?php if (isset($message) && $message !== ''): ?>
            <div class="alert alert-info"><?= $message ?></div>
        <?php endif; ?>
        <form method="POST" action="">
            <div class="mb-3">
                <label for="item_id" class="form-label">اسم المنتج</label>
                <button type="button" class="btn-primary" onclick="openItemModal()">اختر المنتج</button>
                <input type="hidden" id="item_id" name="item_id" required>
                <div id="selectedItemName" class="selected-item-frame"></div>
            </div>
            <div class="mb-3">
                <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
            </div>
            <div class="mb-3">
                <label for="quantity" class="form-label">الكمية</label>
                <input type="number" class="form-control" id="quantity" name="quantity" required>
            </div>
            <button type="submit" class="btn-primary">تسجيل</button>
        </form>
    </div>

    <div id="itemModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeItemModal()">&times;</span>
            <h2>اختر المنتج</h2>
            <table id="itemsTable" class="display">
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while($row = $result->fetch_assoc()): ?>
                        <tr onclick="selectItem(<?= $row['item_id'] ?>, '<?= htmlspecialchars($row['name']) ?>')">
                            <td><?= htmlspecialchars($row['name']) ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#itemsTable').DataTable();
        });

        function openItemModal() {
            document.getElementById('itemModal').style.display = 'block';
        }

        function closeItemModal() {
            document.getElementById('itemModal').style.display = 'none';
        }

        function selectItem(itemId, itemName) {
            document.getElementById('item_id').value = itemId;
            document.getElementById('selectedItemName').textContent = itemName;
            closeItemModal();
        }

        <?php if ($message !== ''): ?>
            Swal.fire({
                icon: '<?= $success ? "success" : "error" ?>',
                title: '<?= $success ? "تم التسجيل" : "خطأ" ?>',
                text: '<?= $message ?>',
                showConfirmButton: <?= $success ? "false" : "true" ?>,
                timer: <?= $success ? "2000" : "null" ?>
            }).then(() => {
                <?php if ($success): ?>
                    window.location.href = 'expired_items.php?store_id=<?= urlencode($encrypted_store_id) ?>';
                <?php endif; ?>
            });
        <?php endif; ?>
    </script>
</body>
</html>

<?php

$conn->close();
?>
