<?php
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_GET['account_id'] ?? null; // Use passed encrypted account_id

if (!$encrypted_account_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Account ID is required']);
    exit();
}

$account_id = decrypt($encrypted_account_id, $key); // Decrypt the account_id
if (!$account_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid Account ID']);
    exit();
}

// Define the path for the temporary JSON file
$tempInvoicesDir = __DIR__ . '/temp_wholesale_invoices';
$jsonFilePath = $tempInvoicesDir . "/account_{$account_id}.json";

// Check if the file exists
if (!file_exists($jsonFilePath)) {
    echo json_encode(['success' => true, 'items' => [], 'buyer_type' => null, 'buyer_name' => null, 'buyer_branch_id' => null, 'buyer_account_id' => null]);
    exit();
}

// Load the items and buyer details from the JSON file
$data = json_decode(file_get_contents($jsonFilePath), true);

if ($data === null) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to load items from temporary file']);
    exit();
}

echo json_encode([
    'success' => true,
    'items' => $data['items'] ?? [],
    'buyer_type' => $data['buyer_type'] ?? null,
    'buyer_name' => $data['buyer_name'] ?? null,
    'buyer_branch_id' => $data['buyer_branch_id'] ?? null,
    'buyer_account_id' => $data['buyer_account_id'] ?? null
]);
?>
