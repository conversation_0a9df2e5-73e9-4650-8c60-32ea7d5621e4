<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الباركود في الوضع المقسم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        .quantity-input {
            width: 60px;
            padding: 5px;
            text-align: center;
        }
        
        .highlight {
            background-color: #fff3cd !important;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>اختبار الباركود في الوضع المقسم</h1>

        <div class="test-section">
            <h3>اختبار الباركود المخصص:</h3>
            <p><strong>مثال:</strong> 2000001001752 (جبنة تركي - 175 جرام = 0.175 كيلو)</p>
            <p><strong>المتوقع:</strong> الإجمالي = 0.175 × 280 = 49.00 جنيه</p>
            <input type="text" id="barcodeInput" class="test-input" placeholder="أدخل الباركود المخصص" maxlength="13">
            <button onclick="testBarcode()">اختبار الباركود</button>
            <div id="barcodeResult"></div>
        </div>

        <div class="test-section">
            <h3>جدول الأصناف المضافة (محاكاة الوضع المقسم):</h3>
            <table id="selectedItemsTable">
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>السعر/كيلو</th>
                        <th>الكمية</th>
                        <th>الإجمالي</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div><strong>إجمالي الفاتورة: <span id="totalAmount">0.00</span> جنيه</strong></div>
        </div>

        <div class="test-section">
            <h3>أمثلة للاختبار:</h3>
            <button onclick="testExample('2000001001752')">جبنة تركي - 175 جرام (49.00 جنيه)</button>
            <button onclick="testExample('2000002002305')">لحم بقري - 230 جرام (103.50 جنيه)</button>
            <button onclick="testExample('2000003007508')">دجاج - 750 جرام (63.75 جنيه)</button>
            <button onclick="clearTable()">مسح الجدول</button>
        </div>
    </div>

    <script>
        // بيانات الأصناف التجريبية
        const items = {
            '000001': {
                id: '000001',
                name: 'جبنة تركي',
                price: 280
            },
            '000002': {
                id: '000002',
                name: 'لحم بقري',
                price: 450
            },
            '000003': {
                id: '000003',
                name: 'دجاج',
                price: 85
            }
        };

        let addedItems = [];

        // دالة معالجة الباركود المخصص
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(8, 12));
            const quantityInKg = weightInGrams / 1000;

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams
            };
        }

        // محاكاة دالة addItemToSelectedList
        function addItemToSelectedList(item) {
            console.log('addItemToSelectedList - الصنف:', item.name, 'الكمية:', item.quantity);

            // التحقق من وجود الصنف مسبقاً
            const existingIndex = addedItems.findIndex(addedItem => addedItem.id === item.id);

            if (existingIndex !== -1) {
                // تحديث الكمية
                const newQuantity = item.quantity ? parseFloat(item.quantity) : (addedItems[existingIndex].quantity + 1);
                addedItems[existingIndex].quantity = newQuantity;
                updateTableRow(existingIndex);
            } else {
                // إضافة صنف جديد
                addedItems.push({
                    id: item.id,
                    name: item.name,
                    price: item.price,
                    quantity: item.quantity || 1
                });
                addTableRow(addedItems.length - 1);
            }

            updateTotal();
        }

        function testBarcode() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            const resultDiv = document.getElementById('barcodeResult');

            if (!barcode) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال باركود للاختبار</div>';
                return;
            }

            const customBarcodeData = processCustomBarcode(barcode);

            if (customBarcodeData) {
                const item = items[customBarcodeData.itemBarcode];
                if (item) {
                    // تحديث كمية الصنف قبل الإضافة (محاكاة الكود الأصلي)
                    item.quantity = customBarcodeData.quantity;
                    console.log('قبل إضافة الصنف - الصنف:', item.name, 'الكمية:', item.quantity);

                    addItemToSelectedList(item);

                    const expectedTotal = (customBarcodeData.quantity * item.price).toFixed(2);
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ تم إضافة الصنف بنجاح!</h4>
                            <p><strong>الصنف:</strong> ${item.name}</p>
                            <p><strong>الكمية:</strong> ${customBarcodeData.quantity.toFixed(3)} كيلو</p>
                            <p><strong>السعر:</strong> ${item.price} جنيه/كيلو</p>
                            <p><strong>الإجمالي المتوقع:</strong> ${expectedTotal} جنيه</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ صنف غير موجود!</h4>
                            <p><strong>باركود الصنف:</strong> ${customBarcodeData.itemBarcode}</p>
                        </div>
                    `;
                }
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ باركود غير صحيح!</h4>
                        <p>يجب أن يبدأ الباركود بـ 2 ويكون طوله 13 رقم</p>
                    </div>
                `;
            }
        }

        function addTableRow(index) {
            const item = addedItems[index];
            const tbody = document.querySelector('#selectedItemsTable tbody');
            const row = document.createElement('tr');
            row.dataset.index = index;
            row.classList.add('highlight');

            const itemQuantity = parseFloat(item.quantity || 1);
            const itemTotal = itemQuantity * parseFloat(item.price || 0);
            console.log('إنشاء صف جديد - الصنف:', item.name, 'الكمية:', itemQuantity, 'السعر:', item.price, 'الإجمالي:', itemTotal);

            row.innerHTML = `
                <td>${item.name}</td>
                <td>${item.price}/كيلو</td>
                <td><input type="number" class="quantity-input" value="${itemQuantity.toFixed(3)}" min="0.001" step="0.001" onchange="updateQuantity(${index}, this.value)"></td>
                <td class="total-cell">${itemTotal.toFixed(2)}</td>
                <td><button onclick="removeItem(${index})" style="background-color: #dc3545;">حذف</button></td>
            `;

            tbody.appendChild(row);
        }

        function updateTableRow(index) {
            const item = addedItems[index];
            const row = document.querySelector(`tr[data-index="${index}"]`);
            if (row) {
                const quantityInput = row.querySelector('.quantity-input');
                const totalCell = row.querySelector('.total-cell');

                quantityInput.value = item.quantity.toFixed(3);
                const newTotal = item.quantity * item.price;
                totalCell.textContent = newTotal.toFixed(2);
                console.log('تحديث الصف - الصنف:', item.name, 'الكمية:', item.quantity, 'الإجمالي:', newTotal);
            }
        }

        function updateQuantity(index, newQuantity) {
            addedItems[index].quantity = parseFloat(newQuantity) || 0;
            updateTableRow(index);
            updateTotal();
        }

        function removeItem(index) {
            addedItems.splice(index, 1);
            rebuildTable();
            updateTotal();
        }

        function rebuildTable() {
            const tbody = document.querySelector('#selectedItemsTable tbody');
            tbody.innerHTML = '';
            addedItems.forEach((item, index) => {
                addTableRow(index);
            });
        }

        function updateTotal() {
            const total = addedItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            document.getElementById('totalAmount').textContent = total.toFixed(2);
        }

        function testExample(barcode) {
            document.getElementById('barcodeInput').value = barcode;
            testBarcode();
        }

        function clearTable() {
            addedItems = [];
            document.querySelector('#selectedItemsTable tbody').innerHTML = '';
            updateTotal();
            document.getElementById('barcodeResult').innerHTML = '';
        }
    </script>
</body>

</html>