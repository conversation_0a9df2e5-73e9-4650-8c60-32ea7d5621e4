<?php
header('Content-Type: application/json');
include 'db_connection.php';
include 'encryption_functions.php';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

$notification = $input['notification'] ?? [];
$data = $input['data'] ?? [];
$account_id = $input['account_id'] ?? 0;
$store_id = $input['store_id'] ?? 0;
$fcm_message_id = $input['fcm_message_id'] ?? '';

if (empty($notification) || empty($account_id)) {
    echo json_encode(['success' => false, 'error' => 'Missing required fields']);
    exit;
}

try {
    // Extract notification details
    $title = $notification['title'] ?? 'إشعار جديد';
    $body = $notification['body'] ?? '';
    $icon = $notification['icon'] ?? '';
    
    // Extract data details
    $data_json = json_encode($data);
    $notification_type = $data['type'] ?? 'firebase';
    $priority = $data['priority'] ?? 'normal';
    
    // Check if notification with same FCM message ID already exists
    if (!empty($fcm_message_id)) {
        $checkQuery = "SELECT id FROM notifications WHERE fcm_message_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("s", $fcm_message_id);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Notification already exists']);
            $checkStmt->close();
            $conn->close();
            exit;
        }
        $checkStmt->close();
    }
    
    // Insert notification into database
    $insertQuery = "INSERT INTO notifications (store_id, account_id, title, message, type, priority, data, fcm_message_id, created_at, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'notread')";
    $insertStmt = $conn->prepare($insertQuery);
    $insertStmt->bind_param("iissssss", $store_id, $account_id, $title, $body, $notification_type, $priority, $data_json, $fcm_message_id);
    
    if ($insertStmt->execute()) {
        $notification_id = $conn->insert_id;
        echo json_encode([
            'success' => true, 
            'message' => 'Firebase notification saved successfully',
            'notification_id' => $notification_id
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to save notification']);
    }
    
    $insertStmt->close();
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>
