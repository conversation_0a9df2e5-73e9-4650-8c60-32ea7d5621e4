<?php
/**
 * ملف تحويلات الرصيد - مدمج مع نظام الصلاحيات
 * 
 * الصلاحيات المطبقة:
 * - access: الوصول للوحدة (مطلوبة للوصول للصفحة)
 * - view: عرض البيانات (مطلوبة لعرض قائمة التحويلات)
 * - add_transfer: إضافة تحويل رصيد جديد
 * - edit_transfer: تعديل تحويل رصيد موجود
 * - delete_transfer: حذف تحويل رصيد
 * - add_supplier: إضافة مزود جديد
 * - delete_supplier: حذف مزود
 * - comprehensive_report: عرض التقارير
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('balance_transfers', 'access');

// فحص صلاحية عرض البيانات
if (!hasPermission('balance_transfers', 'view')) {
    header('Location: unauthorized.php');
    exit();
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : null;

if ($encrypted_store_id) {
    $store_id = decrypt($encrypted_store_id, $key);

    // Use $transfer_result for fetching transfer data
    $stmt = $conn->prepare("SELECT bt.*, a.username FROM balance_transfers bt 
                            JOIN accounts a ON bt.account_id = a.account_id 
                            WHERE bt.store_id = ? ORDER BY bt.created_at DESC");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $transfer_result = $stmt->get_result();
    $stmt->close();

    // Debugging: Check the number of rows retrieved
   
} else {
    echo "Store ID is missing or invalid.";
    exit();
}
$store_name = '';
if ($encrypted_store_id) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

// Use $enum_result for fetching ENUM values
$provider_options = [];
$enum_result = $conn->query("SHOW COLUMNS FROM balance_transfers LIKE 'provider'");
if ($enum_result) {
    $row = $enum_result->fetch_assoc();
    $enum_values = str_replace(["enum(", ")", "'"], "", $row['Type']);
    $provider_options = explode(",", $enum_values);
}

// Map English provider names to Arabic equivalents
$provider_name_map = [
    'Fawery' => 'فوري',
    'Basata' => 'بساطة',
    'Damen' => 'ضامن'
];
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويلات الرصيد</title>
    <link rel="stylesheet" href="web_css/style_web.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    <div class="container">
        <h2 class="section-title">تحويلات الرصيد</h2>
        <div class="button-container" style="display: inline-flex;">
            <!-- أزرار العمليات مع فحص الصلاحيات -->
            <?php if (hasPermission('balance_transfers', 'add_transfer')): ?>
                <button class="action-btn" onclick="openAddTransferModal()" title="إضافة تحويل رصيد جديد">
                    <i class="fas fa-plus"></i>
                </button>
            <?php endif; ?>
            
            <?php if (hasPermission('balance_transfers', 'comprehensive_report')): ?>
                <button class="action-btn" onclick="showReport()" title="عرض التقرير">
                    <i class="fas fa-file-alt"></i>
                </button>
            <?php endif; ?>
            
            <?php if (hasPermission('balance_transfers', 'add_supplier')): ?>
                <button class="action-btn" onclick="openAddProviderModal()" title="إضافة مزود جديد">
                    <i class="fas fa-user-plus"></i>
                </button>
            <?php endif; ?>
            
            <?php if (hasPermission('balance_transfers', 'delete_supplier')): ?>
                <button class="action-btn" onclick="openViewProvidersModal()" title="عرض وإدارة المزودين">
                    <i class="fas fa-list"></i>
                </button>
            <?php endif; ?>
        </div>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>الشخص</th>
                        <th>المزود</th>
                        <th>التكلفة</th>
                        <th>سعر البيع</th>
                        <th>القيمة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $transfer_result->fetch_assoc()): ?>
                        <?php
                        $provider_name = trim($row['provider']);
                        $provider_class = 'default';
                        $provider_display_name = $provider_name_map[$provider_name] ?? $provider_name;

                        if ($provider_name === 'Fawery') {
                            $provider_class = 'fawry';
                        } elseif ($provider_name === 'Basata') {
                            $provider_class = 'basata';
                        } elseif ($provider_name === 'Damen') {
                            $provider_class = 'damen';
                        }
                        ?>
                        <tr>
                            <td data-label="الشخص"><?= htmlspecialchars($row['username']) ?></td>
                            <td data-label="المزود">
                                <span class="provider-frame <?= $provider_class ?>">
                                    <?= htmlspecialchars($provider_display_name) ?>
                                </span>
                            </td>
                            <td data-label="التكلفة"><?= htmlspecialchars($row['cost']) ?></td>
                            <td data-label="سعر البيع"><?= htmlspecialchars($row['sale_price']) ?></td>
                            <td data-label="القيمة"><?= htmlspecialchars($row['value']) ?></td>
                            <td data-label="تاريخ الإنشاء"><?= htmlspecialchars($row['created_at']) ?></td>
                            <td data-label="الإجراءات">
                                <div class='action-buttons'>
                                    <?php if (hasPermission('balance_transfers', 'edit_transfer')): ?>
                                        <button class="action-btn edit-btn" onclick='openEditTransferModal({"balance_id":<?= htmlspecialchars($row['balance_id']) ?>,"store_id":<?= htmlspecialchars($row['store_id']) ?>,"account_id":<?= htmlspecialchars($row['account_id']) ?>,"provider":"<?= htmlspecialchars($row['provider']) ?>","cost":"<?= htmlspecialchars($row['cost']) ?>","sale_price":"<?= htmlspecialchars($row['sale_price']) ?>","value":"<?= htmlspecialchars($row['value']) ?>","created_at":"<?= htmlspecialchars($row['created_at']) ?>","username":"<?= htmlspecialchars($row['username']) ?>"})' title="تعديل التحويل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if (hasPermission('balance_transfers', 'delete_transfer')): ?>
                                        <button class="action-btn delete-btn" onclick="deleteTransfer(<?= htmlspecialchars($row['balance_id']) ?>)" title="حذف التحويل">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add Transfer Modal -->
    <div id="addTransferModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddTransferModal()">&times;</span>
            <h3>إضافة تحويل رصيد جديد</h3>
            <form id="addTransferForm" method="POST" action="add_balance_transfer.php" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="provider" class="form-label">المزود:</label>
                    <select name="provider" id="provider" class="input-field" required>
                        <?php foreach ($provider_options as $provider): ?>
                            <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="cost" class="form-label">التكلفة:</label>
                    <input type="number" step="0.01" name="cost" id="cost" class="input-field" placeholder="أدخل التكلفة" required>
                </div>
                <div class="form-group">
                    <label for="sale_price" class="form-label">سعر البيع:</label>
                    <input type="number" step="0.01" name="sale_price" id="sale_price" class="input-field" placeholder="أدخل سعر البيع" required>
                </div>
                <div class="form-group">
                    <label for="value" class="form-label">القيمة:</label>
                    <input type="number" step="0.01" name="value" id="value" class="input-field" placeholder="أدخل القيمة" required>
                </div>
                <button type="submit" class="action-btn">إضافة التحويل</button>
            </form>
        </div>
    </div>

    <!-- Add Provider Modal -->
    <div id="addProviderModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddProviderModal()">&times;</span>
            <h2>إضافة مزود جديد</h2>
            <form id="addProviderForm" method="POST" action="add_provider.php">
                <div class="form-group">
                    <label for="provider_name" class="form-label">اسم المزود:</label>
                    <input type="text" name="provider_name" id="provider_name" class="input-field" placeholder="أدخل اسم المزود" required>
                </div>
                <button type="submit" class="action-btn">إضافة المزود</button>
            </form>
        </div>
    </div>

    <!-- View Providers Modal -->
    <div id="viewProvidersModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeViewProvidersModal()">&times;</span>
            <h2>عرض المزودين</h2>
            <ul class="providers-list">
                <?php foreach ($provider_options as $provider): ?>
                    <?php $provider_display_name = $provider_name_map[$provider] ?? $provider; ?>
                    <li>
                        <span class="provider-name"><?= htmlspecialchars($provider_display_name) ?></span>
                        <i class="fas fa-trash delete-icon" onclick="deleteProvider('<?= htmlspecialchars($provider) ?>')"></i>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>

    <!-- Edit Transfer Modal -->
    <div id="editTransferModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditTransferModal()">&times;</span>
            <h3>تعديل تحويل الرصيد</h3>
            <form id="editTransferForm" method="POST" action="edit_balance_transfer.php">
                <input type="hidden" name="balance_id" id="balance_id">
                <div class="form-group">
                    <label for="edit_provider" class="form-label">المزود:</label>
                    <select name="provider" id="edit_provider" class="input-field" required>
                        <?php foreach ($provider_options as $provider): ?>
                            <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_cost" class="form-label">التكلفة:</label>
                    <input type="number" step="0.01" name="cost" id="edit_cost" class="input-field" placeholder="أدخل التكلفة" required>
                </div>
                <div class="form-group">
                    <label for="edit_sale_price" class="form-label">سعر البيع:</label>
                    <input type="number" step="0.01" name="sale_price" id="edit_sale_price" class="input-field" placeholder="أدخل سعر البيع" required>
                </div>
                <div class="form-group">
                    <label for="edit_value" class="form-label">القيمة:</label>
                    <input type="number" step="0.01" name="value" id="edit_value" class="input-field" placeholder="أدخل القيمة" required>
                </div>
                <button type="submit" class="action-btn">تعديل التحويل</button>
            </form>
        </div>
    </div>

    <script>
        // متغيرات الصلاحيات
        const permissions = {
            add_transfer: <?php echo hasPermission('balance_transfers', 'add_transfer') ? 'true' : 'false'; ?>,
            edit_transfer: <?php echo hasPermission('balance_transfers', 'edit_transfer') ? 'true' : 'false'; ?>,
            delete_transfer: <?php echo hasPermission('balance_transfers', 'delete_transfer') ? 'true' : 'false'; ?>,
            add_supplier: <?php echo hasPermission('balance_transfers', 'add_supplier') ? 'true' : 'false'; ?>,
            delete_supplier: <?php echo hasPermission('balance_transfers', 'delete_supplier') ? 'true' : 'false'; ?>,
            comprehensive_report: <?php echo hasPermission('balance_transfers', 'comprehensive_report') ? 'true' : 'false'; ?>
        };

        // تعديل دوال النوافذ لاستخدام إضافة/إزالة الفئة "active"
        function openAddTransferModal() {
            if (!permissions.add_transfer) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لإضافة تحويل رصيد جديد'
                });
                return;
            }
            document.getElementById('addTransferModal').classList.add('active');
        }

        function closeAddTransferModal() {
            document.getElementById('addTransferModal').classList.remove('active');
        }

        function openAddProviderModal() {
            if (!permissions.add_supplier) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لإضافة مزود جديد'
                });
                return;
            }
            document.getElementById('addProviderModal').classList.add('active');
        }

        function closeAddProviderModal() {
            document.getElementById('addProviderModal').classList.remove('active');
        }

        function openViewProvidersModal() {
            if (!permissions.delete_supplier) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لإدارة المزودين'
                });
                return;
            }
            document.getElementById('viewProvidersModal').classList.add('active');
        }

        function closeViewProvidersModal() {
            document.getElementById('viewProvidersModal').classList.remove('active');
        }

        function deleteProvider(providerName) {
            if (!permissions.delete_supplier) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لحذف المزودين'
                });
                return;
            }
            
            closeViewProvidersModal();

            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "سيتم حذف المزود ولن تتمكن من التراجع!",
                icon: 'warning',
                zIndex: 3000,
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذفه!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('delete_provider.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ provider_name: providerName })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف!',
                                text: data.message,
                                zIndex: 3000
                            });
                            setTimeout(() => location.reload(), 2000);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ!',
                                text: data.error || 'حدث خطأ أثناء الحذف.',
                                zIndex: 3000
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: 'حدث خطأ أثناء الاتصال بالخادم.',
                            zIndex: 3000
                        });
                    });
                }
            });
        }

        document.getElementById('addTransferForm').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            formData.append('account_id', '<?= $encrypted_account_id ?>');

            fetch('add_balance_transfer.php?store_id=<?= $encrypted_store_id ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الإضافة بنجاح',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 2000
                    });
                    closeAddTransferModal();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.error || 'حدث خطأ أثناء الإضافة.',
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم.',
                    showConfirmButton: true
                });
            });
        });

        document.getElementById('addProviderForm').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            fetch('add_provider.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الإضافة بنجاح',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 2000
                    });
                    closeAddProviderModal();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.error || 'حدث خطأ أثناء الإضافة.',
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم.',
                    showConfirmButton: true
                });
            });
        });

        function openEditTransferModal(transfer) {
            if (!permissions.edit_transfer) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لتعديل تحويلات الرصيد'
                });
                return;
            }
            
            document.getElementById('balance_id').value = transfer.balance_id;
            document.getElementById('edit_provider').value = transfer.provider;
            document.getElementById('edit_cost').value = transfer.cost;
            document.getElementById('edit_sale_price').value = transfer.sale_price;
            document.getElementById('edit_value').value = transfer.value;
            document.getElementById('editTransferModal').classList.add('active');
        }

        function closeEditTransferModal() {
            document.getElementById('editTransferModal').classList.remove('active');
        }

        document.getElementById('editTransferForm').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            fetch('edit_balance_transfer.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التعديل بنجاح',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 2000
                    });
                    closeEditTransferModal();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.error || 'حدث خطأ أثناء التعديل.',
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم.',
                    showConfirmButton: true
                });
            });
        });

        function deleteTransfer(balanceId) {
            if (!permissions.delete_transfer) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لحذف تحويلات الرصيد'
                });
                return;
            }
            
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "سيتم حذف عملية التحويل ولن تتمكن من التراجع!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذفه!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('delete_balance_transfer.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ balance_id: balanceId })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف!',
                                text: data.message,
                                showConfirmButton: false,
                                timer: 2000
                            });
                            setTimeout(() => location.reload(), 2000);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ!',
                                text: data.error || 'حدث خطأ أثناء الحذف.',
                                showConfirmButton: true
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: 'حدث خطأ أثناء الاتصال بالخادم.',
                            showConfirmButton: true
                        });
                    });
                }
            });
        }

        function showReport() {
            if (!permissions.comprehensive_report) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لعرض التقارير'
                });
                return;
            }
            
            fetch(`generate_report.php?store_id=<?= $encrypted_store_id ?>`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديد الوضع الحالي (فاتح أم مظلم)
                    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                    
                    // ألوان متكيفة مع الوضع
                    const colors = {
                        background: isDarkMode ? '#21262d' : '#f8f9fa',
                        text: isDarkMode ? '#e6edf3' : '#333333',
                        title: isDarkMode ? '#58a6ff' : '#3498db',
                        totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
                        totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
                        totalText: isDarkMode ? '#4caf50' : '#2e7d32',
                        cardBorder: isDarkMode ? '#30363d' : 'transparent',
                        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
                        profitBg: isDarkMode ? 'linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%)' : '#e3f2fd',
                        profitBorder: isDarkMode ? '#3498db' : '#1565c0',
                        profitText: isDarkMode ? '#58a6ff' : '#1565c0',
                        providerBg: isDarkMode ? 'linear-gradient(135deg, rgba(155, 89, 182, 0.2) 0%, rgba(155, 89, 182, 0.1) 100%)' : '#f3e5f5',
                        providerBorder: isDarkMode ? '#9b59b6' : '#7b1fa2',
                        providerText: isDarkMode ? '#bc8dbf' : '#7b1fa2'
                    };

                    // إنشاء HTML للمزودين
                    let providersHtml = '';
                    data.providers.forEach((provider, index) => {
                        const providerColors = [
                            { bg: isDarkMode ? 'rgba(40, 167, 69, 0.15)' : '#d4edda', border: '#28a745', text: isDarkMode ? '#4caf50' : '#155724' },
                            { bg: isDarkMode ? 'rgba(52, 152, 219, 0.15)' : '#e3f2fd', border: '#3498db', text: isDarkMode ? '#58a6ff' : '#1565c0' },
                            { bg: isDarkMode ? 'rgba(155, 89, 182, 0.15)' : '#f3e5f5', border: '#9b59b6', text: isDarkMode ? '#bc8dbf' : '#7b1fa2' },
                            { bg: isDarkMode ? 'rgba(255, 193, 7, 0.15)' : '#fff3cd', border: '#ffc107', text: isDarkMode ? '#f7cc47' : '#856404' },
                            { bg: isDarkMode ? 'rgba(220, 53, 69, 0.15)' : '#f8d7da', border: '#dc3545', text: isDarkMode ? '#f85149' : '#721c24' }
                        ];
                        const colorSet = providerColors[index % providerColors.length];
                        
                        providersHtml += `
                            <div style="background: ${colorSet.bg}; padding: 20px; border-radius: 12px; border: 1px solid ${colorSet.border}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colorSet.text}; font-size: 18px; font-weight: 700; margin-bottom: 8px;">
                                            ${provider.name}
                                        </div>
                                        <div style="color: ${colorSet.text}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">
                                            عدد التحويلات: ${provider.count}
                                        </div>
                                        <div style="color: ${colorSet.text}; font-size: 16px; font-weight: 600;">
                                            المكسب: ${provider.profit} جنيه
                                        </div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, ${colorSet.border}, ${colorSet.border}dd); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px ${colorSet.border}33;">
                                        <i class="fas fa-wallet" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    Swal.fire({
                        title: '<i class="fas fa-wallet" style="color: #28a745;"></i> تقرير تحويلات الرصيد الشامل',
                        html: `
                            <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                                <div style="text-align: center; margin-bottom: 25px;">
                                    <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                                        <i class="fas fa-chart-line" style="margin-left: 10px;"></i>
                                        ملخص تحويلات الرصيد
                                    </h3>
                                    <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                                        <?php echo htmlspecialchars($store_name); ?>
                                    </p>
                                    <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                                </div>
                                
                                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                                    <div style="background: ${colors.totalBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.totalBorder}; text-align: center; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                                        <div style="position: relative; z-index: 1;">
                                            <div style="color: ${colors.totalText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                                <i class="fas fa-coins" style="margin-left: 8px;"></i>
                                                إجمالي قيمة التحويلات
                                            </div>
                                            <div style="color: ${colors.totalText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                ${data.total_value} جنيه
                                            </div>
                                            <div style="width: 80px; height: 2px; background: ${colors.totalBorder}; margin: 15px auto; border-radius: 1px;"></div>
                                            <div style="color: ${colors.totalText}; font-size: 12px; opacity: 0.8;">
                                                مجموع قيم جميع التحويلات
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div style="background: ${colors.profitBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.profitBorder}; text-align: center; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(52, 152, 219, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                                        <div style="position: relative; z-index: 1;">
                                            <div style="color: ${colors.profitText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                                                إجمالي المكسب
                                            </div>
                                            <div style="color: ${colors.profitText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                ${data.total_profit} جنيه
                                            </div>
                                            <div style="width: 80px; height: 2px; background: ${colors.profitBorder}; margin: 15px auto; border-radius: 1px;"></div>
                                            <div style="color: ${colors.profitText}; font-size: 12px; opacity: 0.8;">
                                                إجمالي الأرباح من جميع التحويلات
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="margin-bottom: 20px;">
                                    <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; text-align: center;">
                                        <i class="fas fa-users" style="margin-left: 8px;"></i>
                                        تفاصيل المزودين
                                    </h4>
                                    <div style="display: grid; gap: 15px;">
                                        ${providersHtml}
                                    </div>
                                </div>
                                
                                <div class="info-section" style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title}; animation: fadeInUp 0.5s ease-out;">
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                        <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                                        <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات إضافية</span>
                                    </div>
                                    <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                                        • يتم تحديث هذا التقرير تلقائياً عند إضافة أو تعديل التحويلات<br>
                                        • جميع المبالغ محسوبة بالجنيه المصري<br>
                                        • المكسب = (سعر البيع - التكلفة) × القيمة<br>
                                        • التقرير يشمل جميع تحويلات الرصيد في هذا الفرع
                                    </div>
                                </div>
                            </div>
                        `,
                        icon: null,
                        confirmButtonText: 'إغلاق',
                        confirmButtonColor: '#28a745',
                        width: '750px',
                        customClass: {
                            popup: isDarkMode ? 'swal2-dark' : '',
                            title: isDarkMode ? 'swal2-title-dark' : '',
                            content: isDarkMode ? 'swal2-content-dark' : ''
                        },
                        background: isDarkMode ? '#0d1117' : '#ffffff',
                        color: isDarkMode ? '#e6edf3' : '#333333',
                        showClass: {
                            popup: 'animate__animated animate__fadeInUp animate__faster'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutDown animate__faster'
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.error || 'حدث خطأ أثناء توليد التقرير.',
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم.',
                    showConfirmButton: true
                });
            });
        }
    </script>

    <?php
    $conn->close();
    ?>
</body>
</html>
