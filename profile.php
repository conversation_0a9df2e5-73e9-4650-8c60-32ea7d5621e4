<?php

include 'db_connection.php';
include 'encryption_functions.php';
require_once 'security.php';

$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';

$key = getenv('ENCRYPTION_KEY');

// Add code to retrieve the store name
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

// Get the encrypted account ID from the session
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';
$account_id = decrypt($encrypted_account_id, $key);

// Initialize variables
$username = $name = $phone = '';
$user = ['theme' => 'Light']; // Default theme
$imgPath = ''; // Profile image path

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form inputs
    $new_username = $_POST['username'];
    $new_name     = $_POST['name'];
    $new_phone    = $_POST['phone'];
    $new_theme    = $_POST['theme'];
    $new_password = $_POST['password'];
    $profile_img  = $_FILES['profile_img'];

    // Handle profile image upload
    if ($profile_img['error'] === UPLOAD_ERR_OK) {
        $target_dir = "uploads/";
        $target_file = $target_dir . basename($profile_img["name"]);
        move_uploaded_file($profile_img["tmp_name"], $target_file);
        $imgPath = $target_file;
    }

    // Update the account details
    $stmt = $conn->prepare("UPDATE accounts SET username = ?, name = ?, phone = ?, theme = ?, img_path = ? WHERE account_id = ?");
    $stmt->bind_param("sssssi", $new_username, $new_name, $new_phone, $new_theme, $imgPath, $account_id);
    if ($stmt->execute()) {
        $message = "تم تحديث البيانات بنجاح";
    } else {
        $message = "حدث خطأ أثناء التحديث";
    }
    $stmt->close();
}

// Fetch current account details
$stmt = $conn->prepare("SELECT username, name, phone, theme, img_path FROM accounts WHERE account_id = ?");
$stmt->bind_param("i", $account_id);
$stmt->execute();
$stmt->bind_result($username, $name, $phone, $user['theme'], $imgPath);
$stmt->fetch();
$stmt->close();

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف الحساب</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <!-- Font Awesome if needed -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    <div class="container">
        <div class="profile-container">
            <div class="profile-header">
                <?php if ($imgPath): ?>
                    <img src="<?= htmlspecialchars($imgPath) ?>" alt="Profile" class="account-profile-img">
                <?php else: ?>
                    <div class="account-default-profile">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
                <h2>ملفي الشخصي</h2>
            </div>
            
            <p class="success-message"></p>
            
            <form id="profileForm" method="POST" enctype="multipart/form-data" class="profile-form">
                <!-- Pass the encrypted account ID -->
                <input type="hidden" name="account_id" value="<?= htmlspecialchars($encrypted_account_id) ?>">
                
                <div class="form-group">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="icon-input">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" class="input-field" placeholder="اسم المستخدم" 
                               value="<?= htmlspecialchars($username) ?>" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="icon-input">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" class="input-field" placeholder="أدخل كلمة المرور الجديدة إذا رغبت بتغييرها">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="name" class="form-label">الاسم</label>
                    <div class="icon-input">
                        <i class="fas fa-id-card"></i>
                        <input type="text" id="name" name="name" class="input-field" placeholder="الاسم" value="<?= htmlspecialchars($name) ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="phone" class="form-label">الهاتف</label>
                    <div class="icon-input">
                        <i class="fas fa-phone"></i>
                        <input type="text" id="phone" name="phone" class="input-field" placeholder="الهاتف" value="<?= htmlspecialchars($phone) ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="theme" class="form-label">اختيار الثيم</label>
                    <div class="icon-input">
                        <i class="fas fa-palette"></i>
                        <select id="theme" name="theme" class="input-field" required>
                            <option value="" disabled>اختر الثيم</option>
                            <option value="Light" <?= $user['theme'] === 'Light' ? 'selected' : '' ?>>فاتح</option>
                            <option value="Dark" <?= $user['theme'] === 'Dark' ? 'selected' : '' ?>>داكن</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="profile_img" class="image-upload-label">
                        <i class="fas fa-upload"></i> تغيير الصورة الشخصية
                    </label>
                    <input type="file" id="profile_img" name="profile_img" accept="image/*" class="image-upload-input">
                </div>
                
                <button type="submit" class="add-btn">تحديث البيانات</button>
            </form>
        </div>
    </div>
    
    <script>
    document.getElementById('profileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        fetch('update_profile.php', { // Updated to use update_profile.php for profile updates
             method: 'POST',
             body: formData
        })
        .then(response => response.json())
        .then(data => {
             if(data.success) {
                  Swal.fire({
                      icon: 'success',
                      title: 'نجاح',
                      text: data.message
                  });
             } else {
                  Swal.fire({
                      icon: 'error',
                      title: 'خطأ',
                      text: data.message
                  });
             }
        })
        .catch(error => {
             console.error('Error:', error);
        });
    });
    </script>
    <?php include 'notifications.php'; ?>

</body>
</html>
