<?php
include 'db_connection.php';
include 'encryption_functions.php';
require 'vendor/autoload.php'; // Ensure you have PHPExcel library installed

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Conditional;

$key = getenv('ENCRYPTION_KEY');
$encrypted_inventory_id = isset($_GET['inventory_id']) ? $_GET['inventory_id'] : null;
$inventory_id = decrypt($encrypted_inventory_id, $key);

// Fetch inventory and store details in one query
$inventory_sql = "SELECT mi.*, s.name AS store_name 
                  FROM monthly_inventory mi 
                  JOIN stores s ON mi.store_id = s.store_id 
                  WHERE mi.inventory_id = ?";
$stmt = $conn->prepare($inventory_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$inventory_result = $stmt->get_result();
$inventory = $inventory_result->fetch_assoc();
$stmt->close();

// Fetch items in the inventory with their categories
$items_sql = "SELECT items.name, items.category_id, categories.name AS category_name, monthly_inventory_items.total_recorded_quantity, 
              monthly_inventory_items.closing_quantity, monthly_inventory_items.sold_quantity, monthly_inventory_items.cost, 
              monthly_inventory_items.price, monthly_inventory_items.total_cost, monthly_inventory_items.total_sales, 
              monthly_inventory_items.profit 
              FROM monthly_inventory_items 
              JOIN items ON monthly_inventory_items.item_id = items.item_id 
              JOIN categories ON items.category_id = categories.category_id 
              WHERE monthly_inventory_items.inventory_id = ?";
$stmt = $conn->prepare($items_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$items_result = $stmt->get_result();
$items = $items_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Calculate totals
$total_recorded_quantity = 0;
$total_closing_quantity = 0;
$total_sold_quantity = 0;
$total_cost = 0;
$total_sales = 0;
$total_profit = 0;
$total_remaining_cost = 0;

foreach ($items as $item) {
    $total_recorded_quantity += $item['total_recorded_quantity'];
    $total_closing_quantity += $item['closing_quantity'];
    $total_sold_quantity += $item['sold_quantity'];
    $total_cost += $item['total_cost'];
    $total_sales += $item['total_sales'];
    $total_profit += $item['profit'];
    $total_remaining_cost += $item['closing_quantity'] * $item['cost'];
}

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('ملخص الجرد');

// Define styles
$headerStyle = [
    'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF007BFF']]
];
$detailRowStyle = [
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT, 'vertical' => Alignment::VERTICAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
    'font' => ['name' => 'Tahoma', 'size' => 11]
];
$negativeProfitStyle = [
    'font' => ['color' => ['argb' => 'FFFF0000']]
];

// Add title
$sheet->mergeCells('A1:J1');
$sheet->setCellValue('A1', 'ملخص الجرد');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(20);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getRowDimension('1')->setRowHeight(40);

// Add store and inventory details
$sheet->setCellValue('A2', 'المخزن: ' . $inventory['store_name']);
$sheet->setCellValue('A3', 'تاريخ الجرد: ' . $inventory['inventory_date']);
$sheet->mergeCells('A2:J2');
$sheet->mergeCells('A3:J3');
$sheet->getStyle('A2:A3')->getFont()->setBold(true)->setSize(12);
$sheet->getStyle('A2:A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

// Add summary table
$sheet->setCellValue('A5', 'إجمالي الكمية المسجلة');
$sheet->setCellValue('B5', 'إجمالي الكمية المتبقية');
$sheet->setCellValue('C5', 'إجمالي الكمية المباعة');
$sheet->setCellValue('D5', 'إجمالي التكلفة');
$sheet->setCellValue('E5', 'إجمالي المبيعات');
$sheet->setCellValue('F5', 'إجمالي المكسب');
$sheet->setCellValue('G5', 'إجمالي جملة الأصناف المتبقية');

$sheet->setCellValue('A6', $total_recorded_quantity);
$sheet->setCellValue('B6', $total_closing_quantity);
$sheet->setCellValue('C6', $total_sold_quantity);
$sheet->setCellValue('D6', $total_cost);
$sheet->setCellValue('E6', $total_sales);
$sheet->setCellValue('F6', $total_profit);
$sheet->setCellValue('G6', $total_remaining_cost);

$sheet->getStyle('A5:G5')->applyFromArray($headerStyle);
$sheet->getStyle('A6:G6')->applyFromArray($detailRowStyle);

// Set header row for detailed items
$headers = ['اسم الصنف', 'التصنيف', 'الكمية المباعة', 'المكسب', 'إجمالي المبيعات', 'الكمية المسجلة', 'الكمية المتبقية', 'التكلفة', 'السعر', 'إجمالي التكلفة'];
$columnIndex = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($columnIndex . '8', $header);
    $sheet->getStyle($columnIndex . '8')->applyFromArray($headerStyle);
    $columnIndex++;
}

$rowNumber = 9;
$currentCategory = null;
foreach ($items as $item) {
    if ($currentCategory !== $item['category_name']) {
        $sheet->mergeCells('A' . $rowNumber . ':J' . $rowNumber);
        $sheet->setCellValue('A' . $rowNumber, $item['category_name']);
        $sheet->getStyle('A' . $rowNumber)->applyFromArray($headerStyle);
        $currentCategory = $item['category_name'];
        $rowNumber++;
    }
    $sheet->setCellValue('A' . $rowNumber, $item['name']);
    $sheet->setCellValue('B' . $rowNumber, $item['category_name']);
    $sheet->setCellValue('C' . $rowNumber, $item['sold_quantity']);
    $sheet->setCellValue('D' . $rowNumber, $item['profit']);
    $sheet->setCellValue('E' . $rowNumber, $item['total_sales']);
    $sheet->setCellValue('F' . $rowNumber, $item['total_recorded_quantity']);
    $sheet->setCellValue('G' . $rowNumber, $item['closing_quantity']);
    $sheet->setCellValue('H' . $rowNumber, $item['cost']);
    $sheet->setCellValue('I' . $rowNumber, $item['price']);
    $sheet->setCellValue('J' . $rowNumber, $item['total_cost']);
    $sheet->getStyle('A' . $rowNumber . ':J' . $rowNumber)->applyFromArray($detailRowStyle);
    if ($item['profit'] < 0) {
        $sheet->getStyle('D' . $rowNumber)->applyFromArray($negativeProfitStyle);
    }
    $rowNumber++;
}

// Auto size columns
foreach (range('A', 'J') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

// Apply currency format to price columns
$sheet->getStyle('H9:H' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');
$sheet->getStyle('I9:I' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');
$sheet->getStyle('J9:J' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');

// Set right-to-left direction
$sheet->setRightToLeft(true);

// Apply AutoFilter
$sheet->setAutoFilter('A8:J8');

// Freeze Panes
$sheet->freezePane('A9');

// Conditional formatting for negative profit
$conditionalStyles = $sheet->getStyle('D9:D' . $rowNumber)->getConditionalStyles();
$conditional = new Conditional();
$conditional->setConditionType(Conditional::CONDITION_CELLIS)
            ->setOperatorType(Conditional::OPERATOR_LESSTHAN)
            ->addCondition('0');
$conditional->getStyle()->applyFromArray($negativeProfitStyle);
$conditionalStyles[] = $conditional;
$sheet->getStyle('D9:D' . $rowNumber)->setConditionalStyles($conditionalStyles);

$filename = 'ملخص_الجرد_' . $inventory['store_name'] . '_' . date('Ymd_His') . '.xlsx';

// Log the inventory summary export action
session_start();
$account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
            VALUES (?, 'export', 'monthly_inventory', ?)";
$description = "تم إصدار ملخص الجرد للمخزن $inventory[store_name]";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("is", $account_id, $description);
$log_stmt->execute();
$log_stmt->close();

header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit();
?>
