<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');
$response = ['success' => false, 'categories' => []];

try {
    if (isset($_GET['store_id'])) {
        $encrypted_store_id = $_GET['store_id'];
        $store_id = decrypt($encrypted_store_id, $key);

        if ($store_id === false) {
            throw new Exception("Failed to decrypt store ID.");
        }

        $stmt = $conn->prepare("SELECT category_id, name FROM categories WHERE store_id = ? ORDER BY name ASC");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }

        $stmt->bind_param("i", $store_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();

        while ($row = $result->fetch_assoc()) {
            // تشفير معرف التصنيف
            $encrypted_id = encrypt($row['category_id'], $key);
            
            // إضافة التصنيف إلى المصفوفة
            $response['categories'][] = [
                'id' => $row['category_id'],
                'encrypted_id' => $encrypted_id,
                'name' => $row['name']
            ];
        }
        
        $response['success'] = true;
    }
} catch (Exception $e) {
    error_log($e->getMessage());
    http_response_code(500);
    $response['error'] = "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقًا.";
}

echo json_encode($response);
?>
