:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #4dabf7;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --light-bg: #f0f2f5;
    
    /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
    --dark-bg: #0f1419;
    --dark-surface: #1a2332;
    --dark-surface-light: #242b3d;
    --dark-surface-hover: #2a3441;
    --dark-text: #e1e8f0;
    --dark-text-secondary: #b8c5d1;
    --dark-text-muted: #8a9ba8;
    --border-color: #dee2e6;
    --dark-border: #2d3748;
    --dark-border-light: #3a4553;
    
    /* ألوان زرقاء ناعمة ومريحة */
    --blue-gradient-start: #1e3a8a;
    --blue-gradient-end: #3b82f6;
    --blue-accent: #5b9bd5;
    --blue-hover: #4a90c2;
    --blue-soft: #6ba3d6;
    --blue-muted: #4a7ba7;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    background-color: var(--light-bg);
    color: #333;
    transition: all 0.3s ease;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    direction: rtl;
    text-align: right;
}

.dark-mode {
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 40%, var(--dark-surface-light) 100%);
    color: var(--dark-text);
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark-mode .header {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
    box-shadow: 0 2px 20px rgba(91, 155, 213, 0.25);
    border-bottom: 1px solid var(--dark-border-light);
}

.store-name {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
}

/* Search Bar */
.search-container {
    background-color: var(--light-bg);
    padding: 20px;
    position: sticky;
    top: 70px;
    z-index: 999;
    border-bottom: 1px solid var(--border-color);
}

.dark-mode .search-container {
    background: linear-gradient(180deg, var(--dark-surface) 0%, var(--dark-bg) 100%);
    border-bottom: 1px solid var(--dark-border-light);
}

.search-wrapper {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 16px;
    background-color: var(--white);
    color: #333;
    transition: all 0.3s ease;
    outline: none;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.search-input.searching {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
}

.search-loading {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 16px;
    animation: spin 1s linear infinite;
    display: none;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

.dark-mode .search-loading {
    color: var(--blue-soft);
}

/* استايل التحديد بلوحة المفاتيح */
.keyboard-selected {
    background: rgba(0, 123, 255, 0.1) !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: 8px !important;
}

.dark-mode .keyboard-selected {
    background: rgba(91, 155, 213, 0.15) !important;
    border-color: var(--blue-soft) !important;
}

/* تحسين عرض الاقتراحات */
.suggestion-tag:hover {
    background: var(--primary-dark) !important;
    transform: scale(1.05) !important;
    transition: all 0.2s ease !important;
}

.dark-mode .suggestion-tag {
    background: var(--blue-muted) !important;
}

.dark-mode .suggestion-tag:hover {
    background: var(--blue-accent) !important;
}

/* استايل قائمة الاقتراحات */
.search-suggestions {
    font-family: 'Cairo', Arial, sans-serif !important;
    direction: rtl !important;
}

.search-suggestions::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.dark-mode .search-suggestions::-webkit-scrollbar-thumb {
    background: var(--blue-soft);
}

/* استايل زر البحث الصوتي */
.voice-search-btn:hover {
    background: rgba(0, 123, 255, 0.1) !important;
    color: var(--primary-color) !important;
}

.dark-mode .voice-search-btn:hover {
    background: rgba(91, 155, 213, 0.1) !important;
    color: var(--blue-soft) !important;
}

/* تحسين عرض النتائج عالية النقاط */
.high-score-result {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%) !important;
    border-left: 4px solid var(--primary-color) !important;
}

.dark-mode .high-score-result {
    background: linear-gradient(90deg, rgba(91, 155, 213, 0.08) 0%, rgba(91, 155, 213, 0.03) 100%) !important;
    border-left-color: var(--blue-soft) !important;
}

/* تحسين مظهر البحث في الهواتف */
@media (max-width: 768px) {
    .search-suggestions {
        max-height: 150px !important;
        font-size: 0.9rem !important;
    }
    
    .suggestion-item {
        padding: 10px 12px !important;
    }
    
    .voice-search-btn {
        left: 45px !important;
        font-size: 14px !important;
    }

    /* تحسين عمودي الصور والإضافة للهواتف */
    .custom-table th:nth-child(2),
    .custom-table th:nth-child(3) {
        width: 70px !important;
        padding: 12px 8px !important;
        min-width: 70px !important;
        font-size: 0.9rem !important;
    }

    .custom-table td:nth-child(2),
    .custom-table td:nth-child(3) {
        width: 70px !important;
        padding: 12px 8px !important;
        min-width: 70px !important;
    }

    /* تحسين أبعاد الصفوف العامة للهواتف */
    .custom-table th,
    .custom-table td {
        padding: 15px 12px !important;
        font-size: 0.95rem !important;
    }

    /* تحسين عمود اسم المنتج للهواتف */
    .custom-table td:first-child {
        padding: 15px 15px !important;
    }

    .view-images-btn,
    .btn-secondary:disabled,
    .custom-table .add-btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.85rem !important;
    }

    .quantity-input {
        width: 60px !important;
        max-width: 60px !important;
        padding: 8px 10px !important;
        font-size: 0.9rem !important;
    }

    .highlight input {
        width: 60px !important;
        max-width: 60px !important;
        padding: 8px 10px !important;
        font-size: 0.9rem !important;
    }
}

.dark-mode .search-input {
    background-color: var(--dark-surface-light);
    color: var(--dark-text);
    border-color: var(--dark-border-light);
}

.dark-mode .search-input:focus {
    border-color: var(--blue-soft);
    box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2);
    background-color: var(--dark-surface-hover);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    font-size: 18px;
    pointer-events: none;
}

.dark-mode .search-icon {
    color: var(--dark-text-muted);
}

/* Main Content */
.main-content {
    padding: 30px 20px 100px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Item Count Bar */
.item-count-bar {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.dark-mode .item-count-bar {
    background: var(--dark-surface-light);
    box-shadow: 0 4px 20px rgba(26, 35, 50, 0.3);
    border: 1px solid var(--dark-border-light);
    color: var(--dark-text);
}

/* Table Styles */
.table-container {
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    transition: all 0.3s ease;
}

.table-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dark-mode .table-container {
    background: var(--dark-surface-light);
    box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4);
    border: 1px solid var(--dark-border-light);
}

.dark-mode .table-container:hover {
    box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5);
    border-color: var(--blue-soft);
}

.custom-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.custom-table thead {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.dark-mode .custom-table thead {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
}

.custom-table th {
    padding: 20px;
    color: var(--white);
    font-weight: 600;
    font-size: 1.1rem;
    text-align: right;
    border: none;
    vertical-align: middle;
}

/* Center alignment for action columns - تحسين للشاشات الكبيرة */
.custom-table th:nth-child(2),
.custom-table th:nth-child(3) {
    text-align: center;
    width: 120px;
    padding: 20px 15px;
    min-width: 120px;
}

.custom-table td:nth-child(2),
.custom-table td:nth-child(3) {
    text-align: center;
    width: 120px;
    padding: 20px 15px;
    vertical-align: middle;
    min-width: 120px;
}

/* Header icon styling for action columns */
.custom-table th:nth-child(2) i,
.custom-table th:nth-child(3) i {
    display: block;
    margin: 0 auto 5px auto;
    font-size: 1.2rem;
}

/* Header text styling for action columns */
.custom-table th:nth-child(2),
.custom-table th:nth-child(3) {
    font-size: 0.95rem;
    line-height: 1.2;
}

.custom-table td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: right;
    transition: background-color 0.3s ease;
    min-height: 60px;
    vertical-align: middle;
}

.dark-mode .custom-table td {
    border-bottom-color: var(--dark-border-light);
    color: var(--dark-text);
}

.custom-table tbody tr {
    transition: all 0.3s ease;
    cursor: pointer;
}

.custom-table tbody tr:hover:not(.highlight) {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.04) 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.dark-mode .custom-table tbody tr:hover:not(.highlight) {
    background: linear-gradient(90deg, rgba(91, 155, 213, 0.12) 0%, rgba(74, 144, 194, 0.06) 100%);
    box-shadow: 0 2px 8px rgba(91, 155, 213, 0.15);
}

.custom-table tbody tr:nth-child(even):not(.highlight) {
    background-color: rgba(0, 123, 255, 0.02);
}

.dark-mode .custom-table tbody tr:nth-child(even):not(.highlight) {
    background-color: rgba(45, 55, 72, 0.3);
}

/* Highlight styles for selected items */
.highlight {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: var(--white) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
}

.highlight td {
    background: transparent !important;
    color: var(--white) !important;
    border-bottom-color: rgba(255, 255, 255, 0.2) !important;
}

.dark-mode .highlight {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4) !important;
}

.highlight:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4) !important;
}

.dark-mode .highlight:hover {
    box-shadow: 0 6px 20px rgba(91, 155, 213, 0.5) !important;
}

.highlight input {
    background-color: var(--white) !important;
    color: #333 !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    text-align: center !important;
    width: 90px !important;
    max-width: 90px !important;
    margin: 0 auto !important;
    padding: 8px 12px !important;
}

.highlight input:focus {
    border-color: rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
}

.highlight .remove-item {
    transition: all 0.3s ease;
}

.highlight .remove-item:hover {
    transform: scale(1.2);
    color: #ff6b6b !important;
}

/* Ensure buttons in highlighted rows remain centered */
.highlight td:nth-child(2),
.highlight td:nth-child(3) {
    text-align: center;
}

.highlight .view-images-btn,
.highlight .btn-secondary:disabled {
    margin: 0 auto;
}

/* تحسين مدخل الكمية في الشاشات الكبيرة */
.quantity-input {
    width: 90px !important;
    max-width: 90px !important;
    margin: 0 auto !important;
    padding: 8px 12px !important;
    text-align: center !important;
    border-radius: 8px !important;
    border: 2px solid var(--border-color) !important;
    font-weight: 600 !important;
    background-color: var(--white) !important;
    color: #333 !important;
    transition: all 0.3s ease !important;
}

.quantity-input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    outline: none !important;
}

.dark-mode .quantity-input {
    background-color: var(--dark-surface-light) !important;
    color: var(--dark-text) !important;
    border-color: var(--dark-border-light) !important;
}

.dark-mode .quantity-input:focus {
    border-color: var(--blue-soft) !important;
    box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2) !important;
}

/* Button Styles */
.btn {
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.dark-mode .btn-primary {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
}

.dark-mode .btn-primary:hover {
    box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
}

.item-name {
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: right;
    direction: rtl;
    line-height: 1.4;
    min-height: 40px;
}

.dark-mode .item-name {
    color: var(--dark-text);
}

.item-icon {
    margin-right: 10px;
    margin-left: 0;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.custom-table tbody tr:hover .item-icon {
    transform: scale(1.1);
    color: var(--blue-hover);
}

.dark-mode .custom-table tbody tr:hover .item-icon {
    color: var(--blue-soft);
}

/* Table Button Styles - Removed old conflicting styles */

/* Highlight styles for selected items */
.highlight {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: var(--white) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
}

.highlight td {
    background: transparent !important;
    color: var(--white) !important;
    border-bottom-color: rgba(255, 255, 255, 0.2) !important;
}

.dark-mode .highlight {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4) !important;
}

.highlight input {
    background-color: var(--white) !important;
    color: #333 !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    text-align: center !important;
}

.highlight .remove-item {
    transition: all 0.3s ease;
}

.highlight .remove-item:hover {
    transform: scale(1.2);
    color: #ff6b6b !important;
}

/* Form Controls */
.form-select {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 8px 12px;
    font-family: 'Cairo', Arial, sans-serif;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.dark-mode .form-select {
    background-color: var(--dark-surface-light);
    color: var(--dark-text);
    border-color: var(--dark-border-light);
}

.dark-mode .form-select:focus {
    border-color: var(--blue-soft);
    box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2);
}

/* Sale Options */
#sale-options {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    margin: 15px 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.dark-mode #sale-options {
    background: var(--dark-surface-light);
    box-shadow: 0 4px 20px rgba(26, 35, 50, 0.3);
    border: 1px solid var(--dark-border-light);
}

#sale-options > div {
    margin-bottom: 15px;
}

#sale-options label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.dark-mode #sale-options label {
    color: var(--dark-text);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 15px 18px;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
    }

    .store-name {
        font-size: 0.9rem;
        flex: 1;
        text-align: right;
        line-height: 1.2;
    }

    .header > div:last-child {
        flex-shrink: 0;
    }

    .header .form-select {
        font-size: 0.75rem;
        padding: 6px 8px;
        min-width: 90px;
        width: auto;
    }

    .main-content {
        padding: 20px 15px 100px;
    }

    .custom-table th,
    .custom-table td {
        padding: 12px 15px;
    }

    .search-container {
        padding: 15px;
        top: 70px; /* إرجاع الموضع الأصلي */
    }

    #sale-options {
        margin: 15px;
        padding: 15px;
    }

    #sale-options > div {
        margin-bottom: 12px;
    }

    #sale-options .form-select {
        width: 100%;
        margin-top: 5px;
    }
}

@media (max-width: 768px) {
.header {
padding: 20px 24px; /* زودت البادينج عشان يكبر الهيدر */
flex-direction: row;
gap: 12px;
align-items: center;
justify-content: space-between;
}

.store-name {
font-size: 1.1rem; /* كبرت حجم النص */
flex: 1;
text-align: right;
line-height: 1.3;
}

.header > div:last-child {
flex-shrink: 0;
}

.header .form-select {
font-size: 0.8rem; /* شويه أكبر */
padding: 8px 10px;
min-width: 100px;
width: auto;
}


    .custom-table th,
    .custom-table td {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .search-container {
        top: 70px;
        padding: 12px;
    }

    #sale-options {
        margin: 10px;
        padding: 12px;
    }

    #sale-options label {
        font-size: 0.9rem;
    }

    #sale-options .form-select {
        font-size: 0.9rem;
        padding: 8px 10px;
    }
}

/* تحسينات إضافية للهواتف الصغيرة جداً */
@media (max-width: 360px) {
    .header {
        padding: 10px 12px;
        gap: 6px;
    }

    .store-name {
        font-size: 0.7rem;
        line-height: 1.0;
    }

    .header .form-select {
        font-size: 0.55rem;
        padding: 4px 5px;
        min-width: 70px;
    }

    .item-count-bar {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .item-count-bar .btn {
        width: 100%;
    }

    /* تحسين إضافي للعمودين في الشاشات الصغيرة جداً */
    .custom-table th:nth-child(2),
    .custom-table th:nth-child(3) {
        width: 55px !important;
        padding: 10px 5px !important;
        min-width: 55px !important;
        font-size: 0.8rem !important;
    }

    .custom-table td:nth-child(2),
    .custom-table td:nth-child(3) {
        width: 55px !important;
        padding: 10px 5px !important;
        min-width: 55px !important;
    }

    /* تحسين أبعاد الصفوف العامة للشاشات الصغيرة جداً */
    .custom-table th,
    .custom-table td {
        padding: 12px 8px !important;
        font-size: 0.9rem !important;
    }

    /* تحسين عمود اسم المنتج للشاشات الصغيرة جداً */
    .custom-table td:first-child {
        padding: 12px 10px !important;
    }

    .view-images-btn,
    .btn-secondary:disabled,
    .custom-table .add-btn {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.75rem !important;
    }

    .quantity-input {
        width: 50px !important;
        max-width: 50px !important;
        padding: 6px 8px !important;
        font-size: 0.85rem !important;
    }

    .highlight input {
        width: 50px !important;
        max-width: 50px !important;
        padding: 6px 8px !important;
        font-size: 0.85rem !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom SweetAlert2 Styles for Product Display Window */
.custom-swal-products {
    z-index: 9999 !important;
}

.custom-swal-products .swal2-popup {
    background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%) !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
    border: 2px solid rgba(0, 123, 255, 0.1) !important;
    padding: 30px !important;
    max-width: 700px !important;
    width: 90% !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
    overflow: visible !important;
}

.dark-mode .custom-swal-products .swal2-popup {
    background: linear-gradient(135deg, var(--dark-surface-light) 0%, var(--dark-surface) 100%) !important;
    border: 2px solid var(--dark-border-light) !important;
    box-shadow: 0 20px 60px rgba(26, 35, 50, 0.4) !important;
    color: var(--dark-text) !important;
}

.custom-swal-products .swal2-title {
    color: var(--primary-color) !important;
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    margin-bottom: 25px !important;
    text-align: center !important;
    position: relative !important;
    padding-bottom: 15px !important;
}

.custom-swal-products .swal2-title::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 60px !important;
    height: 3px !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    border-radius: 2px !important;
}

.dark-mode .custom-swal-products .swal2-title {
    color: var(--blue-soft) !important;
}

.dark-mode .custom-swal-products .swal2-title::after {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
}

.custom-swal-products .swal2-html-container {
    margin: 0 !important;
    padding: 0 !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    direction: rtl !important;
}

.custom-swal-products .swal2-html-container::-webkit-scrollbar {
    width: 8px !important;
}

.custom-swal-products .swal2-html-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1) !important;
    border-radius: 4px !important;
}

.custom-swal-products .swal2-html-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    border-radius: 4px !important;
}

.dark-mode .custom-swal-products .swal2-html-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
}

/* View Images Button Styles */
.view-images-btn {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
    border: none;
    color: var(--white);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.view-images-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
}

.dark-mode .view-images-btn {
    background: linear-gradient(135deg, var(--blue-soft) 0%, var(--blue-accent) 100%);
    box-shadow: 0 2px 8px rgba(91, 155, 213, 0.3);
}

.dark-mode .view-images-btn:hover {
    background: linear-gradient(135deg, var(--blue-accent) 0%, var(--blue-muted) 100%);
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4);
}

/* Disabled eye button styles */
.btn-secondary:disabled {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #5a6268 100%);
    border: none;
    color: var(--white);
    opacity: 0.6;
    cursor: not-allowed;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
}

.dark-mode .btn-secondary:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    opacity: 0.5;
}

/* Add button styles in table */
.custom-table .add-btn {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    color: var(--white);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.custom-table .add-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.dark-mode .custom-table .add-btn {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
    box-shadow: 0 2px 8px rgba(91, 155, 213, 0.3);
}

.dark-mode .custom-table .add-btn:hover {
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.image-modal-content {
    position: relative;
    background: var(--white);
    margin: 2% auto;
    padding: 0;
    width: 90%;
    max-width: 900px;
    max-height: 90%;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: modalSlideIn 0.4s ease-out;
}

.dark-mode .image-modal-content {
    background: var(--dark-surface-light);
    box-shadow: 0 20px 60px rgba(26, 35, 50, 0.5);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.image-modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark-mode .image-modal-header {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
}

.image-modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    font-family: 'Cairo', Arial, sans-serif;
}

.image-modal-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.image-modal-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
}

.image-modal-body::-webkit-scrollbar {
    width: 8px;
}

.image-modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.image-modal-body::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 4px;
}

.dark-mode .image-modal-body::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
}

.images-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.image-item {
    text-align: center;
    border: 2px solid var(--border-color);
    border-radius: 15px;
    padding: 15px;
    background: var(--light-bg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.image-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.dark-mode .image-item {
    background: var(--dark-surface);
    border-color: var(--dark-border-light);
    box-shadow: 0 4px 15px rgba(26, 35, 50, 0.3);
}

.dark-mode .image-item:hover {
    border-color: var(--blue-soft);
    box-shadow: 0 8px 25px rgba(26, 35, 50, 0.4);
}

.image-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-item img:hover {
    transform: scale(1.05);
}

.no-images {
    text-align: center;
    padding: 60px 20px;
    color: var(--secondary-color);
}

.dark-mode .no-images {
    color: var(--dark-text-muted);
}

.no-images i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-images p {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

/* Full size image modal */
.fullsize-modal {
    display: none;
    position: fixed;
    z-index: 10001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
}

.fullsize-modal-content {
    margin: auto;
    display: block;
    width: 90%;
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 10px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.fullsize-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: var(--white);
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10002;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.fullsize-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .image-modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .image-modal-header {
        padding: 15px 20px;
    }

    .image-modal-title {
        font-size: 1.2rem;
    }

    .image-modal-body {
        padding: 20px;
    }

    .images-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .image-item {
        padding: 10px;
    }

    .image-item img {
        height: 150px;
    }

    .fullsize-close {
        top: 10px;
        right: 15px;
        font-size: 2rem;
        width: 40px;
        height: 40px;
    }
}

/* Custom table container for SweetAlert - نفس استايل جدول المنتجات */
.custom-swal-products .table-container,
.custom-swal-products .swal2-html-container .table-container {
    background: var(--white) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
    overflow: hidden !important;
    margin-top: 0 !important;
    border: 1px solid rgba(0, 123, 255, 0.1) !important;
    transition: all 0.3s ease !important;
}

.custom-swal-products .table-container:hover,
.custom-swal-products .swal2-html-container .table-container:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.dark-mode .custom-swal-products .table-container,
.dark-mode .custom-swal-products .swal2-html-container .table-container {
    background: var(--dark-surface-light) !important;
    box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4) !important;
    border: 1px solid var(--dark-border-light) !important;
}

.dark-mode .custom-swal-products .table-container:hover,
.dark-mode .custom-swal-products .swal2-html-container .table-container:hover {
    box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5) !important;
    border-color: var(--blue-soft) !important;
}

.custom-swal-products .table {
    width: 100% !important;
    margin: 0 !important;
    border-collapse: collapse !important;
    background-color: transparent !important;
    border: none !important;
}

/* إلغاء تأثير Bootstrap على الجدول */
.custom-swal-products .table > :not(caption) > * > * {
    padding: 0 !important;
    background-color: transparent !important;
    border-bottom-width: 0 !important;
    box-shadow: none !important;
}

/* إلغاء جميع تأثيرات Bootstrap */
.custom-swal-products .table,
.custom-swal-products .table th,
.custom-swal-products .table td,
.custom-swal-products .table tr {
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.custom-swal-products .table-bordered,
.custom-swal-products .table-bordered th,
.custom-swal-products .table-bordered td {
    border: none !important;
}

.custom-swal-products .table thead {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.dark-mode .custom-swal-products .table thead {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
}

.custom-swal-products .table th {
    padding: 20px !important;
    color: var(--white) !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    text-align: right !important;
    border: none !important;
    background-color: transparent !important;
    border-bottom: none !important;
    vertical-align: middle !important;
}

.custom-swal-products .table td {
    padding: 15px 20px !important;
    border-bottom: 1px solid var(--border-color) !important;
    text-align: right !important;
    transition: all 0.3s ease !important;
    background-color: transparent !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    vertical-align: middle !important;
}

.dark-mode .custom-swal-products .table td {
    border-bottom-color: var(--dark-border-light) !important;
    color: var(--dark-text) !important;
}

.custom-swal-products .table tbody tr {
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    background-color: transparent !important;
    border: none !important;
}

.custom-swal-products .table tbody tr:hover:not(.highlight) {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.04) 100%) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1) !important;
}

.custom-swal-products .table tbody tr:hover:not(.highlight) td {
    background-color: transparent !important;
}

.dark-mode .custom-swal-products .table tbody tr:hover:not(.highlight) {
    background: linear-gradient(90deg, rgba(91, 155, 213, 0.12) 0%, rgba(74, 144, 194, 0.06) 100%) !important;
    box-shadow: 0 2px 8px rgba(91, 155, 213, 0.15) !important;
}

.dark-mode .custom-swal-products .table tbody tr:hover:not(.highlight) td {
    background-color: transparent !important;
}

.custom-swal-products .table tbody tr:nth-child(even):not(.highlight) {
    background-color: rgba(0, 123, 255, 0.02) !important;
}

.custom-swal-products .table tbody tr:nth-child(even):not(.highlight) td {
    background-color: transparent !important;
}

.dark-mode .custom-swal-products .table tbody tr:nth-child(even):not(.highlight) {
    background-color: rgba(45, 55, 72, 0.3) !important;
}

.dark-mode .custom-swal-products .table tbody tr:nth-child(even):not(.highlight) td {
    background-color: transparent !important;
}

/* Item name styling in SweetAlert table - نفس استايل جدول المنتجات */
.custom-swal-products .item-name {
    font-weight: 600 !important;
    color: #333 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    text-align: right !important;
    direction: rtl !important;
}

.dark-mode .custom-swal-products .item-name {
    color: var(--dark-text) !important;
}

.custom-swal-products .item-icon {
    margin-right: 10px !important;
    margin-left: 0 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
}

.custom-swal-products .text-primary {
    color: var(--primary-color) !important;
}

.dark-mode .custom-swal-products .text-primary {
    color: var(--blue-soft) !important;
}

.custom-swal-products .table tbody tr:hover .item-icon {
    transform: scale(1.1) !important;
    color: var(--blue-hover) !important;
}

.dark-mode .custom-swal-products .table tbody tr:hover .item-icon {
    color: var(--blue-soft) !important;
}

/* استايل أزرار الحذف في نافذة الأصناف المضافة */
.custom-swal-products .delete-item-btn {
    width: 35px !important;
    height: 35px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    margin: 0 auto !important;
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%) !important;
    border: none !important;
    color: var(--white) !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
}

.custom-swal-products .delete-item-btn:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4) !important;
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
}

.dark-mode .custom-swal-products .delete-item-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #e55353 100%) !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.dark-mode .custom-swal-products .delete-item-btn:hover {
    background: linear-gradient(135deg, #e55353 0%, #d63031 100%) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
}

/* تحسين عرض عمود الحذف */
.custom-swal-products .table th:last-child,
.custom-swal-products .table td:last-child {
    width: 80px !important;
    text-align: center !important;
}

/* تأثيرات إضافية للصفوف عند hover */
.custom-swal-products .table tbody tr:hover .delete-item-btn {
    transform: scale(1.05) !important;
}

/* تأكيد نهائي لإلغاء Bootstrap */
.custom-swal-products * {
    box-sizing: border-box !important;
}

.custom-swal-products .table * {
    border: none !important;
    background-image: none !important;
    background-color: transparent !important;
}

.custom-swal-products .table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    background-image: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.dark-mode .custom-swal-products .table thead th {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
    background-image: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
}

/* استايل نوافذ SweetAlert2 العادية للثيم الداكن */
.dark-mode .swal2-popup:not(.custom-swal-products .swal2-popup) {
    background: linear-gradient(135deg, var(--dark-surface-light) 0%, var(--dark-surface) 100%) !important;
    color: var(--dark-text) !important;
    border: 2px solid var(--dark-border-light) !important;
    border-radius: 15px !important;
    box-shadow: 0 15px 50px rgba(26, 35, 50, 0.4) !important;
}

.dark-mode .swal2-title:not(.custom-swal-products .swal2-title) {
    color: var(--blue-soft) !important;
}

.dark-mode .swal2-content:not(.custom-swal-products .swal2-content) {
    color: var(--dark-text-secondary) !important;
}

.dark-mode .swal2-confirm:not(.custom-swal-products .swal2-confirm) {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3) !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 600 !important;
}

.dark-mode .swal2-confirm:not(.custom-swal-products .swal2-confirm):hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4) !important;
}

.dark-mode .swal2-cancel:not(.custom-swal-products .swal2-cancel) {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #5a6268 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 600 !important;
    color: var(--white) !important;
}

.dark-mode .swal2-cancel:not(.custom-swal-products .swal2-cancel):hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
}

.dark-mode .swal2-icon.swal2-success [class^='swal2-success-line'] {
    background-color: var(--blue-soft) !important;
}

.dark-mode .swal2-icon.swal2-success .swal2-success-ring {
    border-color: var(--blue-soft) !important;
}

.dark-mode .swal2-icon.swal2-error {
    border-color: #ff6b6b !important;
    color: #ff6b6b !important;
}

.dark-mode .swal2-icon.swal2-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.dark-mode .swal2-icon.swal2-info {
    border-color: var(--blue-soft) !important;
    color: var(--blue-soft) !important;
}

.dark-mode .swal2-backdrop {
    background: rgba(0, 0, 0, 0.7) !important;
}

/* تحسين الخطوط في نوافذ SweetAlert العادية */
.swal2-popup:not(.custom-swal-products .swal2-popup) {
    font-family: 'Cairo', Arial, sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

.swal2-title:not(.custom-swal-products .swal2-title) {
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 700 !important;
}

.swal2-content:not(.custom-swal-products .swal2-content) {
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 500 !important;
}

.swal2-confirm:not(.custom-swal-products .swal2-confirm),
.swal2-cancel:not(.custom-swal-products .swal2-cancel) {
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 600 !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
}

.swal2-confirm:not(.custom-swal-products .swal2-confirm):hover,
.swal2-cancel:not(.custom-swal-products .swal2-cancel):hover {
    transform: translateY(-2px) !important;
}

.custom-swal-products .swal2-actions {
    margin-top: 25px !important;
    gap: 15px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.custom-swal-products .swal2-confirm {
    background: linear-gradient(135deg, var(--success-color) 0%, #218838 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 30px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
    transition: all 0.3s ease !important;
}

.custom-swal-products .swal2-confirm:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.custom-swal-products .swal2-cancel {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #5a6268 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 30px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
    transition: all 0.3s ease !important;
}

.custom-swal-products .swal2-cancel:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
}

.custom-swal-products .btn-secondary {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    color: var(--white) !important;
    font-weight: 600 !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3) !important;
    transition: all 0.3s ease !important;
    margin-bottom: 15px !important;
}

.custom-swal-products .btn-secondary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4) !important;
}

.custom-swal-products #image-gallery {
    background: rgba(0, 123, 255, 0.05) !important;
    border-radius: 12px !important;
    padding: 15px !important;
    border: 2px dashed rgba(0, 123, 255, 0.2) !important;
    min-height: 100px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
    margin-top: 20px !important;
}

.dark-mode .custom-swal-products #image-gallery {
    background: rgba(91, 155, 213, 0.08) !important;
    border-color: rgba(91, 155, 213, 0.3) !important;
}

.custom-swal-products #image-gallery:empty::before {
    content: 'لم يتم تحميل أي صور بعد' !important;
    color: var(--secondary-color) !important;
    font-style: italic !important;
    font-size: 0.9rem !important;
}

.dark-mode .custom-swal-products #image-gallery:empty::before {
    color: var(--dark-text-muted) !important;
}

.custom-swal-products .swal2-backdrop {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(5px) !important;
}

/* Animation for the custom SweetAlert */
.custom-swal-products.swal2-show {
    animation: customSwalSlideIn 0.4s ease-out !important;
}

@keyframes customSwalSlideIn {
    from {
        opacity: 0 !important;
        transform: scale(0.8) translateY(-50px) !important;
    }
    to {
        opacity: 1 !important;
        transform: scale(1) translateY(0) !important;
    }
}

/* Responsive adjustments for the custom SweetAlert */
@media (max-width: 768px) {
    .custom-swal-products .swal2-popup {
        width: 95% !important;
        padding: 20px !important;
        margin: 10px !important;
    }

    .custom-swal-products .swal2-title {
        font-size: 1.5rem !important;
        margin-bottom: 20px !important;
    }

    .custom-swal-products .table th,
    .custom-swal-products .table td {
        padding: 12px 15px !important;
        font-size: 0.9rem !important;
    }

    .custom-swal-products .swal2-actions {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .custom-swal-products .swal2-confirm,
    .custom-swal-products .swal2-cancel {
        width: 100% !important;
        padding: 12px 20px !important;
    }
}

@media (max-width: 480px) {
    .custom-swal-products .table th,
    .custom-swal-products .table td {
        padding: 10px 12px !important;
        font-size: 0.9rem !important;
    }

    .custom-swal-products .delete-item-btn {
        width: 30px !important;
        height: 30px !important;
        font-size: 0.8rem !important;
    }

    .custom-swal-products .table th:last-child,
    .custom-swal-products .table td:last-child {
        width: 60px !important;
    }
}

/* تحسينات للشاشات المتوسطة - التابلت 769px إلى 1023px */
@media (min-width: 769px) and (max-width: 1023px) {
    .custom-table th:nth-child(2),
    .custom-table th:nth-child(3) {
        width: 110px;
        padding: 20px 15px;
        min-width: 110px;
    }

    .custom-table td:nth-child(2),
    .custom-table td:nth-child(3) {
        width: 110px;
        padding: 20px 15px;
        min-width: 110px;
    }

    /* تحسين أبعاد الصفوف العامة للتابلت */
    .custom-table th,
    .custom-table td {
        padding: 18px 15px;
        font-size: 1rem;
    }

    /* تحسين عمود اسم المنتج للتابلت */
    .custom-table td:first-child {
        padding: 18px 18px;
    }

    .quantity-input {
        width: 85px !important;
        max-width: 85px !important;
        padding: 9px 12px !important;
    }

    .highlight input {
        width: 85px !important;
        max-width: 85px !important;
        padding: 9px 12px !important;
    }

    .view-images-btn,
    .btn-secondary:disabled,
    .custom-table .add-btn {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
}

/* تحسينات للشاشات الكبيرة - فوق 1024px */
@media (min-width: 1024px) {
    .custom-table th:nth-child(2),
    .custom-table th:nth-child(3) {
        width: 140px;
        padding: 25px 20px;
        min-width: 140px;
    }

    .custom-table td:nth-child(2),
    .custom-table td:nth-child(3) {
        width: 140px;
        padding: 25px 20px;
        min-width: 140px;
    }

    .quantity-input {
        width: 100px !important;
        max-width: 100px !important;
        padding: 10px 15px !important;
    }

    .highlight input {
        width: 100px !important;
        max-width: 100px !important;
        padding: 10px 15px !important;
    }
}

/* ===== التصميم الريسبونسيف الجديد ===== */

/* Offline Status Indicator */
#offline-status {
    position: fixed;
    top: 15px;
    left: 15px;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    color: #333;
    transition: all 0.3s ease;
}

#offline-status.offline {
    background-color: #fff5f5;
    border-color: #ffcccc;
    color: #e53e3e;
}

#offline-status .status-icon {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #38a169;
}

#offline-status.offline .status-icon {
    background-color: #e53e3e;
    animation: pulse-red 1.5s infinite;
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.7); }
    70% { box-shadow: 0 0 0 6px rgba(229, 62, 62, 0); }
    100% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0); }
}

.dark-mode #offline-status {
    background-color: var(--dark-surface);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

.dark-mode #offline-status.offline {
    background-color: rgba(229, 62, 62, 0.2);
    border-color: rgba(229, 62, 62, 0.5);
    color: #fc8181;
}

/* Responsive Layout is handled by JavaScript */

/* Responsive Section Styles for Large Screens */
.responsive-section {
    background: var(--white);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.responsive-section:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dark-mode .responsive-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.dark-mode .responsive-section:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.categories-section {
    flex: 0.8;
    min-width: 220px;
}

.items-section {
    flex: 1.2;
    min-width: 280px;
}

.selected-items-section {
    flex: 1;
    min-width: 240px;
}

.responsive-section h3 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
    margin-bottom: 20px;
    font-weight: 600;
}

.dark-mode .responsive-section h3 {
    color: var(--blue-soft);
    border-bottom-color: var(--blue-soft);
}

.responsive-section .form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.responsive-section .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.dark-mode .responsive-section .form-control {
    background: var(--dark-surface);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .responsive-section .form-control:focus {
    border-color: var(--blue-soft);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Category List Items */
.responsive-section #categoryList li {
    padding: 12px 15px;
    margin: 8px 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.responsive-section #categoryList li:hover,
.responsive-section #categoryList li.active {
    background: var(--primary-color);
    color: white;
    transform: translateX(-3px);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
}

.dark-mode .responsive-section #categoryList li {
    background: var(--dark-surface);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

.dark-mode .responsive-section #categoryList li:hover,
.dark-mode .responsive-section #categoryList li.active {
    background: var(--blue-soft);
    color: white;
}

/* Items List */
.responsive-section #itemsList .item-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.responsive-section #itemsList .item-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.dark-mode .responsive-section #itemsList .item-card {
    background: var(--dark-surface);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

.dark-mode .responsive-section #itemsList .item-card:hover {
    border-color: var(--blue-soft);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Selected Items */
.responsive-section #selectedItemsList .selected-item {
    background: var(--success-color);
    color: white;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.responsive-section #selectedItemsList .selected-item .quantity-input {
    width: 60px;
    padding: 5px 8px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    text-align: center;
}

.responsive-section #saveInvoiceBtn {
    background: linear-gradient(135deg, var(--success-color), #28a745);
    border: none;
    border-radius: 10px;
    padding: 15px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.responsive-section #saveInvoiceBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Invoice Summary in Responsive Mode */
.responsive-section .invoice-summary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
}

.dark-mode .responsive-section .invoice-summary {
    background: linear-gradient(135deg, var(--blue-soft), #2c5aa0);
    box-shadow: 0 3px 10px rgba(74, 144, 226, 0.3);
}

/* Responsive behavior is handled by JavaScript */

/* Toastr Dark Mode Support */
.dark-mode .toast-success {
    background-color: #2c3e50;
    color: #e4e6eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-left: 6px solid #2ecc71;
}

.dark-mode .toast-error {
    background-color: #2c3e50;
    color: #e4e6eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-left: 6px solid #e74c3c;
}

.dark-mode .toast-info {
    background-color: #2c3e50;
    color: #e4e6eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-left: 6px solid #3498db;
}

.dark-mode .toast-warning {
    background-color: #2c3e50;
    color: #e4e6eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-left: 6px solid #f39c12;
}

.dark-mode .toast-progress {
    background-color: rgba(255, 255, 255, 0.3);
}

.dark-mode .toast-close-button {
    color: #e4e6eb;
    opacity: 0.7;
}

.dark-mode .toast-close-button:hover {
    color: #ffffff;
    opacity: 1;
}

/* =============================================
   اختصارات الكيبورد والتحسينات المتعلقة بالحفظ
   ============================================= */

/* تحسين مظهر زر حفظ الفاتورة مع الاختصار */
#view-items {
    position: relative;
    transition: all 0.3s ease;
}

#view-items:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4) !important;
}

#view-items small {
    transition: opacity 0.3s ease;
}

#view-items:hover small {
    opacity: 1 !important;
}

/* تحسين إشعارات اختصار الكيبورد */
.keyboard-shortcut-notification {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين مظهر زر الحفظ في الوضع المظلم */
[data-theme="dark"] #view-items {
    background: linear-gradient(135deg, #0d6efd, #6610f2);
}

[data-theme="dark"] #view-items:hover {
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4) !important;
}

[data-theme="dark"] #view-items small {
    color: rgba(255, 255, 255, 0.8);
}

/* تحسين مظهر لوحة إرشادات اختصارات الكيبورد */
.keyboard-shortcuts-info {
    font-family: 'Cairo', sans-serif;
}

.keyboard-shortcuts-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15) !important;
}

.keyboard-shortcuts-info button:hover {
    color: #666 !important;
}

/* تحسين مظهر لوحة الإرشادات في الوضع المظلم */
[data-theme="dark"] .keyboard-shortcuts-info {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    border-color: #495057 !important;
}

[data-theme="dark"] .keyboard-shortcuts-info strong {
    color: #74b9ff !important;
}

[data-theme="dark"] .keyboard-shortcuts-info div {
    color: #ddd !important;
}

[data-theme="dark"] .keyboard-shortcuts-info span {
    background: #495057 !important;
    color: #fff !important;
    border-color: #6c757d !important;
}

[data-theme="dark"] .keyboard-shortcuts-info i.fas.fa-keyboard {
    color: #74b9ff !important;
}

[data-theme="dark"] .keyboard-shortcuts-info button {
    color: #adb5bd !important;
}

[data-theme="dark"] .keyboard-shortcuts-info button:hover {
    color: #fff !important;
}

/* تحسين مظهر نافذة المساعدة */
.keyboard-shortcuts-modal {
    font-family: 'Cairo', sans-serif !important;
}

.keyboard-shortcuts-modal .swal2-title {
    color: #007bff !important;
    font-weight: 600 !important;
}

.keyboard-shortcuts-modal .swal2-html-container {
    line-height: 1.6 !important;
}

/* تحسين مظهر نافذة المساعدة في الوضع المظلم */
[data-theme="dark"] .keyboard-shortcuts-modal {
    background-color: #1a2332 !important;
    color: #e1e8f0 !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal .swal2-title {
    color: #74b9ff !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal .swal2-html-container {
    color: #e1e8f0 !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal h4 {
    color: #74b9ff !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal div[style*="background: #f8f9fa"] {
    background: #242b3d !important;
    color: #e1e8f0 !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal div[style*="background: #d4edda"] {
    background: #1e3f28 !important;
    border-color: #2d5a3d !important;
    color: #85c896 !important;
}

[data-theme="dark"] .keyboard-shortcuts-modal div[style*="background: #fff3cd"] {
    background: #3d3221 !important;
    border-color: #5a4a2e !important;
    color: #d4af37 !important;
}