<?php
include 'db_connection.php';

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_item_id']) && isset($_POST['current_status'])) {
    $toggle_item_id = $_POST['toggle_item_id'];
    $current_status = $_POST['current_status'];
    $new_status = $current_status === 'active' ? 'inactive' : 'active';

    $stmt = $conn->prepare("UPDATE items SET status = ? WHERE item_id = ?");
    $stmt->bind_param("si", $new_status, $toggle_item_id);
    if ($stmt->execute()) {
        // Log the item status change action
        session_start();
        include 'encryption_functions.php';
        $key = getenv('ENCRYPTION_KEY');
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Fetch the item name for logging
        $stmt_item = $conn->prepare("SELECT name FROM items WHERE item_id = ?");
        $stmt_item->bind_param("i", $toggle_item_id);
        $stmt_item->execute();
        $stmt_item->bind_result($item_name);
        $stmt_item->fetch();
        $stmt_item->close();

        // Translate status to Arabic
        $new_status_ar = $new_status === 'active' ? 'نشط' : 'متوقف';

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'update', 'items', ?, ?)";
        $description = "تم تغيير حالة الصنف $item_name إلى $new_status_ar";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $toggle_item_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true, 'new_status' => $new_status]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update item status.']);
    }
    $stmt->close();
    exit();
}

echo json_encode(['success' => false, 'message' => 'Invalid request.']);
$conn->close();
?>
