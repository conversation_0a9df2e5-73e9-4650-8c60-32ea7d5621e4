<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['q']) && isset($_GET['store_id'])) {
    $query = trim($_GET['q']);
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);

    if ($store_id === false) {
        echo json_encode(['success' => false, 'message' => 'Failed to decrypt store ID.']);
        exit();
    }

    // إذا كان البحث فارغ، إرجاع جميع المنتجات
    if (empty($query)) {
        $stmt = $conn->prepare("SELECT items.item_id, items.name, items.price, categories.name as category_name 
                                FROM items 
                                JOIN categories ON items.category_id = categories.category_id 
                                WHERE categories.store_id = ? AND items.status = 'active' 
                                ORDER BY items.name ASC 
                                LIMIT 100");
        $stmt->bind_param("i", $store_id);
    } else {
        // بحث محسن ومتقدم
        $searchTerms = explode(' ', $query);
        $searchConditions = [];
        $params = [];
        $types = '';
        
        // إنشاء شروط البحث المتعددة
        foreach ($searchTerms as $term) {
            if (!empty(trim($term))) {
                $searchConditions[] = "(items.name LIKE ? OR items.barcode LIKE ? OR categories.name LIKE ?)";
                $searchTerm = "%" . trim($term) . "%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $types .= 'sss';
            }
        }
        
        if (empty($searchConditions)) {
            echo json_encode([]);
            exit();
        }
        
        // استعلام محسن مع البحث في اسم المنتج والباركود وفئة المنتج
        $sql = "SELECT DISTINCT items.item_id, items.name, items.price, items.barcode, categories.name as category_name,
                CASE 
                    WHEN items.name LIKE ? THEN 1
                    WHEN items.name LIKE ? THEN 2
                    WHEN items.barcode LIKE ? THEN 3
                    WHEN categories.name LIKE ? THEN 4
                    ELSE 5
                END as relevance_score
                FROM items 
                JOIN categories ON items.category_id = categories.category_id 
                WHERE categories.store_id = ? AND items.status = 'active' 
                AND (" . implode(' AND ', $searchConditions) . ")
                ORDER BY relevance_score ASC, items.name ASC 
                LIMIT 50";
        
        $stmt = $conn->prepare($sql);
        
        // إضافة معاملات الترتيب
        $exactMatch = $query . "%";
        $startsWith = $query . "%";
        $barcode = "%" . $query . "%";
        $category = "%" . $query . "%";
        
        $allParams = array_merge(
            [$exactMatch, $startsWith, $barcode, $category, $store_id],
            $params
        );
        $allTypes = 'ssssi' . $types;
        
        $stmt->bind_param($allTypes, ...$allParams);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();

    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = [
            'item_id' => $row['item_id'],
            'name' => $row['name'],
            'price' => $row['price'],
            'barcode' => $row['barcode'] ?? '',
            'category_name' => $row['category_name'] ?? '',
            'relevance_score' => $row['relevance_score'] ?? 5
        ];
    }

    echo json_encode($items);
} else {
    echo json_encode([]);
}
?>
