<?php


include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول لوحدة الأصناف
checkPagePermission('items', 'access');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['category_id'])) {
    $encrypted_category_id = $_GET['category_id'];
    $category_id = decrypt($encrypted_category_id, $key);

    if ($category_id) {
        $re_encrypted_category_id = encrypt($category_id, $key);

        // Fetch store ID, store name, and category name using category ID
        $store_sql = "SELECT store_id, name AS category_name, (SELECT name FROM stores WHERE store_id = categories.store_id) AS store_name FROM categories WHERE category_id = ?";
        $store_stmt = $conn->prepare($store_sql);
        $store_stmt->bind_param("i", $category_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        $store_row = $store_result->fetch_assoc();
        $store_id = $store_row ? $store_row['store_id'] : null;
        $store_name = $store_row ? $store_row['store_name'] : null;
        $category_name = $store_row ? $store_row['category_name'] : null;
        $store_stmt->close();

        if ($store_id) {
            $encrypted_store_id = encrypt($store_id, $key);
            // تعيين متغير encrypted_category_id ليكون متاحًا في السايد بار
            $encrypted_category_id = $re_encrypted_category_id;

            // معالجة طلبات POST مع فحص الصلاحيات المحددة
            if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_item_id'])) {
                // فحص صلاحية حذف الأصناف
                if (!hasPermission('items', 'delete_item')) {
                    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف الأصناف']);
                    exit();
                }

                $delete_item_id = $_POST['delete_item_id'];

                $stmt = $conn->prepare("DELETE FROM items WHERE item_id = ?");
                $stmt->bind_param("i", $delete_item_id);
                $stmt->execute();
                $stmt->close();
                echo json_encode(['success' => true]);
                exit();
            }

            if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_item_id'])) {
                // فحص صلاحية تغيير حالة الصنف
                if (!hasPermission('items', 'change_item_status')) {
                    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتغيير حالة الأصناف']);
                    exit();
                }

                $toggle_item_id = $_POST['toggle_item_id'];
                $current_status = $_POST['current_status'];
                $new_status = $current_status === 'active' ? 'inactive' : 'active';
                $stmt = $conn->prepare("UPDATE items SET status = ? WHERE item_id = ?");
                $stmt->bind_param("si", $new_status, $toggle_item_id);
                $stmt->execute();
                $stmt->close();
                echo json_encode(['success' => true, 'new_status' => $new_status]);
                exit();
            }

            // Fetch items with image count and favorite status
            $account_id = isset($_SESSION['account_id']) ? decrypt($_SESSION['account_id'], $key) : null;
            
            if ($account_id) {
                $sql = "SELECT i.*, COUNT(img.img_id) as image_count,
                        (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite
                        FROM items i 
                        LEFT JOIN item_images img ON i.item_id = img.item_id 
                        WHERE i.category_id = ? 
                        GROUP BY i.item_id 
                        ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.item_id DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("iii", $account_id, $category_id, $account_id);
            } else {
                $sql = "SELECT i.*, COUNT(img.img_id) as image_count, 0 as is_favorite
                        FROM items i 
                        LEFT JOIN item_images img ON i.item_id = img.item_id 
                        WHERE i.category_id = ? 
                        GROUP BY i.item_id 
                        ORDER BY i.item_id DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $category_id);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
        } else {
            echo "Category ID is invalid.";
            exit();
        }
    } else {
        echo "Category ID is invalid.";
        exit();
    }
} else {
    echo "Category ID is missing.";
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        .current-images {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .current-images h4 {
            margin-bottom: 10px;
            color: #333;
        }
        .image-container {
            display: inline-block;
            margin: 5px;
            position: relative;
        }
        .image-container img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .delete-image-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .delete-image-btn:hover {
            background: #c82333;
        }
        .no-images-icon {
            background-color: #ff8c00 !important;
            color: white !important;
        }
        .no-images-icon:hover {
            background-color: #ff7700 !important;
        }
        .has-images-icon {
            background-color: #28a745 !important;
            color: white !important;
        }
        .has-images-icon:hover {
            background-color: #218838 !important;
        }
        .images-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            padding: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .modal-image-item {
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 5px;
            background: #f9f9f9;
        }
        .modal-image-item img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
            transition: transform 0.3s;
        }
        .modal-image-item img:hover {
            transform: scale(1.05);
        }
        .no-images-message {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .image-preview-content {
            margin: auto;
            display: block;
            width: 80%;
            max-width: 700px;
            max-height: 80%;
            object-fit: contain;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Favorite Button Styles */
        .favorite-btn {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto;
            border: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .favorite-btn.not-favorite {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #6c757d;
        }

        .favorite-btn.not-favorite:hover {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
        }

        .favorite-btn.is-favorite {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        .favorite-btn.is-favorite:hover {
            background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.4);
        }

        /* Filter Container Styles - High Priority */
        .filter-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%) !important;
            padding: 25px 30px !important;
            border-radius: 25px !important;
            box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08), 0 4px 15px rgba(63, 81, 181, 0.1) !important;
            margin-bottom: 30px !important;
            display: flex !important;
            align-items: center !important;
            gap: 25px !important;
            flex-wrap: nowrap !important;
            flex-direction: row !important;
            border: 2px solid rgba(63, 81, 181, 0.08) !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .filter-container::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%) !important;
            z-index: 1 !important;
        }

        /* Dark Mode Filter Container */
        [data-theme="dark"] .filter-container {
            background: linear-gradient(135deg, #1a2332 0%, #242b3d 50%, #2a3441 100%) !important;
            box-shadow: 0 10px 35px rgba(0, 0, 0, 0.4), 0 4px 15px rgba(88, 166, 255, 0.15) !important;
            border: 2px solid rgba(88, 166, 255, 0.2) !important;
        }

        [data-theme="dark"] .filter-container::before {
            background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%) !important;
        }

        .filter-label {
            display: flex !important;
            align-items: center !important;
            gap: 15px !important;
            font-weight: 800 !important;
            color: var(--color-primary) !important;
            font-size: 1.2rem !important;
            padding: 12px 20px !important;
            background: linear-gradient(135deg, rgba(63, 81, 181, 0.08) 0%, rgba(63, 81, 181, 0.04) 100%) !important;
            border-radius: 20px !important;
            border: 1px solid rgba(63, 81, 181, 0.15) !important;
            position: relative !important;
            z-index: 2 !important;
        }

        .filter-label i {
            font-size: 1.3rem !important;
            background: linear-gradient(135deg, var(--color-primary) 0%, #58a6ff 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
        }

        /* Dark Mode Filter Label */
        [data-theme="dark"] .filter-label {
            color: #58a6ff !important;
            background: linear-gradient(135deg, rgba(88, 166, 255, 0.12) 0%, rgba(88, 166, 255, 0.06) 100%) !important;
            border: 1px solid rgba(88, 166, 255, 0.25) !important;
        }

        [data-theme="dark"] .filter-label i {
            background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
        }

        .filter-container .filter-btn,
        button.filter-btn {
            width: 60px !important;
            height: 60px !important;
            border: 3px solid rgba(63, 81, 181, 0.2) !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%) !important;
            color: #6c757d !important;
            font-size: 1.3rem !important;
            cursor: pointer !important;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(63, 81, 181, 0.1) !important;
            position: relative !important;
            overflow: hidden !important;
            outline: none !important;
            padding: 0 !important;
            margin: 0 !important;
            min-width: 60px !important;
            min-height: 60px !important;
            max-width: 60px !important;
            max-height: 60px !important;
        }

        /* Dark Mode Filter Buttons */
        [data-theme="dark"] .filter-container .filter-btn,
        [data-theme="dark"] button.filter-btn {
            border: 3px solid rgba(88, 166, 255, 0.3) !important;
            background: linear-gradient(135deg, #242b3d 0%, #2a3441 50%, #1a2332 100%) !important;
            color: #8a9ba8 !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(88, 166, 255, 0.15) !important;
        }

        .filter-container .filter-btn i,
        button.filter-btn i {
            z-index: 2 !important;
            position: relative !important;
        }

        .filter-container .filter-btn.active,
        button.filter-btn.active {
            border-color: var(--color-primary) !important;
            background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 50%, #1976d2 100%) !important;
            color: white !important;
            box-shadow: 0 8px 25px rgba(63, 81, 181, 0.4), 0 4px 15px rgba(63, 81, 181, 0.2) !important;
            transform: scale(1.08) !important;
        }

        .filter-container .filter-btn:hover:not(.active),
        button.filter-btn:hover:not(.active) {
            transform: scale(1.12) translateY(-3px) !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 4px 15px rgba(63, 81, 181, 0.2) !important;
            border-color: var(--color-primary) !important;
            background: linear-gradient(135deg, #ffffff 0%, rgba(63, 81, 181, 0.05) 100%) !important;
        }

        .filter-container .filter-btn:active,
        button.filter-btn:active {
            transform: scale(0.95) !important;
        }

        /* Dark Mode Active and Hover States */
        [data-theme="dark"] .filter-container .filter-btn.active,
        [data-theme="dark"] button.filter-btn.active {
            border-color: #58a6ff !important;
            background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 50%, #1976d2 100%) !important;
            color: white !important;
            box-shadow: 0 8px 25px rgba(88, 166, 255, 0.4), 0 4px 15px rgba(88, 166, 255, 0.2) !important;
            transform: scale(1.08) !important;
        }

        [data-theme="dark"] .filter-container .filter-btn:hover:not(.active),
        [data-theme="dark"] button.filter-btn:hover:not(.active) {
            transform: scale(1.12) translateY(-3px) !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 4px 15px rgba(88, 166, 255, 0.3) !important;
            border-color: #58a6ff !important;
            background: linear-gradient(135deg, #2a3441 0%, rgba(88, 166, 255, 0.1) 100%) !important;
            color: #b8c5d1 !important;
        }

        /* Default Layout for Large Screens */
        .filter-buttons-row {
            display: flex !important;
            gap: 20px !important;
            justify-content: flex-start !important;
            flex-wrap: nowrap !important;
            align-items: center !important;
        }

        /* Special colors for status filter buttons */
        .filter-btn[data-filter="active"] {
            border-color: rgba(40, 167, 69, 0.3) !important;
        }

        .filter-btn[data-filter="active"].active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%) !important;
            border-color: #28a745 !important;
        }

        .filter-btn[data-filter="inactive"] {
            border-color: rgba(220, 53, 69, 0.3) !important;
        }

        .filter-btn[data-filter="inactive"].active {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 50%, #c0392b 100%) !important;
            border-color: #dc3545 !important;
        }

        /* Dark mode status buttons */
        [data-theme="dark"] .filter-btn[data-filter="active"] {
            border-color: rgba(76, 175, 80, 0.4) !important;
        }

        [data-theme="dark"] .filter-btn[data-filter="active"].active {
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 50%, #81c784 100%) !important;
            border-color: #4caf50 !important;
        }

        [data-theme="dark"] .filter-btn[data-filter="inactive"] {
            border-color: rgba(244, 67, 54, 0.4) !important;
        }

        [data-theme="dark"] .filter-btn[data-filter="inactive"].active {
            background: linear-gradient(135deg, #f44336 0%, #ef5350 50%, #e57373 100%) !important;
            border-color: #f44336 !important;
        }

        /* Large Screens - Ensure Horizontal Layout */
        @media (min-width: 769px) {
            .filter-container {
                flex-direction: row !important;
                flex-wrap: nowrap !important;
                align-items: center !important;
            }

            .filter-buttons-row {
                display: flex !important;
                flex-direction: row !important;
                gap: 20px !important;
                justify-content: flex-start !important;
                flex-wrap: nowrap !important;
                align-items: center !important;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) and (min-width: 601px) {
            .filter-container {
                padding: 20px 25px !important;
                gap: 20px !important;
                border-radius: 20px !important;
                flex-direction: row !important;
                flex-wrap: wrap !important;
            }
            
            .filter-container .filter-btn,
            button.filter-btn {
                width: 50px !important;
                height: 50px !important;
                font-size: 1.1rem !important;
                min-width: 50px !important;
                min-height: 50px !important;
                max-width: 50px !important;
                max-height: 50px !important;
            }
            
            .filter-label {
                font-size: 1.1rem !important;
                padding: 10px 16px !important;
                gap: 12px !important;
            }

            .filter-label i {
                font-size: 1.2rem !important;
            }

            .filter-buttons-row {
                display: flex !important;
                gap: 12px !important;
                justify-content: center !important;
                flex-wrap: wrap !important;
            }
        }

        @media (max-width: 600px) {
            .filter-container {
                padding: 15px 20px !important;
                gap: 15px !important;
                flex-direction: column !important;
                align-items: center !important;
            }

            .filter-label {
                margin-bottom: 5px !important;
            }

            .filter-buttons-row {
                justify-content: flex-start !important;
                flex-wrap: nowrap !important;
                overflow-x: auto !important;
                padding: 8px 5px !important;
                width: 100% !important;
                scrollbar-width: none !important;
                -ms-overflow-style: none !important;
                gap: 15px !important;
            }

            .filter-buttons-row::-webkit-scrollbar {
                display: none !important;
            }

            .filter-container .filter-btn,
            button.filter-btn {
                width: 50px !important;
                height: 50px !important;
                font-size: 1.1rem !important;
                min-width: 50px !important;
                min-height: 50px !important;
                max-width: 50px !important;
                max-height: 50px !important;
                flex-shrink: 0 !important;
            }
        }

        @media (max-width: 480px) {
            .filter-container {
                padding: 12px 15px !important;
                gap: 12px !important;
                flex-direction: column !important;
                align-items: center !important;
            }

            .filter-label {
                justify-content: center !important;
                margin-bottom: 5px !important;
                font-size: 0.95rem !important;
                padding: 6px 12px !important;
            }

            .filter-buttons-row {
                display: flex !important;
                justify-content: flex-start !important;
                gap: 12px !important;
                flex-wrap: nowrap !important;
                width: 100% !important;
                overflow-x: auto !important;
                padding: 8px 5px !important;
                scrollbar-width: none !important; /* Firefox */
                -ms-overflow-style: none !important; /* IE and Edge */
            }

            .filter-buttons-row::-webkit-scrollbar {
                display: none !important; /* Chrome, Safari and Opera */
            }

            .filter-container .filter-btn,
            button.filter-btn {
                width: 42px !important;
                height: 42px !important;
                font-size: 0.95rem !important;
                min-width: 42px !important;
                min-height: 42px !important;
                max-width: 42px !important;
                max-height: 42px !important;
                flex-shrink: 0 !important;
            }
        }

        @media (max-width: 360px) {
            .filter-container {
                padding: 10px 12px !important;
                gap: 10px !important;
                flex-direction: column !important;
                align-items: center !important;
            }

            .filter-label {
                font-size: 0.85rem !important;
                padding: 5px 10px !important;
                margin-bottom: 3px !important;
            }

            .filter-buttons-row {
                gap: 10px !important;
                justify-content: flex-start !important;
                overflow-x: auto !important;
                padding: 6px 3px !important;
                width: 100% !important;
                scrollbar-width: none !important; /* Firefox */
                -ms-overflow-style: none !important; /* IE and Edge */
            }

            .filter-buttons-row::-webkit-scrollbar {
                display: none !important; /* Chrome, Safari and Opera */
            }

            .filter-container .filter-btn,
            button.filter-btn {
                width: 38px !important;
                height: 38px !important;
                font-size: 0.85rem !important;
                min-width: 38px !important;
                min-height: 38px !important;
                max-width: 38px !important;
                max-height: 38px !important;
                flex-shrink: 0 !important;
            }
        }

        /* Extra small screens - ensure all 6 buttons are visible */
        @media (max-width: 320px) {
            .filter-container {
                padding: 8px 10px !important;
                gap: 8px !important;
            }

            .filter-label {
                font-size: 0.8rem !important;
                padding: 4px 8px !important;
            }

            .filter-buttons-row {
                gap: 8px !important;
                padding: 5px 2px !important;
            }

            .filter-container .filter-btn,
            button.filter-btn {
                width: 35px !important;
                height: 35px !important;
                font-size: 0.8rem !important;
                min-width: 35px !important;
                min-height: 35px !important;
                max-width: 35px !important;
                max-height: 35px !important;
            }
        }

        /* Similar Items Styles */
        .similar-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .similar-item:hover {
            background-color: #f5f5f5;
            border-color: #ccc;
        }

        .similar-item.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .similar-item-checkbox {
            margin-left: 10px;
        }

        .similar-item-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .similar-item-name {
            font-weight: bold;
            color: #333;
        }

        .similar-item-store {
            color: #666;
            font-size: 0.9em;
        }

        .similar-item-barcode {
            color: #888;
            font-size: 0.85em;
            font-family: 'Courier New', monospace;
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            margin: 2px 0;
        }

        .similar-item-details {
            display: flex;
            gap: 15px;
            font-size: 0.85em;
            color: #777;
        }

        .loading-similar-items {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-similar-items {
            text-align: center;
            padding: 20px;
            color: #999;
            font-style: italic;
        }

        /* Dark Mode Support for Similar Items */
        [data-theme="dark"] .similar-item {
            background-color: #2a3441;
            border-color: #3a4a5c;
        }

        [data-theme="dark"] .similar-item:hover {
            background-color: #3a4a5c;
            border-color: #4a5a6c;
        }

        [data-theme="dark"] .similar-item.selected {
            background-color: #1e3a5f;
            border-color: #58a6ff;
        }

        [data-theme="dark"] .similar-item-name {
            color: #e6edf3;
        }

        [data-theme="dark"] .similar-item-store {
            color: #8a9ba8;
        }

        [data-theme="dark"] .similar-item-barcode {
            color: #8a9ba8;
            background-color: #3a4a5c;
        }

        [data-theme="dark"] .similar-item-details {
            color: #8a9ba8;
        }

        [data-theme="dark"] .loading-similar-items {
            color: #8a9ba8;
        }

        [data-theme="dark"] .no-similar-items {
            color: #6e7681;
        }

        /* Dark Mode Support for Other Stores Edit Container */
        [data-theme="dark"] #other_stores_items {
            background-color: #1a2332;
            border-color: #3a4a5c;
        }

        [data-theme="dark"] #other_stores_items h4 {
            color: #8a9ba8;
        }

        [data-theme="dark"] #similar_items_list {
            background-color: #242b3d;
            border: 1px solid #3a4a5c;
            border-radius: 5px;
        }

        /* Dark Mode for Edit Modal Container */
        [data-theme="dark"] .modal-content div[style*="background-color: #f9f9f9"] {
            background-color: #1a2332 !important;
            border-color: #3a4a5c !important;
        }

        [data-theme="dark"] .modal-content label[style*="color: #333"] span {
            color: #e6edf3 !important;
        }

        [data-theme="dark"] .modal-content .fas.fa-info-circle {
            color: #58a6ff !important;
        }

        /* Other Stores Edit Container Styles */
        .other-stores-edit-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        .other-stores-edit-label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .other-stores-edit-text {
            font-weight: bold;
            color: #333;
        }

        .other-stores-info-icon {
            color: #666;
        }

        .other-stores-items-container {
            display: none;
            margin-top: 15px;
        }

        .other-stores-items-title {
            color: #666;
            margin-bottom: 10px;
        }

        .similar-items-list {
            max-height: 200px;
            overflow-y: auto;
        }

        /* Dark Mode for Other Stores Edit Container */
        [data-theme="dark"] .other-stores-edit-container {
            background-color: #1a2332;
            border-color: #3a4a5c;
        }

        [data-theme="dark"] .other-stores-edit-text {
            color: #e6edf3;
        }

        [data-theme="dark"] .other-stores-info-icon {
            color: #58a6ff;
        }

        [data-theme="dark"] .other-stores-items-container {
            background-color: #1a2332;
        }

        [data-theme="dark"] .other-stores-items-title {
            color: #8a9ba8;
        }

        [data-theme="dark"] .similar-items-list {
            background-color: #242b3d;
            border: 1px solid #3a4a5c;
            border-radius: 5px;
        }

        /* Dark Mode for Checkbox */
        [data-theme="dark"] .other-stores-edit-label input[type="checkbox"] {
            accent-color: #58a6ff;
        }

        [data-theme="dark"] .similar-item-checkbox {
            accent-color: #58a6ff;
        }

        /* Dark Mode Scrollbar for Similar Items List */
        [data-theme="dark"] .similar-items-list::-webkit-scrollbar {
            width: 8px;
        }

        [data-theme="dark"] .similar-items-list::-webkit-scrollbar-track {
            background: #1a2332;
            border-radius: 4px;
        }

        [data-theme="dark"] .similar-items-list::-webkit-scrollbar-thumb {
            background: #3a4a5c;
            border-radius: 4px;
        }

        [data-theme="dark"] .similar-items-list::-webkit-scrollbar-thumb:hover {
            background: #4a5a6c;
        }
    </style>
</head>
<body>

    <?php include 'sidebar.php'; ?>

<div class="container">
    <h2>قائمة الأصناف</h2>
    <h3>الفرع: <?php echo htmlspecialchars($store_name); ?></h3>

    <!-- زر إضافة صنف جديد - يظهر فقط للمستخدمين الذين لديهم صلاحية إضافة صنف -->
    <?php if (hasPermission('items', 'add_item_from_items')): ?>
        <button class="add-btn" onclick="showAddItemForm()">+</button>
    <?php endif; ?>
    <input type="text" id="searchField" class="search-bar" placeholder="ابحث عن صنف...">

    <!-- Filter Container -->
    <div class="filter-container">
        <div class="filter-label">
            <i class="fas fa-filter"></i>
            <span>فلترة:</span>
        </div>
        <div class="filter-buttons-row">
            <button class="filter-btn active" data-filter="all" title="جميع الأصناف">
                <i class="fas fa-list"></i>
            </button>
            <button class="filter-btn" data-filter="favorites" title="المفضلة فقط">
                <i class="fas fa-star"></i>
            </button>
            <button class="filter-btn" data-filter="active" title="الأصناف النشطة">
                <i class="fas fa-check-circle"></i>
            </button>
            <button class="filter-btn" data-filter="inactive" title="الأصناف المتوقفة">
                <i class="fas fa-pause-circle"></i>
            </button>
            <button class="filter-btn" data-filter="with-images" title="مع صور">
                <i class="fas fa-images"></i>
            </button>
            <button class="filter-btn" data-filter="no-images" title="بدون صور">
                <i class="fas fa-image"></i>
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="items-table">
            <thead>
                <tr>
                    <th>المفضلة</th>
                    <th>اسم الصنف</th>
                    <th>التكلفة</th>
                    <th>سعر البيع</th>
                    <th>الكمية</th>
                    <th>الباركود</th>
                    <th>نوع الصنف</th>
                    <th>عدد القطع داخل الكرتونة</th>
                    <th>الحالة</th>
                    <th>الصور</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
                <tbody id="itemsTable">
                    <?php
                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            $quantity_unit = '';
                            if ($row['type'] == 'box') {
                                $quantity_unit = ' كرتونة';
                            } elseif ($row['type'] == 'fridge') {
                                $quantity_unit = ' كيلو';
                            } elseif ($row['type'] == 'piece' || $row['type'] == 'other') {
                                $quantity_unit = ' قطعة';
                            }
                            $encrypted_item_id = encrypt($row['item_id'], $key);
                            $status_class = $row['status'] === 'active' ? 'confirmed' : 'pending';
                            $status_text = $row['status'] === 'active' ? 'نشط' : 'متوقف';
                            $image_class = $row['image_count'] > 0 ? 'has-images-icon' : 'no-images-icon';
                            $image_title = $row['image_count'] > 0 ? 'يحتوي على ' . $row['image_count'] . ' صورة/صور' : 'لا يحتوي على صور';
                            $is_favorite = $row['is_favorite'] > 0;
                            $favorite_class = $is_favorite ? 'is-favorite' : 'not-favorite';
                            $favorite_title = $is_favorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة';
                            echo "<tr id='item-{$row['item_id']}' onclick='handleRowClick(event, this)'>
                                    <td onclick='event.stopPropagation()' style='text-align: center;'>
                                        <button class='favorite-btn $favorite_class' data-item-id='{$row['item_id']}' title='$favorite_title' onclick='toggleFavorite({$row['item_id']}, this, event)'><i class='fas fa-star'></i></button>
                                    </td>
                                    <td>" . htmlspecialchars($row['name']) . "</td>
                                    <td>" . htmlspecialchars($row['cost']) . "</td>
                                    <td>" . htmlspecialchars($row['price']) . "</td>
                                    <td>" . htmlspecialchars($row['quantity']) . $quantity_unit . "</td>
                                    <td>" . htmlspecialchars($row['barcode']) . "</td>
                                    <td>" . htmlspecialchars($row['type']) . "</td>
                                    <td>" . htmlspecialchars($row['pieces_per_box']) . "</td>
                                    <td class='status-cell $status_class'" . (hasPermission('items', 'change_item_status') ? " onclick='event.stopPropagation(); toggleItemStatus({$row["item_id"]}, \"{$row["status"]}\")'" : "") . ">
                                        <span class='status-frame $status_class'>$status_text</span>
                                    </td>
                                    <td onclick='event.stopPropagation()'>
                                        <button class='action-btn $image_class' type='button' title='$image_title' onclick='viewItemImages(\"" . htmlspecialchars($encrypted_item_id) . "\")'><i class='fas fa-images'></i></button>
                                    </td>
                                    <td onclick='event.stopPropagation()'>";
                            
                            // زر التعديل - يظهر فقط للمستخدمين الذين لديهم صلاحية تعديل الأصناف
                            if (hasPermission('items', 'edit_item')) {
                                echo "<button class='action-btn' type='button' onclick='editItem(\"" . htmlspecialchars($encrypted_item_id) . "\")'><i class='fas fa-edit'></i></button>";
                            }
                            
                            // زر الحذف - يظهر فقط للمستخدمين الذين لديهم صلاحية حذف الأصناف
                            if (hasPermission('items', 'delete_item')) {
                                echo "<button class='action-btn' type='button' onclick='deleteItem(" . htmlspecialchars($row['item_id']) . ")'><i class='fas fa-trash-alt'></i></button>";
                            }
                            
                            echo "</td>
                                  </tr>";
                        }
                    } else {
                        echo "<tr><td colspan='11'>لا توجد أصناف حالياً</td></tr>";
                    }
                    ?></tbody>
            </table>
        </div>
    </div>
</div>

<div id="editItemModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span><br>
        <h2>تعديل تفاصيل الصنف</h2>
        <form method="POST" action="edit_item.php" enctype="multipart/form-data" style="width: 100%; max-width: 500px; margin: 50 auto;">
            <input type="hidden" name="edit_item_id" id="edit_item_id">
            <input type="hidden" name="category_id" value="<?php echo isset($encrypted_category_id) ? htmlspecialchars($encrypted_category_id) : ''; ?>">

            <input type="hidden" name="item_type" id="edit_item_type" readonly>

            <label for="edit_barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="edit_barcode" class="input-field" placeholder="باركود المنتج">

            <label for="edit_item_images">صور الصنف:</label>
            <input type="file" name="item_images[]" id="edit_item_images" class="input-field" multiple accept="image/*">
            <small>يمكنك اختيار صور جديدة للصنف</small>
            
            <div id="current_images" class="current-images">
                <!-- سيتم عرض الصور الحالية هنا -->
            </div>

            <div id="editPieceFields" class="item-fields">
                <label for="edit_item_name_piece">اسم الصنف:</label>
                <input type="text" name="item_name_piece" id="edit_item_name_piece" class="input-field" placeholder="اسم الصنف">

                <label for="edit_cost_piece">تكلفة المنتج:</label>
                <input type="number" step="0.01" name="cost_piece" id="edit_cost_piece" class="input-field" placeholder="جملة الصنف">

                <label for="edit_price1_piece">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_piece" id="edit_price1_piece" class="input-field" placeholder="سعر القطاعي">

                <label for="edit_quantity_piece">الكمية:</label>
                <input type="number" step="0.01" name="quantity_piece" id="edit_quantity_piece" class="input-field" placeholder="الكمية">
            </div>

            <div id="editBoxFields" class="item-fields">
                <label for="edit_item_name_box">اسم الكرتونة:</label>
                <input type="text" name="item_name_box" id="edit_item_name_box" class="input-field" placeholder="اسم الكرتونة">

                <label for="edit_cost_box">تكلفة الكرتونة:</label>
                <input type="number" step="0.01" name="cost_box" id="edit_cost_box" class="input-field" placeholder="جملة الكرتونة">

                <label for="edit_price1_box">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_box" id="edit_price1_box" class="input-field" placeholder="سعر القطاعي">

                <label for="edit_pieces_per_box">عدد القطع داخل الكرتونة:</label>
                <input type="number" step="0.01" name="pieces_per_box" id="edit_pieces_per_box" class="input-field" placeholder="عدد القطع">

                <label for="edit_quantity_box">كمية الكراتين:</label>
                <input type="number" step="0.01" name="quantity_box" id="edit_quantity_box" class="input-field" placeholder="كمية الكراتين">
            </div>

            <div id="editFridgeFields" class="item-fields">
                <label for="edit_item_name_fridge">اسم الصنف:</label>
                <input type="text" name="item_name_fridge" id="edit_item_name_fridge" class="input-field" placeholder="اسم الصنف">

                <label for="edit_cost_fridge">سعر الكيلو جملة:</label>
                <input type="number" step="0.01" name="cost_fridge" id="edit_cost_fridge" class="input-field" placeholder="سعر الكيلو جملة">

                <label for="edit_price1_fridge">سعر الكيلو قطاعي:</label>
                <input type="number" step="0.01" name="price1_fridge" id="edit_price1_fridge" class="input-field" placeholder="سعر الكيلو قطاعي">

                <label for="edit_quantity_fridge">الوزن الموجود:</label>
                <input type="number" step="0.01" name="quantity_fridge" id="edit_quantity_fridge" class="input-field" placeholder="الوزن الموجود">
            </div>

            <!-- خيار التعديل في فروع أخرى -->
            <div class="other-stores-edit-container">
                <label class="other-stores-edit-label">
                    <input type="checkbox" id="edit_in_other_stores" onchange="toggleOtherStoresEdit()">
                    <span class="other-stores-edit-text">تعديل في فروع أخرى</span>
                    <i class="fas fa-info-circle other-stores-info-icon" title="البحث عن أصناف بنفس الباركود في فروع أخرى وتعديلها معاً"></i>
                </label>
                <div id="other_stores_items" class="other-stores-items-container">
                    <h4 class="other-stores-items-title">الأصناف المشابهة في فروع أخرى:</h4>
                    <div id="similar_items_list" class="similar-items-list">
                        <!-- سيتم عرض الأصناف المشابهة هنا -->
                    </div>
                </div>
            </div>

            <button type="submit" class="add-btn">تعديل الصنف</button>
        </form>
    </div>
</div>

<div id="addItemModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeAddItemForm()">&times;</span><br>
        <h2>إضافة صنف جديد</h2>
        <form method="POST" action="add_item_from_items.php" enctype="multipart/form-data" style="width: 100%; max-width: 500px; margin: 50 auto;">
            <input type="hidden" name="category_id" value="<?php echo isset($re_encrypted_category_id) ? htmlspecialchars($re_encrypted_category_id) : ''; ?>">

            <label for="item_type">نوع الصنف:</label>
            <select name="item_type" id="item_type" class="input-field" onchange="toggleAddItemFields()">
                <option value="piece">قطعة</option>
                <option value="box">كرتونة</option>
                <option value="fridge">ثلاجة</option>
                <option value="other">أخرى</option>
            </select>

            <div id="addPieceFields" class="item-fields">
                <label for="item_name_piece">اسم الصنف:</label>
                <input type="text" name="item_name_piece" id="item_name_piece" class="input-field" placeholder="اسم الصنف">

                <label for="cost_piece">تكلفة المنتج:</label>
                <input type="number" step="0.01" name="cost_piece" id="cost_piece" class="input-field" placeholder="جملة الصنف">

                <label for="price1_piece">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_piece" id="price1_piece" class="input-field" placeholder="سعر القطاعي">

                <label for="quantity_piece">الكمية:</label>
                <input type="number" step="0.01" name="quantity_piece" id="quantity_piece" class="input-field" placeholder="الكمية">
            </div>

            <div id="addBoxFields" class="item-fields" style="display: none;">
                <label for="item_name_box">اسم الكرتونة:</label>
                <input type="text" name="item_name_box" id="item_name_box" class="input-field" placeholder="اسم الكرتونة">

                <label for="cost_box">تكلفة الكرتونة:</label>
                <input type="number" step="0.01" name="cost_box" id="cost_box" class="input-field" placeholder="جملة الكرتونة">

                <label for="price1_box">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_box" id="price1_box" class="input-field" placeholder="سعر القطاعي">

                <label for="pieces_per_box">عدد القطع داخل الكرتونة:</label>
                <input type="number" step="0.01" name="pieces_per_box" id="pieces_per_box" class="input-field" placeholder="عدد القطع">

                <label for="quantity_box">كمية الكراتين:</label>
                <input type="number" step="0.01" name="quantity_box" id="quantity_box" class="input-field" placeholder="كمية الكراتين">
            </div>

            <div id="addFridgeFields" class="item-fields" style="display: none;">
                <label for="item_name_fridge">اسم الصنف:</label>
                <input type="text" name="item_name_fridge" id="item_name_fridge" class="input-field" placeholder="اسم الصنف">

                <label for="cost_fridge">سعر الكيلو جملة:</label>
                <input type="number" step="0.01" name="cost_fridge" id="cost_fridge" class="input-field" placeholder="سعر الكيلو جملة">

                <label for="price1_fridge">سعر الكيلو قطاعي:</label>
                <input type="number" step="0.01" name="price1_fridge" id="price1_fridge" class="input-field" placeholder="سعر الكيلو قطاعي">

                <label for="quantity_fridge">الوزن الموجود:</label>
                <input type="number" step="0.01" name="quantity_fridge" id="quantity_fridge" class="input-field" placeholder="الوزن الموجود">
            </div>

            <label for="barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="barcode" class="input-field" placeholder="باركود المنتج" disabled>

            <label>
                <input type="checkbox" name="auto_generate_barcode" id="auto_generate_barcode" onclick="toggleBarcode()" checked> تحديد الباركود تلقائي
            </label>
            <div id="barcode_error" class="error-message"></div>

            <label for="item_images">صور الصنف:</label>
            <input type="file" name="item_images[]" id="item_images" class="input-field" multiple accept="image/*">
            <small>يمكنك اختيار عدة صور للصنف</small>

            <button type="submit" class="add-btn">إضافة الصنف</button>
        </form>
    </div>
</div>

<!-- Modal for viewing item images -->
<div id="viewImagesModal" class="modal">
    <div class="modal-content" style="max-width: 800px;">
        <span class="close" onclick="closeViewImagesModal()">&times;</span>
        <h2 id="modalItemName">صور الصنف</h2>
        <div id="modalImagesContainer" class="images-gallery">
            <!-- سيتم عرض الصور هنا -->
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <button id="viewAllImagesBtn" class="add-btn" onclick="openFullImagePage()">
                <i class="fas fa-expand"></i> عرض جميع الصور في صفحة منفصلة
            </button>
        </div>
    </div>
</div>

<!-- Modal for image preview -->
<div id="imagePreviewModal" class="image-preview-modal">
    <span class="close" onclick="closeImagePreview()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
    <img class="image-preview-content" id="previewImage">
</div>

<script>
    // متغيرات الصلاحيات
    const permissions = {
        canCreate: <?php echo hasPermission('items', 'add_item_from_items') ? 'true' : 'false'; ?>,
        canEdit: <?php echo hasPermission('items', 'edit_item') ? 'true' : 'false'; ?>,
        canDelete: <?php echo hasPermission('items', 'delete_item') ? 'true' : 'false'; ?>,
        canChangeStatus: <?php echo hasPermission('items', 'change_item_status') ? 'true' : 'false'; ?>
    };

    var editItemModal = document.getElementById("editItemModal");
    var addItemModal = document.getElementById("addItemModal");
    var editItemSpan = document.getElementsByClassName("close")[0];

    /* Updated modal display using "active" class */
    editItemSpan.onclick = function() {
        editItemModal.classList.remove("active"); // instead of .style.display = "none"
    }

    // Consolidated window click handler to avoid conflicts
    window.onclick = function(event) {
        // Handle edit modal
        if (event.target == editItemModal) {
            editItemModal.classList.remove("active");
        }
        
        // Handle add modal
        if (event.target == addItemModal) {
            addItemModal.classList.remove("active");
        }
        
        // Handle view images modal
        const viewImagesModal = document.getElementById('viewImagesModal');
        if (event.target == viewImagesModal) {
            viewImagesModal.classList.remove('active');
        }
        
        // Handle image preview modal
        const imagePreviewModal = document.getElementById('imagePreviewModal');
        if (event.target == imagePreviewModal) {
            closeImagePreview();
        }
    }

    function editItem(encryptedItemId) {
        // فحص الصلاحية
        if (!permissions.canEdit) {
            Swal.fire({
                icon: 'warning',
                title: 'غير مصرح',
                text: 'ليس لديك صلاحية لتعديل الأصناف'
            });
            return;
        }

        // Disable SSE connection
        if (typeof eventSource !== "undefined") {
            eventSource.close();
        }

        // Fetch data for the selected item using AJAX
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('edit_item_id').value = encryptedItemId;
                    document.getElementById('edit_item_type').value = data.item.type;
                    document.getElementById('edit_barcode').value = data.item.barcode;
                    
                    // Reset arrays for tracking changes
                    imagesToDelete = [];
                    currentItemImages = data.item.images || [];
                    similarItemsData = [];
                    selectedSimilarItems = [];
                    
                    // إعادة تعيين خيار التعديل في فروع أخرى
                    document.getElementById('edit_in_other_stores').checked = false;
                    document.getElementById('other_stores_items').style.display = 'none';
                    
                    // Display current images
                    const currentImagesDiv = document.getElementById('current_images');
                    currentImagesDiv.innerHTML = '';
                    if (data.item.images && data.item.images.length > 0) {
                        const imagesTitle = document.createElement('h4');
                        imagesTitle.textContent = 'الصور الحالية:';
                        currentImagesDiv.appendChild(imagesTitle);
                        
                        data.item.images.forEach(image => {
                            const imageContainer = document.createElement('div');
                            imageContainer.style.display = 'inline-block';
                            imageContainer.style.margin = '5px';
                            imageContainer.style.position = 'relative';
                            
                            const img = document.createElement('img');
                            img.src = image.img_path;
                            img.style.width = '100px';
                            img.style.height = '100px';
                            img.style.objectFit = 'cover';
                            img.style.border = '1px solid #ddd';
                            img.style.borderRadius = '5px';
                            
                            const deleteBtn = document.createElement('button');
                            deleteBtn.innerHTML = '×';
                            deleteBtn.style.position = 'absolute';
                            deleteBtn.style.top = '-5px';
                            deleteBtn.style.right = '-5px';
                            deleteBtn.style.background = 'red';
                            deleteBtn.style.color = 'white';
                            deleteBtn.style.border = 'none';
                            deleteBtn.style.borderRadius = '50%';
                            deleteBtn.style.width = '20px';
                            deleteBtn.style.height = '20px';
                            deleteBtn.style.cursor = 'pointer';
                            deleteBtn.onclick = function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                markImageForDeletion(image.img_id, imageContainer);
                            };
                            
                            imageContainer.appendChild(img);
                            imageContainer.appendChild(deleteBtn);
                            currentImagesDiv.appendChild(imageContainer);
                        });
                    }
                    
                    toggleEditItemFields();

                    if (data.item.type === 'piece' || data.item.type === 'other') {
                        document.getElementById('edit_item_name_piece').value = data.item.name;
                        document.getElementById('edit_cost_piece').value = data.item.cost;
                        document.getElementById('edit_price1_piece').value = data.item.price;
                        document.getElementById('edit_quantity_piece').value = data.item.quantity;
                    } else if (data.item.type === 'box') {
                        document.getElementById('edit_item_name_box').value = data.item.name;
                        document.getElementById('edit_cost_box').value = data.item.cost;
                        document.getElementById('edit_price1_box').value = data.item.price;
                        document.getElementById('edit_quantity_box').value = data.item.quantity;
                        document.getElementById('edit_pieces_per_box').value = data.item.pieces_per_box;
                    } else if (data.item.type === 'fridge') {
                        document.getElementById('edit_item_name_fridge').value = data.item.name;
                        document.getElementById('edit_cost_fridge').value = data.item.cost;
                        document.getElementById('edit_price1_fridge').value = data.item.price;
                        document.getElementById('edit_quantity_fridge').value = data.item.quantity;
                    }

                    // Display modal using active class
                    editItemModal.classList.add("active"); // instead of .style.display = "block"
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'فشل في جلب تفاصيل الصنف.'
                    });
                }
            })
            .catch(error => {
                console.error("Error fetching item:", error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب تفاصيل الصنف.'
                });
            });
    }

    function deleteItem(itemId) {
        // فحص الصلاحية
        if (!permissions.canDelete) {
            Swal.fire({
                icon: 'warning',
                title: 'غير مصرح',
                text: 'ليس لديك صلاحية لحذف الأصناف'
            });
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفه!'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('delete_item_id', itemId);

                fetch('delete_item.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`item-${itemId}`).remove();
                        Swal.fire(
                            'تم الحذف!',
                            'تم حذف الصنف بنجاح.',
                            'success'
                        );
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء الحذف.'
                    });
                });
            }
        });
    }

    document.getElementById('searchField').onkeyup = function() {
        var filter = this.value.toLowerCase();
        var rows = document.querySelectorAll('#itemsTable tr');
        rows.forEach(row => {
            var name = row.cells[1].textContent.toLowerCase(); // اسم الصنف في العمود الثاني الآن
            var barcode = row.cells[5].textContent.toLowerCase(); // الباركود في العمود السادس
            var type = row.cells[6].textContent.toLowerCase(); // نوع الصنف في العمود السابع
            if (name.includes(filter) || barcode.includes(filter) || type.includes(filter)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    function toggleCheckbox(row) {
        var checkbox = row.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
        }
    }
    
    // Improved event handling to prevent conflicts between row clicks and button clicks
    function handleRowClick(event, row) {
        // Check if the click was on a button or its child elements (including favorite button)
        if (event.target.closest('button') || 
            event.target.closest('.status-cell') || 
            event.target.closest('.favorite-btn') ||
            event.target.classList.contains('fas')) {
            return; // Don't toggle checkbox if clicking on buttons or status
        }
        toggleCheckbox(row);
    }

    function toggleEditItemFields() {
        var itemType = document.getElementById("edit_item_type").value;
        var pieceFields = document.getElementById("editPieceFields");
        var boxFields = document.getElementById("editBoxFields");
        var fridgeFields = document.getElementById("editFridgeFields");

        pieceFields.style.display = "none";
        boxFields.style.display = "none";
        fridgeFields.style.display = "none";

        if (itemType === "piece" || itemType === "other") {
            pieceFields.style.display = "block";
        } else if (itemType === "box") {
            boxFields.style.display = "block";
        } else if (itemType === "fridge") {
            fridgeFields.style.display = "block";
        }
    }

    function showAddItemForm() {
        // فحص الصلاحية
        if (!permissions.canCreate) {
            Swal.fire({
                icon: 'warning',
                title: 'غير مصرح',
                text: 'ليس لديك صلاحية لإنشاء أصناف جديدة'
            });
            return;
        }

        addItemModal.classList.add("active"); // instead of .style.display = "block"
    }

    function closeAddItemForm() {
        addItemModal.classList.remove("active"); // instead of .style.display = "none"
    }

    function toggleAddItemFields() {
        var itemType = document.getElementById("item_type").value;
        var pieceFields = document.getElementById("addPieceFields");
        var boxFields = document.getElementById("addBoxFields");
        var fridgeFields = document.getElementById("addFridgeFields");

        pieceFields.style.display = "none";
        boxFields.style.display = "none";
        fridgeFields.style.display = "none";

        if (itemType === "piece" || itemType === "other") {
            pieceFields.style.display = "block";
        } else if (itemType === "box") {
            boxFields.style.display = "block";
        } else if (itemType === "fridge") {
            fridgeFields.style.display = "block";
        }
    }

    function generateBarcode(inputId) {
        var inputField = document.getElementById(inputId);
        var randomBarcode = Math.floor(Math.random() * 1000000); // Generate a random 6-digit number
        inputField.value = randomBarcode;
    }

    function toggleBarcodeField(inputId, isChecked) {
        var inputField = document.getElementById(inputId);
        if (isChecked) {
            inputField.value = '';
            inputField.readOnly = true;
        } else {
            inputField.value = '';
            inputField.readOnly = false;
        }
    }

    function toggleBarcode() {
        var barcodeField = document.getElementById("barcode");
        var autoGenerateCheckbox = document.getElementById("auto_generate_barcode");
        if (autoGenerateCheckbox.checked) {
            barcodeField.disabled = true;
        } else {
            barcodeField.disabled = false;
        }
    }

    document.querySelector('form[action="add_item_from_items.php"]').addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('add_item_from_items.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: data.message,
                    showConfirmButton: false,
                    timer: 3000
                });
                addItemModal.classList.remove("active"); // hide modal using active class
                this.reset(); // Clear the form fields

                // Fetch the updated items list and update the table
                fetch(`get_items.php?category_id=<?php echo urlencode($encrypted_category_id); ?>`)
                    .then(response => response.json())
                    .then(data => {
                        const itemsTable = document.getElementById("itemsTable");
                        itemsTable.innerHTML = "";

                        data.items.forEach(item => {
                            const encryptedItemId = '<?php echo encrypt("ITEM_ID_PLACEHOLDER", $key); ?>'.replace('ITEM_ID_PLACEHOLDER', item.item_id);
                            let quantity_unit = '';
                            if (item.type === 'box') {
                                quantity_unit = ' كرتونة';
                            } else if (item.type === 'fridge') {
                                quantity_unit = ' كيلو';
                            } else if (item.type === 'piece') {
                                quantity_unit = ' قطعة';
                            }
                            const imageClass = item.image_count > 0 ? 'has-images-icon' : 'no-images-icon';
                            const imageTitle = item.image_count > 0 ? `يحتوي على ${item.image_count} صورة/صور` : 'لا يحتوي على صور';
                            const isFavorite = item.is_favorite > 0;
                            const favoriteClass = isFavorite ? 'is-favorite' : 'not-favorite';
                            const favoriteTitle = isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة';
                            
                            // تحديث مجموعة المفضلة
                            if (isFavorite) {
                                userFavorites.add(item.item_id);
                            }
                            
                            const statusClass = item.status === 'active' ? 'confirmed' : 'pending';
                            const statusText = item.status === 'active' ? 'نشط' : 'متوقف';
                            
                            const newRow = `<tr id='item-${item.item_id}' onclick='handleRowClick(event, this)'>
                                                <td onclick='event.stopPropagation()' style='text-align: center;'>
                                                    <button class='favorite-btn ${favoriteClass}' data-item-id='${item.item_id}' title='${favoriteTitle}' onclick='toggleFavorite(${item.item_id}, this, event)'><i class='fas fa-star'></i></button>
                                                </td>
                                                <td>${item.name}</td>
                                                <td>${item.cost}</td>
                                                <td>${item.price}</td>
                                                <td>${item.quantity}${quantity_unit}</td>
                                                <td>${item.barcode}</td>
                                                <td>${item.type}</td>
                                                <td>${item.pieces_per_box}</td>
                                                <td class='status-cell ${statusClass}' onclick='event.stopPropagation(); toggleItemStatus(${item.item_id}, "${item.status}")'><span class='status-frame ${statusClass}'>${statusText}</span></td>
                                                <td onclick='event.stopPropagation()'>
                                                    <button class='action-btn ${imageClass}' type='button' title='${imageTitle}' onclick='viewItemImages("${encryptedItemId}")'><i class='fas fa-images'></i></button>
                                                </td>
                                                <td onclick='event.stopPropagation()'>
                                                    <button class='action-btn' type='button' onclick='editItem("${encryptedItemId}")'><i class='fas fa-edit'></i></button>
                                                    <button class='action-btn' type='button' onclick='deleteItem(${item.item_id})'><i class='fas fa-trash-alt'></i></button>
                                                </td>
                                            </tr>`;
                            itemsTable.insertAdjacentHTML('beforeend', newRow);
                        });
                        
                        // إعادة تطبيق الفلتر الحالي
                        applyCurrentFilter();
                    })
                    .catch(error => console.error('Error fetching items:', error));
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء إضافة الصنف.'
            });
        });
    });

    document.querySelector('form[action="edit_item.php"]').addEventListener('submit', function(event) {
        event.preventDefault();
        
        const editInOtherStores = document.getElementById('edit_in_other_stores').checked;
        
        if (editInOtherStores && selectedSimilarItems.length > 0) {
            // التحديث المتعدد
            handleMultipleItemsUpdate();
        } else {
            // التحديث العادي
            handleSingleItemUpdate();
        }
    });

    function handleSingleItemUpdate() {
        const formData = new FormData(document.querySelector('form[action="edit_item.php"]'));
        
        // Add images to delete to form data
        if (imagesToDelete.length > 0) {
            formData.append('images_to_delete', JSON.stringify(imagesToDelete));
        }

        fetch('edit_item.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم تعديل الصنف بنجاح',
                    showConfirmButton: false,
                    timer: 3000
                });
                editItemModal.classList.remove("active");
                setTimeout(() => {
                    location.reload();
                }, 3000);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.message || 'حدث خطأ أثناء تعديل الصنف.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء تعديل الصنف.'
            });
        });
    }

    function handleMultipleItemsUpdate() {
        const form = document.querySelector('form[action="edit_item.php"]');
        const formData = new FormData(form);
        
        // جمع البيانات المطلوبة ل��تحديث المتعدد
        const itemType = document.getElementById('edit_item_type').value;
        let itemName, cost, price, quantity, piecesPerBox = 0;
        
        if (itemType === 'piece' || itemType === 'other') {
            itemName = document.getElementById('edit_item_name_piece').value;
            cost = document.getElementById('edit_cost_piece').value;
            price = document.getElementById('edit_price1_piece').value;
            quantity = document.getElementById('edit_quantity_piece').value;
        } else if (itemType === 'box') {
            itemName = document.getElementById('edit_item_name_box').value;
            cost = document.getElementById('edit_cost_box').value;
            price = document.getElementById('edit_price1_box').value;
            quantity = document.getElementById('edit_quantity_box').value;
            piecesPerBox = document.getElementById('edit_pieces_per_box').value;
        } else if (itemType === 'fridge') {
            itemName = document.getElementById('edit_item_name_fridge').value;
            cost = document.getElementById('edit_cost_fridge').value;
            price = document.getElementById('edit_price1_fridge').value;
            quantity = document.getElementById('edit_quantity_fridge').value;
        }
        
        const barcode = document.getElementById('edit_barcode').value;
        
        // إعداد البيانات للإرسال
        const updateData = new FormData();
        updateData.append('main_item_id', document.getElementById('edit_item_id').value);
        updateData.append('selected_items', JSON.stringify(selectedSimilarItems));
        updateData.append('item_name', itemName);
        updateData.append('cost', cost);
        updateData.append('price', price);
        updateData.append('barcode', barcode);
        updateData.append('pieces_per_box', piecesPerBox);
        
        // عرض تأكيد للمستخدم
        const selectedCount = selectedSimilarItems.length + 1; // +1 للصنف الحالي
        Swal.fire({
            title: 'تأكيد التحديث',
            text: `سيتم تحديث ${selectedCount} صنف في فروع مختلفة. هل أنت متأكد؟`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، حدث الكل',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('edit_item.php', {
                    method: 'POST',
                    body: updateData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = `تم تحديث ${data.updated_count} صنف بنجاح`;
                        
                        // إضافة معلومات الإشعارات إذا وجدت
                        if (data.price_change_notifications) {
                            message += `\nتم إرسال ${data.price_change_notifications} إشعار تغيير سعر`;
                        }
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'تم التحديث بنجاح',
                            text: message,
                            showConfirmButton: false,
                            timer: 4000
                        });
                        editItemModal.classList.remove("active");
                        setTimeout(() => {
                            location.reload();
                        }, 3000);
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message || 'حدث خطأ أثناء التحديث المتعدد.'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء التحديث المتعدد.'
                    });
                });
            }
        });
    }

    function toggleItemStatus(itemId, currentStatus) {
        // فحص الصلاحية
        if (!permissions.canChangeStatus) {
            Swal.fire({
                icon: 'warning',
                title: 'غير مصرح',
                text: 'ليس لديك صلاحية لتغيير حالة الأصناف'
            });
            return;
        }

        const formData = new FormData();
        formData.append('toggle_item_id', itemId);
        formData.append('current_status', currentStatus);

        fetch('toggle_item_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusCell = document.querySelector(`#item-${itemId} .status-cell`);
                const newStatus = data.new_status;
                const newStatusText = newStatus === 'active' ? 'نشط' : 'متوقف';
                const newStatusClass = newStatus === 'active' ? 'confirmed' : 'pending';

                statusCell.innerHTML = `<span class='status-frame ${newStatusClass}'>${newStatusText}</span>`;
                statusCell.classList.toggle('confirmed', newStatus === 'active');
                statusCell.classList.toggle('pending', newStatus !== 'active');
                statusCell.setAttribute('onclick', `event.stopPropagation(); toggleItemStatus(${itemId}, "${newStatus}")`);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء تغيير حالة الصنف.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء تغيير حالة الصنف.'
            });
        });
    }

    // تعريف متغير معرف الفرع المشفر ليكون متاحًا للسايد بار
    var encrypted_store_id = "<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>";
    var encrypted_account_id = "<?php echo isset($_SESSION['account_id']) ? $_SESSION['account_id'] : ''; ?>";
    
    // متغيرات الفلترة
    let currentFilter = 'all';
    let userFavorites = new Set();
    
    // تحميل المفضلة من البيانات الأولية
    document.addEventListener('DOMContentLoaded', function() {
        const rows = document.querySelectorAll('#itemsTable tr');
        rows.forEach(row => {
            const favoriteBtn = row.querySelector('.favorite-btn');
            if (favoriteBtn && favoriteBtn.classList.contains('is-favorite')) {
                const itemId = favoriteBtn.getAttribute('data-item-id');
                if (itemId) {
                    userFavorites.add(parseInt(itemId));
                }
            }
        });
    });

    // وظيفة إدارة المفضلة
    async function toggleFavorite(itemId, button, event) {
        // منع انتشار الحدث
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        }
        
        if (!encrypted_account_id) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يجب تسجي�� الدخول لإضافة الأصناف إلى المفضلة'
            });
            return;
        }

        const isFavorite = button.classList.contains('is-favorite');
        const action = isFavorite ? 'remove' : 'add';
        
        try {
            const response = await fetch('manage_favorites.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    account_id: encrypted_account_id,
                    item_id: itemId,
                    action: action
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (action === 'add') {
                    button.classList.remove('not-favorite');
                    button.classList.add('is-favorite');
                    button.title = 'إزالة من المفضلة';
                    userFavorites.add(parseInt(itemId));
                } else {
                    button.classList.remove('is-favorite');
                    button.classList.add('not-favorite');
                    button.title = 'إضافة إل�� المفضلة';
                    userFavorites.delete(parseInt(itemId));
                }
                
                // إعادة تطبيق الفلتر الحالي
                applyCurrentFilter();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: result.message || 'حدث خطأ في إدارة المفضلة'
                });
            }
            
        } catch (error) {
            console.error('خطأ في الشبكة:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ في الاتصال بالخادم'
            });
        }
    }

    // وظائف الفلترة
    function applyFilter(filterType) {
        currentFilter = filterType;
        
        // تحديث أزرار الفلتر باستخدام الكلاسات
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`[data-filter="${filterType}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        applyCurrentFilter();
    }

    function applyCurrentFilter() {
        const rows = document.querySelectorAll('#itemsTable tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const favoriteBtn = row.querySelector('.favorite-btn');
            const imageBtn = row.querySelector('.action-btn[title*="صور"]');
            const statusCell = row.querySelector('.status-cell');
            
            if (!favoriteBtn) return; // تخطي الصفوف التي لا تحتوي على أزرار
            
            const itemId = parseInt(favoriteBtn.getAttribute('data-item-id'));
            const isFavorite = userFavorites.has(itemId);
            const hasImages = imageBtn && imageBtn.classList.contains('has-images-icon');
            
            // تحديد حالة الصنف (نشط أم متوقف)
            let isActive = true;
            if (statusCell) {
                const statusFrame = statusCell.querySelector('.status-frame');
                if (statusFrame) {
                    isActive = statusFrame.classList.contains('confirmed');
                }
            }
            
            let shouldShow = true;
            
            switch (currentFilter) {
                case 'favorites':
                    shouldShow = isFavorite;
                    break;
                case 'active':
                    shouldShow = isActive;
                    break;
                case 'inactive':
                    shouldShow = !isActive;
                    break;
                case 'with-images':
                    shouldShow = hasImages;
                    break;
                case 'no-images':
                    shouldShow = !hasImages;
                    break;
                case 'all':
                default:
                    shouldShow = true;
                    break;
            }
            
            if (shouldShow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        // إظهار/إخفاء رسالة عدم وجود نتائج
        const noResultsMessage = document.querySelector('.table-responsive');
        if (visibleCount === 0) {
            let message = '';
            switch (currentFilter) {
                case 'favorites':
                    message = 'لا توجد أصناف في المفضلة';
                    break;
                case 'active':
                    message = 'لا توجد أصناف نشطة';
                    break;
                case 'inactive':
                    message = 'لا توجد أصناف متوقفة';
                    break;
                case 'with-images':
                    message = 'لا توجد أصناف مع ص��ر';
                    break;
                case 'no-images':
                    message = 'لا توجد أصناف بدون صور';
                    break;
                default:
                    message = 'لا توجد أصناف';
                    break;
            }
            
            // إضافة رسالة إذا لم تكن موجودة
            let noResultsDiv = document.getElementById('no-results-filter');
            if (!noResultsDiv) {
                noResultsDiv = document.createElement('div');
                noResultsDiv.id = 'no-results-filter';
                noResultsDiv.style.cssText = 'text-align: center; padding: 40px; color: #666; font-style: italic;';
                noResultsMessage.appendChild(noResultsDiv);
            }
            noResultsDiv.innerHTML = `<i class="fas fa-search" style="font-size: 48px; color: #ccc; margin-bottom: 15px; display: block;"></i>${message}`;
            noResultsDiv.style.display = 'block';
        } else {
            const noResultsDiv = document.getElementById('no-results-filter');
            if (noResultsDiv) {
                noResultsDiv.style.display = 'none';
            }
        }
    }

    let currentItemId = '';
    let imagesToDelete = []; // Array to store image IDs marked for deletion
    let currentItemImages = []; // Array to store current item images
    let similarItemsData = []; // Array to store similar items data
    let selectedSimilarItems = []; // Array to store selected similar items

    function viewItemImages(encryptedItemId) {
        currentItemId = encryptedItemId;
        
        // Fetch item details and images
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Set item name
                    document.getElementById('modalItemName').textContent = `صور الصنف: ${data.item.name}`;
                    
                    // Clear previous images
                    const imagesContainer = document.getElementById('modalImagesContainer');
                    imagesContainer.innerHTML = '';
                    
                    if (data.item.images && data.item.images.length > 0) {
                        // Display images
                        data.item.images.forEach(image => {
                            const imageItem = document.createElement('div');
                            imageItem.className = 'modal-image-item';
                            
                            const img = document.createElement('img');
                            img.src = image.img_path;
                            img.alt = 'صورة الصنف';
                            img.onclick = function() {
                                openImagePreview(image.img_path);
                            };
                            
                            imageItem.appendChild(img);
                            imagesContainer.appendChild(imageItem);
                        });
                    } else {
                        // No images message
                        const noImagesDiv = document.createElement('div');
                        noImagesDiv.className = 'no-images-message';
                        noImagesDiv.innerHTML = '<i class="fas fa-image" style="font-size: 48px; color: #ccc;"></i><br>لا توجد صور لهذا الصنف';
                        imagesContainer.appendChild(noImagesDiv);
                    }
                    
                    // Show modal
                    document.getElementById('viewImagesModal').classList.add('active');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'فشل في جلب صور الصنف.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب صور الصنف.'
                });
            });
    }

    function closeViewImagesModal() {
        document.getElementById('viewImagesModal').classList.remove('active');
    }

    function openFullImagePage() {
        window.open('view_item_images.php?item_id=' + encodeURIComponent(currentItemId), '_blank');
    }

    function openImagePreview(imageSrc) {
        document.getElementById('previewImage').src = imageSrc;
        document.getElementById('imagePreviewModal').style.display = 'block';
    }

    function closeImagePreview() {
        document.getElementById('imagePreviewModal').style.display = 'none';
    }

    // Image preview click handler is now consolidated in the main window.onclick handler above

    
    function markImageForDeletion(imgId, imageContainer) {
        // Add image ID to deletion array if not already there
        if (!imagesToDelete.includes(imgId)) {
            imagesToDelete.push(imgId);
        }
        
        // Mark the image container as deleted (visual feedback)
        imageContainer.style.opacity = '0.5';
        imageContainer.style.border = '2px solid #dc3545';
        imageContainer.style.position = 'relative';
        
        // Add "محذوف" overlay
        const deletedOverlay = document.createElement('div');
        deletedOverlay.innerHTML = 'محذوف';
        deletedOverlay.style.position = 'absolute';
        deletedOverlay.style.top = '50%';
        deletedOverlay.style.left = '50%';
        deletedOverlay.style.transform = 'translate(-50%, -50%)';
        deletedOverlay.style.background = 'rgba(220, 53, 69, 0.9)';
        deletedOverlay.style.color = 'white';
        deletedOverlay.style.padding = '5px 10px';
        deletedOverlay.style.borderRadius = '3px';
        deletedOverlay.style.fontSize = '12px';
        deletedOverlay.style.fontWeight = 'bold';
        
        imageContainer.appendChild(deletedOverlay);
        
        // Change delete button to restore button
        const deleteBtn = imageContainer.querySelector('button');
        deleteBtn.innerHTML = '↶';
        deleteBtn.style.background = '#28a745';
        deleteBtn.title = 'استعادة الصورة';
        deleteBtn.onclick = function() {
            restoreImageFromDeletion(imgId, imageContainer);
        };
    }

    function restoreImageFromDeletion(imgId, imageContainer) {
        // Remove image ID from deletion array
        const index = imagesToDelete.indexOf(imgId);
        if (index > -1) {
            imagesToDelete.splice(index, 1);
        }
        
        // Restore visual appearance
        imageContainer.style.opacity = '1';
        imageContainer.style.border = '1px solid #ddd';
        
        // Remove "محذوف" overlay
        const overlay = imageContainer.querySelector('div:last-child');
        if (overlay && overlay.innerHTML === 'محذوف') {
            overlay.remove();
        }
        
        // Change restore button back to delete button
        const restoreBtn = imageContainer.querySelector('button');
        restoreBtn.innerHTML = '×';
        restoreBtn.style.background = '#dc3545';
        restoreBtn.title = 'حذف الصورة';
        restoreBtn.onclick = function() {
            markImageForDeletion(imgId, imageContainer);
        };
    }

    function updateImageIconColor(encryptedItemId, hasImages) {
        // Find the image button for this item
        const buttons = document.querySelectorAll('button[onclick*="' + encryptedItemId + '"]');
        buttons.forEach(button => {
            if (button.innerHTML.includes('fa-images')) {
                if (hasImages) {
                    button.classList.remove('no-images-icon');
                    button.classList.add('has-images-icon');
                    button.title = 'يحتوي على صور';
                } else {
                    button.classList.remove('has-images-icon');
                    button.classList.add('no-images-icon');
                    button.title = 'لا يحتوي على صور';
                }
            }
        });
    }

    // وظائف التعديل في فروع أخرى
    function toggleOtherStoresEdit() {
        const checkbox = document.getElementById('edit_in_other_stores');
        const container = document.getElementById('other_stores_items');
        
        if (checkbox.checked) {
            container.style.display = 'block';
            loadSimilarItems();
        } else {
            container.style.display = 'none';
            similarItemsData = [];
            selectedSimilarItems = [];
        }
    }

    function loadSimilarItems() {
        const similarItemsList = document.getElementById('similar_items_list');
        const currentItemId = document.getElementById('edit_item_id').value;
        
        if (!currentItemId) {
            similarItemsList.innerHTML = '<div class="no-similar-items">لا يوجد صنف محدد</div>';
            return;
        }
        
        similarItemsList.innerHTML = '<div class="loading-similar-items"><i class="fas fa-spinner fa-spin"></i> جاري البحث عن الأصناف المشابهة...</div>';
        
        fetch(`get_similar_items_in_other_stores.php?item_id=${encodeURIComponent(currentItemId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    similarItemsData = data.similar_items;
                    displaySimilarItems(data.similar_items, data.current_barcode);
                } else {
                    similarItemsList.innerHTML = `<div class="no-similar-items">${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error loading similar items:', error);
                similarItemsList.innerHTML = '<div class="no-similar-items">حدث خطأ أثناء البحث عن الأصناف المشابهة</div>';
            });
    }

    function displaySimilarItems(items, barcode) {
        const similarItemsList = document.getElementById('similar_items_list');
        
        if (items.length === 0) {
            similarItemsList.innerHTML = `<div class="no-similar-items">لا توجد أصناف أخرى بنفس الباركود (${barcode}) في فروع أخرى</div>`;
            return;
        }
        
        let html = '';
        items.forEach(item => {
            const quantityUnit = getQuantityUnit(item.type);
            html += `
                <div class="similar-item" data-item-id="${item.encrypted_item_id}">
                    <input type="checkbox" class="similar-item-checkbox" 
                           onchange="toggleSimilarItemSelection('${item.encrypted_item_id}', this)">
                    <div class="similar-item-info">
                        <div class="similar-item-name">${item.name}</div>
                        <div class="similar-item-store">الفرع: ${item.store_name}</div>
                        <div class="similar-item-barcode">الباركود: ${item.barcode || 'غير محدد'}</div>
                        <div class="similar-item-details">
                            <span>التكلفة: ${item.cost}</span>
                            <span>السعر: ${item.price}</span>
                            <span>الكمية: ${item.quantity}${quantityUnit}</span>
                            <span>الحالة: ${item.status === 'active' ? 'نشط' : 'متوقف'}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        
        similarItemsList.innerHTML = html;
    }

    function toggleSimilarItemSelection(encryptedItemId, checkbox) {
        const itemDiv = checkbox.closest('.similar-item');
        
        if (checkbox.checked) {
            selectedSimilarItems.push(encryptedItemId);
            itemDiv.classList.add('selected');
        } else {
            const index = selectedSimilarItems.indexOf(encryptedItemId);
            if (index > -1) {
                selectedSimilarItems.splice(index, 1);
            }
            itemDiv.classList.remove('selected');
        }
    }

    function getQuantityUnit(type) {
        switch(type) {
            case 'box': return ' كرتونة';
            case 'fridge': return ' كيلو';
            case 'piece': return ' قطعة';
            default: return ' قطعة';
        }
    }

    // إضافة event listeners لأزرار الفلترة
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.filter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const filterType = this.getAttribute('data-filter');
                applyFilter(filterType);
            });
        });
    });
</script>

<div class="popup-message" id="popupMessage"></div>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
