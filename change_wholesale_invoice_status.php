<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['encrypted_invoice_id'])) {
    $invoice_id = decrypt($_POST['encrypted_invoice_id'], $key);

    // Fetch invoice details
    $sql_invoice = "SELECT store_id, buyer, total_amount, account_id_buyer, created_at FROM wholesale_invoices WHERE invoice_id = ?";
    $stmt_invoice = $conn->prepare($sql_invoice);
    $stmt_invoice->bind_param("i", $invoice_id);
    $stmt_invoice->execute();
    $invoice = $stmt_invoice->get_result()->fetch_assoc();
    $stmt_invoice->close();

    if (!$invoice) {
        echo json_encode(['success' => false, 'message' => 'فاتورة غير موجودة.']);
        exit();
    }

    $store_id = $invoice['store_id'];
    $buyer_store_id = $invoice['buyer'];
    $total_amount = $invoice['total_amount'];
    $account_id_buyer = $invoice['account_id_buyer'];
    $created_at = $invoice['created_at'];

    // Fetch invoice items with barcode and name
    $sql_items = "SELECT w.item_id, w.quantity, w.total_amount, i.barcode, i.name 
                  FROM whosales w
                  JOIN items i ON w.item_id = i.item_id
                  WHERE w.invoice_id = ?";
    $stmt_items = $conn->prepare($sql_items);
    $stmt_items->bind_param("i", $invoice_id);
    $stmt_items->execute();
    $items = $stmt_items->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt_items->close();

    $conn->begin_transaction();
    try {
        // Insert item transactions for wholesale sale
        $sql_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity, transaction_date) VALUES (?, 'wholesale_sale', ?, ?, ?)";
        $stmt_transaction = $conn->prepare($sql_transaction);

        foreach ($items as $item) {
            $stmt_transaction->bind_param("iiis", $item['item_id'], $invoice_id, $item['quantity'], $created_at);
            $stmt_transaction->execute();

            // Deduct quantity from seller's inventory
            $sql_deduct = "UPDATE items SET quantity = quantity - ? WHERE item_id = ? AND store_id = ?";
            $stmt_deduct = $conn->prepare($sql_deduct);
            $stmt_deduct->bind_param("iii", $item['quantity'], $item['item_id'], $store_id);
            $stmt_deduct->execute();
            $stmt_deduct->close();
        }
        $stmt_transaction->close();

        // Check if the buyer is a store or a person
        if (is_numeric($buyer_store_id)) {
            // Buyer is a store, proceed with inventory updates for the buyer
            $sql_purchase_invoice = "INSERT INTO purchase_invoices (store_id, total_amount, account_id, status, created_at) VALUES (?, ?, ?, 'confirmed', ?)";
            $stmt_purchase_invoice = $conn->prepare($sql_purchase_invoice);
            $stmt_purchase_invoice->bind_param("idis", $buyer_store_id, $total_amount, $account_id_buyer, $created_at);
            $stmt_purchase_invoice->execute();
            $purchase_invoice_id = $stmt_purchase_invoice->insert_id;
            $stmt_purchase_invoice->close();

            // Update wholesale invoice with purchase invoice ID
            $sql_update_wholesale = "UPDATE wholesale_invoices SET purchase_invoice_id = ? WHERE invoice_id = ?";
            $stmt_update_wholesale = $conn->prepare($sql_update_wholesale);
            $stmt_update_wholesale->bind_param("ii", $purchase_invoice_id, $invoice_id);
            $stmt_update_wholesale->execute();
            $stmt_update_wholesale->close();

            // Insert items into purchases and adjust buyer's inventory
            $sql_purchase = "INSERT INTO purchases (store_id, invoice_id, item_id, quantity, time, total_amount) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt_purchase = $conn->prepare($sql_purchase);

            $sql_buyer_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity, transaction_date) VALUES (?, 'wholesale_purchase', ?, ?, ?)";
            $stmt_buyer_transaction = $conn->prepare($sql_buyer_transaction);

            foreach ($items as $item) {
                $stmt_purchase->bind_param("iiisis", $buyer_store_id, $purchase_invoice_id, $item['item_id'], $item['quantity'], $created_at, $item['total_amount']);
                $stmt_purchase->execute();

                // Check if the item exists in the buyer's store with the same barcode, name, and store_id
                $sql_check_item = "SELECT item_id FROM items WHERE barcode = ? AND name = ? AND store_id = ?";
                $stmt_check_item = $conn->prepare($sql_check_item);
                $stmt_check_item->bind_param("ssi", $item['barcode'], $item['name'], $buyer_store_id);
                $stmt_check_item->execute();
                $stmt_check_item->store_result();

                if ($stmt_check_item->num_rows > 0) {
                    $stmt_check_item->bind_result($buyer_item_id);
                    $stmt_check_item->fetch();

                    // Update quantity in buyer's inventory
                    $sql_add_quantity = "UPDATE items SET quantity = quantity + ? WHERE item_id = ?";
                    $stmt_add_quantity = $conn->prepare($sql_add_quantity);
                    $stmt_add_quantity->bind_param("ii", $item['quantity'], $buyer_item_id);
                    $stmt_add_quantity->execute();
                    $stmt_add_quantity->close();

                    // Record the transaction for the buyer's store
                    $stmt_buyer_transaction->bind_param("iiis", $buyer_item_id, $purchase_invoice_id, $item['quantity'], $created_at);
                    $stmt_buyer_transaction->execute();

                    // Update the purchases table with the correct buyer's item_id
                    $sql_update_purchase = "UPDATE purchases SET item_id = ? WHERE invoice_id = ? AND item_id = ?";
                    $stmt_update_purchase = $conn->prepare($sql_update_purchase);
                    $stmt_update_purchase->bind_param("iii", $buyer_item_id, $purchase_invoice_id, $item['item_id']);
                    $stmt_update_purchase->execute();
                    $stmt_update_purchase->close();
                } else {
                    echo json_encode(['success' => false, 'message' => 'الصنف غير موجود في مخزن المشتري. تأكد من تطابق الاسم والباركود.']);
                    $conn->rollback();
                    exit();
                }
                $stmt_check_item->close();
            }
            $stmt_purchase->close();
            $stmt_buyer_transaction->close();
        }

        // Update the wholesale invoice status to "confirmed"
        $sql_update_status = "UPDATE wholesale_invoices SET status = 'confirmed' WHERE invoice_id = ?";
        $stmt_update_status = $conn->prepare($sql_update_status);
        $stmt_update_status->bind_param("i", $invoice_id);
        $stmt_update_status->execute();
        $stmt_update_status->close();

        $conn->commit();
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تغيير حالة الفاتورة.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طلب غير صالح.']);
}
$conn->close();
?>
