<?php
/**
 * ملف جلب خيارات النقل - مدمج مع نظام الصلاحيات
 * يتطلب صلاحية الوصول لوحدة نقل الأصناف
 */

include 'db_connection.php';
require_once 'security.php';

// فحص صلاحية الوصول للوحدة
if (!hasPermission('transfer_items', 'access')) {
    http_response_code(403);
    echo '<div class="alert alert-danger text-center">';
    echo '<i class="fas fa-exclamation-triangle"></i>';
    echo '<strong>غير مسموح!</strong> ليس لديك صلاحية للوصول لهذه البيانات.';
    echo '</div>';
    exit();
}

$source_store_id = $_GET['source_store_id'];
$target_store_id = $_GET['target_store_id'];
$transfer_type = $_GET['transfer_type'];

// فحص صلاحية النقل لعرض رسالة تحذيرية
$hasTransferPermission = hasPermission('transfer_items', 'transfer_permission');

if (!$hasTransferPermission) {
    echo '<div class="alert alert-info text-center mb-3">';
    echo '<i class="fas fa-info-circle"></i>';
    echo '<strong>ملاحظة:</strong> يمكنك عرض البيانات فقط. ليس لديك صلاحية لتنفيذ عمليات النقل.';
    echo '</div>';
}

if ($transfer_type == 'categories') {
    $sql = "SELECT * FROM categories WHERE store_id = ? AND name NOT IN (SELECT name FROM categories WHERE store_id = ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $source_store_id, $target_store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $categories = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();

    echo '<div class="mb-3">';
    echo '<label class="form-label">اختر التصنيفات</label>';
    echo '<div class="table-responsive">';
    echo '<table id="transferTable" class="table table-bordered text-center">';
    echo '<thead class="table-dark">';
    echo '<tr><th>اختر</th><th>اسم التصنيف</th></tr>';
    echo '</thead>';
    echo '<tbody>';
    foreach ($categories as $category) {
        echo '<tr>';
        if ($hasTransferPermission) {
            echo '<td><input class="form-check-input" type="checkbox" name="selected_ids[]" value="' . $category['category_id'] . '"></td>';
        } else {
            echo '<td><input class="form-check-input" type="checkbox" disabled title="ليس لديك صلاحية للنقل"></td>';
        }
        echo '<td>' . $category['name'] . '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
} elseif ($transfer_type == 'items') {
    $sql = "SELECT * FROM items WHERE store_id = ? AND barcode NOT IN (SELECT barcode FROM items WHERE store_id = ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $source_store_id, $target_store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();

    echo '<div class="mb-3">';
    echo '<label class="form-label">اختر الأصناف</label>';
    echo '<div class="table-responsive">';
    echo '<table id="transferTable" class="table table-bordered text-center">';
    echo '<thead class="table-dark">';
    echo '<tr><th>اختر</th><th>اسم الصنف</th><th>الباركود</th></tr>';
    echo '</thead>';
    echo '<tbody>';
    foreach ($items as $item) {
        echo '<tr>';
        if ($hasTransferPermission) {
            echo '<td><input class="form-check-input" type="checkbox" name="selected_ids[]" value="' . $item['item_id'] . '"></td>';
        } else {
            echo '<td><input class="form-check-input" type="checkbox" disabled title="ليس لديك صلاحية للنقل"></td>';
        }
        echo '<td>' . $item['name'] . '</td>';
        echo '<td>' . $item['barcode'] . '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
}

$conn->close();


?>
