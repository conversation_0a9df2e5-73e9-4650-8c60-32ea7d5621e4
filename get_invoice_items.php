<?php
include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['invoice_id'])) {
    $encrypted_invoice_id = $_GET['invoice_id'];
    $invoice_id = decrypt($encrypted_invoice_id, $key);

    $sql = "SELECT i.name, w.quantity, w.total_amount
            FROM whosales w
            JOIN items i ON w.item_id = i.item_id
            WHERE w.invoice_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = $row;
    }
    $stmt->close();

    $sql_images = "SELECT img_path FROM wholesale_invoice_images WHERE wholesale_invoice_id = ?";
    $stmt_images = $conn->prepare($sql_images);
    $stmt_images->bind_param("i", $invoice_id);
    $stmt_images->execute();
    $result_images = $stmt_images->get_result();
    $images = [];
    while ($row = $result_images->fetch_assoc()) {
        $images[] = $row['img_path'];
    }
    $stmt_images->close();

    echo json_encode(['items' => $items, 'images' => $images]);
} else {
    echo json_encode(['error' => 'Invalid request']);
}

$conn->close();
?>
