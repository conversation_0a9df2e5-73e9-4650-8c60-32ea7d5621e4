<?php
/**
 * ملف إرسال الإشعارات - مدمج مع نظام الصلاحيات المتقدم
 * 
 * الصلاحيات المطبقة:
 * - access: الوصول للوحدة (مطلوبة للوصول للصفحة)
 * - send_notifications: إرسال الإشعارات
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول لوحدة إرسال الإشعارات
checkPagePermission('send_notifications', 'access');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';
$account_id = decrypt($encrypted_account_id, $key);

// Get store name
$store_query = "SELECT name FROM stores WHERE store_id = ?";
$store_stmt = $conn->prepare($store_query);
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_result = $store_stmt->get_result();
$store_data = $store_result->fetch_assoc();
$store_name = $store_data ? $store_data['name'] : 'غير معروف';

// Get recent notifications
$notifications_query = "SELECT id, message, created_at FROM notifications WHERE store_id = ? ORDER BY created_at DESC LIMIT 10";
$notifications_stmt = $conn->prepare($notifications_query);
$notifications_stmt->bind_param("i", $store_id);
$notifications_stmt->execute();
$notifications_result = $notifications_stmt->get_result();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال الإشعارات - <?php echo htmlspecialchars($store_name); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link rel="stylesheet" href="web_css/style_web.css">
    <style>
        /* متغيرات الألوان للوضع العادي والمظلم */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            --shadow-dark: rgba(0, 0, 0, 0.25);
            --gradient-primary: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            --gradient-info: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.05) 100%);
            --gradient-warning: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
            --gradient-success: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%);
        }

        [data-theme='dark'] {
            --bg-primary: #1a202c;
            --bg-secondary: #2d3748;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --border-color: #4a5568;
            --shadow-light: rgba(0, 0, 0, 0.3);
            --shadow-medium: rgba(0, 0, 0, 0.4);
            --shadow-dark: rgba(0, 0, 0, 0.6);
            --gradient-primary: linear-gradient(135deg, #58a6ff 0%, #a5a5f5 100%);
            --gradient-info: linear-gradient(135deg, rgba(13, 202, 240, 0.15) 0%, rgba(13, 202, 240, 0.08) 100%);
            --gradient-warning: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.08) 100%);
            --gradient-success: linear-gradient(135deg, rgba(25, 135, 84, 0.15) 0%, rgba(25, 135, 84, 0.08) 100%);
        }

        /* تحسين الحاويات الرئيسية */
        .notification-form,
        .notification-history {
            background: var(--bg-primary);
            border-radius: 16px;
            box-shadow: 0 8px 32px var(--shadow-light);
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .notification-form::before,
        .notification-history::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 16px 16px 0 0;
        }

        .notification-form:hover,
        .notification-history:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px var(--shadow-medium);
        }

        /* تحسين عناصر الإشعارات */
        .notification-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .notification-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .notification-item:hover {
            transform: translateX(8px);
            box-shadow: 0 8px 24px var(--shadow-light);
        }

        .notification-item:hover::before {
            transform: scaleY(1);
        }

        .notification-message {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .notification-time {
            color: var(--text-secondary);
            font-size: 13px;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .notification-time::before {
            content: '🕒';
            font-size: 12px;
        }

        /* تحسين الشارات والعناصر التفاعلية */
        .badge-container {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .badge {
            font-weight: 600;
            letter-spacing: 0.5px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #198754, #20c997) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, #0d6efd, #6610f2) !important;
        }

        /* تحسين التنبيهات */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 20px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }

        .alert-info {
            background: var(--gradient-info);
            border-left: 4px solid #0dcaf0;
            color: var(--text-primary);
        }

        .alert-warning {
            background: var(--gradient-warning);
            border-left: 4px solid #ffc107;
            color: var(--text-primary);
        }

        .alert-heading {
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* تحسين العناوين */
        h1 {
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        h3 {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        h3::before {
            content: '';
            width: 4px;
            height: 24px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        /* تحسين النماذج */
        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control,
        .form-select {
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-control:focus,
        .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
            transform: translateY(-2px);
        }

        .form-check {
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .form-check:hover {
            background: var(--bg-primary);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            margin-top: 0.1em;
        }

        .form-check-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-right: 10px;
        }

        /* تحسين الأزرار */
        .btn {
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            box-shadow: 0 4px 16px rgba(13, 110, 253, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(13, 110, 253, 0.4);
            background: linear-gradient(135deg, #0b5ed7, #5a0fc8);
        }

        /* تحسين معلومات الصلاحيات */
        .permissions-info {
            text-align: center;
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .permissions-info small {
            color: var(--text-secondary);
            font-weight: 500;
            display: block;
            margin-bottom: 8px;
        }

        /* تأثيرات التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-form,
            .notification-history {
                padding: 20px;
                margin-bottom: 20px;
            }

            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }

            .badge-container {
                align-self: stretch;
                justify-content: center;
            }

            .permissions-info {
                text-align: center;
                margin-top: 15px;
            }

            .col-md-4.text-end {
                text-align: center !important;
            }

            h1 {
                font-size: 1.8rem;
                text-align: center;
                justify-content: center;
            }

            .notification-item {
                padding: 15px;
                margin: 10px 0;
            }

            .notification-item:hover {
                transform: translateY(-2px);
            }

            .alert {
                padding: 15px;
            }

            .row {
                margin: 0;
            }

            .col-md-6 {
                padding: 0 10px;
            }
        }

        /* تحسين التمرير */
        .notification-history {
            max-height: 600px;
            overflow-y: auto;
        }

        .notification-history::-webkit-scrollbar {
            width: 8px;
        }

        .notification-history::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .notification-history::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        .notification-history::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* تأثيرات إضافية للتفاعل */
        .notification-form,
        .notification-history,
        .notification-item,
        .badge,
        .btn,
        .form-control,
        .alert {
            will-change: transform;
        }

        /* تحسين الأيقونات */
        .fas,
        .far {
            transition: all 0.3s ease;
        }

        .btn:hover .fas,
        .btn:hover .far {
            transform: scale(1.1);
        }

        /* تأثير النبض للشارات المهمة */
        .badge.bg-success {
            animation: pulse-success 2s infinite;
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 2px 8px rgba(25, 135, 84, 0.3); }
            50% { box-shadow: 0 4px 16px rgba(25, 135, 84, 0.5); }
            100% { box-shadow: 0 2px 8px rgba(25, 135, 84, 0.3); }
        }

        /* تحسين حالة عدم وجود إشعارات */
        .no-notifications {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .no-notifications i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .no-notifications p {
            font-size: 1.1rem;
            margin: 0;
        }

        /* تحسينات إضافية للتفاعل */
        .focused .form-label {
            color: #0d6efd !important;
            transform: scale(1.05);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        /* تأثيرات الكتابة */
        .typing-effect {
            overflow: hidden;
            border-right: 2px solid #0d6efd;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #0d6efd; }
        }

        /* تحسين الشارات المتحركة */
        .badge.animate {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* تأثيرات التمرير السلس */
        html {
            scroll-behavior: smooth;
        }

        /* تحسين الظلال للوضع المظلم */
        [data-theme='dark'] .notification-form,
        [data-theme='dark'] .notification-history {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        [data-theme='dark'] .notification-form:hover,
        [data-theme='dark'] .notification-history:hover {
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.6);
        }

        [data-theme='dark'] .notification-item {
            background: #374151;
            border-color: #4b5563;
        }

        [data-theme='dark'] .notification-item:hover {
            background: #4b5563;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
        }

        /* تحسين النصوص للوضع المظلم */
        [data-theme='dark'] .form-control,
        [data-theme='dark'] .form-select {
            background: #374151;
            border-color: #4b5563;
            color: #e5e7eb;
        }

        [data-theme='dark'] .form-control::placeholder {
            color: #9ca3af;
        }

        [data-theme='dark'] .form-check {
            background: #374151;
            border-color: #4b5563;
        }

        [data-theme='dark'] .permissions-info {
            background: #374151;
            border-color: #4b5563;
        }

        /* تأثيرات الانتقال المحسنة */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }

        /* تحسين الأيقونات المتحركة */
        .icon-spin {
            animation: spin 2s linear infinite;
        }

        .icon-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* تحسين الاستجابة للأجهزة اللوحية */
        @media (max-width: 992px) {
            .notification-form,
            .notification-history {
                padding: 25px;
            }

            h1 {
                font-size: 2rem;
            }

            .badge {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

        /* تحسين الطباعة */
        @media print {
            .notification-form form,
            .btn,
            .badge-container {
                display: none !important;
            }

            .notification-history {
                box-shadow: none;
                border: 1px solid #000;
            }

            .notification-item {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="content">
        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">
                    <i class="fas fa-paper-plane text-primary"></i> إرسال الإشعارات
                </h1>
                <div class="badge-container">
                    <?php if (hasPermission('send_notifications', 'send_notifications')): ?>
                        <span class="badge bg-success fs-6 animate">
                            <i class="fas fa-check-circle icon-pulse"></i> مسموح بالإرسال
                        </span>
                    <?php else: ?>
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-eye"></i> عرض فقط
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- معلومات الفرع و��لصلاحيات -->
            <div class="alert alert-info mb-4" role="alert">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="alert-heading mb-2">
                            <i class="fas fa-store"></i> الفرع الحالي: <?php echo htmlspecialchars($store_name); ?>
                        </h5>
                        <p class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            <?php if (hasPermission('send_notifications', 'send_notifications')): ?>
                                يمكنك إرسال الإشعارات لجميع المستخدمين في هذا الفرع.
                            <?php else: ?>
                                يمكنك عرض الإشعارات السابقة فقط. للحصول على صلاحية الإرسال، تواصل مع المدير.
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="permissions-info">
                            <small class="text-muted d-block">الصلاحيات المتاحة:</small>
                            <div class="mt-1">
                                <span class="badge bg-primary me-1">
                                    <i class="fas fa-eye"></i> عرض
                                </span>
                                <?php if (hasPermission('send_notifications', 'send_notifications')): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-paper-plane"></i> إرسال
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="notification-form">
                        <h3 class="mb-3">إرسال إشعار جديد</h3>

                        <?php if (hasPermission('send_notifications', 'send_notifications')): ?>
                            <form id="notificationForm">
                                <div class="mb-4">
                                    <label for="notificationTitle" class="form-label">
                                        <i class="fas fa-heading text-primary"></i>
                                        عنوان الإشعار
                                    </label>
                                    <input type="text" class="form-control" id="notificationTitle" 
                                           placeholder="أدخل عنوان الإشعار..." required>
                                </div>

                                <div class="mb-4">
                                    <label for="notificationMessage" class="form-label">
                                        <i class="fas fa-comment-alt text-primary"></i>
                                        نص الإشعار
                                    </label>
                                    <textarea class="form-control" id="notificationMessage" rows="4" 
                                              placeholder="اكتب نص الإشعار هنا..." required></textarea>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sendToAll" checked>
                                        <label class="form-check-label" for="sendToAll">
                                            <i class="fas fa-users text-success"></i>
                                            إرسال لجميع المستخدمين في الفرع
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-paper-plane"></i> 
                                    إرسال الإشعار
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>تنبيه:</strong> ليس لديك صلاحية لإرسال الإشعارات. يمكنك فقط عرض الإشعارات السابقة.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="notification-history">
                        <h3 class="mb-3">آخر الإشعارات المرسلة</h3>

                        <div id="notificationsList">
                            <?php if ($notifications_result->num_rows > 0): ?>
                                <?php while ($row = $notifications_result->fetch_assoc()): ?>
                                    <div class="notification-item">
                                        <p class="notification-message"><?php echo htmlspecialchars($row['message']); ?></p>
                                        <small class="notification-time"><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></small>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="no-notifications">
                                    <i class="fas fa-bell-slash"></i>
                                    <p>لا توجد إشعارات سابقة في هذا الفرع</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
    <!-- Firebase Initialization -->
    <script src="/js/firebase-init.js"></script>

    <!-- Notification Sound -->
    <audio id="notificationSound" src="/sounds/notification.mp3" preload="auto"></audio>

    <!-- Firebase Scripts -->
    <?php include_once 'firebase_scripts.php'; ?>

    <script>
        // متغيرات الصلاحيات
        const permissions = {
            access: <?php echo hasPermission('send_notifications', 'access') ? 'true' : 'false'; ?>,
            send_notifications: <?php echo hasPermission('send_notifications', 'send_notifications') ? 'true' : 'false'; ?>
        };

        $(document).ready(function() {
            // فحص الصلاحيات عند تحميل الصفحة
            if (!permissions.access) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية للوصول لهذه الصفحة',
                    confirmButtonText: 'حسناً'
                }).then(() => {
                    window.location.href = 'dashboard.php';
                });
                return;
            }

            // Handle form submission
            $('#notificationForm').on('submit', function(e) {
                e.preventDefault();

                // فحص صلاحية إرسال الإشعارات
                if (!permissions.send_notifications) {
                    Swal.fire({
                        icon: 'error',
                        title: 'غير مسموح',
                        text: 'ليس لديك صلاحية لإرسال الإشعارات',
                        confirmButtonText: 'حسناً'
                    });
                    return;
                }

                const title = $('#notificationTitle').val();
                const message = $('#notificationMessage').val();
                const sendToAll = $('#sendToAll').is(':checked');

                // التحقق من صحة البيانات
                if (!title.trim() || !message.trim()) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'بيانات ناقصة',
                        text: 'يرجى ملء جميع الحقول المطلوبة',
                        confirmButtonText: 'حسناً'
                    });
                    return;
                }

                // Show loading with custom animation
                Swal.fire({
                    title: 'جاري إرسال الإشعار...',
                    html: `
                        <div class="d-flex justify-content-center align-items-center" style="height: 80px;">
                            <div class="loading-spinner"></div>
                        </div>
                        <p class="mt-3 text-muted">يرجى الانتظار...</p>
                    `,
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    background: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                });

                // Send notification
                $.ajax({
                    url: 'process_notification.php',
                    type: 'POST',
                    data: {
                        title: title,
                        message: message,
                        sendToAll: sendToAll
                    },
                    dataType: 'json',
                    success: function(response) {
                        Swal.close();

                        if (response.success) {
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'تم إرسال الإشعار بنجاح',
                                text: response.message,
                                confirmButtonText: 'حسناً',
                                timer: 3000,
                                timerProgressBar: true
                            });

                            // Clear form
                            $('#notificationTitle').val('');
                            $('#notificationMessage').val('');

                            // Refresh notifications list
                            refreshNotificationsList();

                            // Update unread count for all users
                            if (typeof updateUnreadCount === 'function') {
                                setTimeout(() => {
                                    updateUnreadCount();
                                }, 1000);
                                setTimeout(() => {
                                    updateUnreadCount();
                                }, 3000);
                            }

                            // Force update if available
                            if (typeof forceUpdateUnreadCount === 'function') {
                                setTimeout(() => {
                                    forceUpdateUnreadCount();
                                }, 2000);
                            }
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ في الإرسال',
                                text: response.message || 'حدث خطأ أثناء إرسال الإشعار',
                                confirmButtonText: 'حسناً'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        Swal.close();

                        let errorMessage = 'حدث خطأ أثناء إرسال الإشعار. يرجى المحاولة مرة أخرى.';
                        
                        if (xhr.status === 403) {
                            errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
                        } else if (xhr.status === 401) {
                            errorMessage = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى';
                        }

                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ في الاتصال',
                            text: errorMessage,
                            confirmButtonText: 'حسناً'
                        });
                    }
                });
            });

            // Function to refresh notifications list
            function refreshNotificationsList() {
                $.ajax({
                    url: 'get_recent_notifications.php',
                    type: 'GET',
                    dataType: 'html',
                    success: function(response) {
                        $('#notificationsList').html(response);
                    },
                    error: function() {
                        console.log('فشل في تحديث قائمة الإشعارات');
                    }
                });
            }

            // إضافة معلومات الصلاحيات للمطورين في وضع التطوير
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('صلاحيات وحدة إرسال الإشعارات:', permissions);
            }

            // تحسين تفاعل النموذج
            $('#notificationTitle, #notificationMessage').on('input', function() {
                const title = $('#notificationTitle').val().trim();
                const message = $('#notificationMessage').val().trim();
                const submitBtn = $('#notificationForm button[type="submit"]');
                
                if (title && message) {
                    submitBtn.removeClass('btn-secondary').addClass('btn-primary');
                    submitBtn.prop('disabled', false);
                } else {
                    submitBtn.removeClass('btn-primary').addClass('btn-secondary');
                    submitBtn.prop('disabled', true);
                }
            });

            // تأثير بصري عند التركيز على الحقول
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });

            // تحسين عرض الوقت
            $('.notification-time').each(function() {
                const timeText = $(this).text();
                const timeAgo = getTimeAgo(timeText);
                $(this).attr('title', timeText).text(timeAgo);
            });

            // إضافة تأثير الكتابة للعناوين
            animateTitle();
        });

        // دالة لحساب الوقت المنقضي
        function getTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) return 'منذ لحظات';
            if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
            if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
            if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
            return dateString;
        }

        // دالة لتحريك العنوان
        function animateTitle() {
            const title = document.querySelector('h1');
            if (title) {
                title.style.opacity = '0';
                title.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    title.style.transition = 'all 0.6s ease';
                    title.style.opacity = '1';
                    title.style.transform = 'translateY(0)';
                }, 100);
            }
        }
    </script>
</body>
</html>

<?php
/**
 * ملخص الصلاحيات المطبقة في وحدة إرسال الإشعارات:
 * 
 * 1. صلاحية الوصول (access):
 *    - مطلوبة للوصول للصفحة
 *    - يتم فحصها في بداية الملف باستخدام checkPagePermission()
 *    - في حالة عدم وجودها يتم التوجيه لصفحة غير مصرح
 * 
 * 2. صلاحية إرسال الإشعارات (send_notifications):
 *    - مطلوبة لإرسال الإشعارات
 *    - يتم فحصها في PHP لإظهار/إخفاء نموذج الإرسال
 *    - يتم فحصها في JavaScript قبل إرسال الطلب
 *    - في حالة عدم وجودها يظهر تنبيه للمستخدم
 * 
 * 3. آلية الحماية:
 *    - فحص في PHP (server-side) لضمان الأمان
 *    - فحص في JavaScript (client-side) لتحسين تجربة المستخدم
 *    - رسائل واضحة للمستخدم عند عدم وجود الصلاحيات
 *    - تسجيل الصلاحيات في console للمطورين
 * 
 * 4. التكامل مع نظام الصلاحيات:
 *    - استخدام hasPermission() للفحص
 *    - التكامل مع permissions_system.php
 *    - دعم الصلاحيات الخاصة والعامة
 *    - دعم انتهاء صلاحية الصلاحيات المؤقتة
 */
?>
