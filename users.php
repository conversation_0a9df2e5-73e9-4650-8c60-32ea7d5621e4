<?php
// تحديد الصلاحيات المطلوبة لهذه الصفحة
require_once 'security.php';

// التحقق من صلاحية الوصول للصفحة الرئيسية لنظام الكاشير
checkPagePermission('cashier_home', 'access');

include 'check_expiry_notifications.php';

// فحص الصلاحيات الفرعية
$canViewImages = hasPermission('cashier_home', 'view_images');
$canViewPrices = hasPermission('cashier_home', 'view_prices');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['account_id'])) {
    $encrypted_account_id = $_GET['account_id'];
    $account_id = decrypt($encrypted_account_id, $key);

    if ($account_id === false) {
        die("Failed to decrypt Account ID.");
    }

    // Fetch the store_id using the account_id
    $query = "SELECT store_id FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $account = $result->fetch_assoc();
    $store_id = $account['store_id'] ?? null;

    if (!$store_id) {
        die("Store ID not found for the given Account ID.");
    }

    // Encrypt the store_id for session storage
    $encrypted_store_id = encrypt($store_id, $key);

    // Store the encrypted store ID and account ID in the session
    $_SESSION['store_id'] = $encrypted_store_id;
    $_SESSION['account_id'] = $encrypted_account_id;
    setcookie('account_id', $encrypted_account_id, time() + (86400 * 30), "/");

    // Fetch items related to the store with active status and image count
    $query = "SELECT items.item_id, items.name, items.price,
              (SELECT COUNT(*) FROM item_images WHERE item_id = items.item_id) as image_count,
              (SELECT COUNT(*) FROM user_favorites WHERE item_id = items.item_id AND account_id = ?) as is_favorite
              FROM items 
              JOIN categories ON items.category_id = categories.category_id 
              WHERE categories.store_id = ? AND items.status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $account_id, $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Store the initial items in a variable
    $initialItems = [];
    while ($row = $result->fetch_assoc()) {
        $initialItems[] = $row;
    }

    // Fetch the store name using the store ID
    $query = "SELECT name FROM stores WHERE store_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $store = $result->fetch_assoc();
    $storeName = $store['name'] ?? 'اسم الفرع غير متوفر';

    // Fetch the user's theme preference
    $query = "SELECT theme FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $userTheme = $user['theme'] ?? 'Light';

} else {
    die("Account ID not provided.");
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <title>ELWALED MARKET</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --primary-light: #4dabf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --light-bg: #f0f2f5;
            
            /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
            --dark-bg: #0f1419;
            --dark-surface: #1a2332;
            --dark-surface-light: #242b3d;
            --dark-surface-hover: #2a3441;
            --dark-text: #e1e8f0;
            --dark-text-secondary: #b8c5d1;
            --dark-text-muted: #8a9ba8;
            --border-color: #dee2e6;
            --dark-border: #2d3748;
            --dark-border-light: #3a4553;
            
            /* ألوان زرقاء ناعمة ومريحة */
            --blue-gradient-start: #1e3a8a;
            --blue-gradient-end: #3b82f6;
            --blue-accent: #5b9bd5;
            --blue-hover: #4a90c2;
            --blue-soft: #6ba3d6;
            --blue-muted: #4a7ba7;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: var(--light-bg);
            color: #333;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            direction: rtl;
            text-align: right;
        }

        .dark-mode {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 40%, var(--dark-surface-light) 100%);
            color: var(--dark-text);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dark-mode .header {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 2px 20px rgba(91, 155, 213, 0.25);
            border-bottom: 1px solid var(--dark-border-light);
        }

        .store-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        /* Theme Toggle Button */
        .btn-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            width: 50px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .btn-toggle .light-icon,
        .btn-toggle .dark-icon {
            position: absolute;
            color: var(--white);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-toggle .light-icon {
            opacity: 1;
            transform: scale(1);
        }

        .btn-toggle .dark-icon {
            opacity: 0;
            transform: scale(0.8);
        }

        .dark-mode .btn-toggle .light-icon {
            opacity: 0;
            transform: scale(0.8);
        }

        .dark-mode .btn-toggle .dark-icon {
            opacity: 1;
            transform: scale(1);
        }

        /* Search Bar */
        .search-container {
            background-color: var(--light-bg);
            padding: 20px;
            position: sticky;
            top: 70px;
            z-index: 999;
            border-bottom: 1px solid var(--border-color);
        }

        .dark-mode .search-container {
            background: linear-gradient(180deg, var(--dark-surface) 0%, var(--dark-bg) 100%);
            border-bottom: 1px solid var(--dark-border-light);
        }

        .search-wrapper {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 16px;
            background-color: var(--white);
            color: #333;
            transition: all 0.3s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .search-input::placeholder {
            color: #999;
            transition: all 0.3s ease;
        }

        .search-input:focus::placeholder {
            opacity: 0.7;
            transform: translateX(5px);
        }

        .dark-mode .search-input {
            background-color: var(--dark-surface-light);
            color: var(--dark-text);
            border-color: var(--dark-border-light);
        }

        .dark-mode .search-input:focus {
            border-color: var(--blue-soft);
            box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2);
            background-color: var(--dark-surface-hover);
        }

        .dark-mode .search-input::placeholder {
            color: var(--dark-text-muted);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
            font-size: 18px;
            pointer-events: none;
        }

        .clear-search-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clear-search-btn:hover {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            transform: translateY(-50%) scale(1.1);
        }

        .dark-mode .search-icon {
            color: var(--dark-text-muted);
        }

        .dark-mode .clear-search-btn {
            color: var(--dark-text-muted);
        }

        .dark-mode .clear-search-btn:hover {
            background-color: rgba(220, 53, 69, 0.15);
            color: #ff8a80;
        }

        /* Main Content */
        .main-content {
            padding: 30px 20px 100px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-title {
            text-align: center;
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dark-mode .page-title {
            color: var(--blue-soft);
        }

        .store-info {
            text-align: center;
            color: var(--secondary-color);
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .dark-mode .store-info {
            color: var(--dark-text-secondary);
        }

        /* Table Styles */
        .table-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            margin-top: 20px;
            border: 1px solid rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
        }

        .table-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .dark-mode .table-container {
            background: var(--dark-surface-light);
            box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4);
            border: 1px solid var(--dark-border-light);
        }

        .dark-mode .table-container:hover {
            box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5);
            border-color: var(--blue-soft);
        }

        .custom-table {
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }

        .custom-table thead {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        .dark-mode .custom-table thead {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
        }

        .custom-table th {
            padding: 20px;
            color: var(--white);
            font-weight: 600;
            font-size: 1.1rem;
            text-align: right;
            border: none;
        }

        .custom-table td {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            text-align: right;
            transition: background-color 0.3s ease;
        }

        .dark-mode .custom-table td {
            border-bottom-color: var(--dark-border-light);
            color: var(--dark-text);
        }

        .custom-table tbody tr {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .custom-table tbody tr:hover {
            background: linear-gradient(90deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.04) 100%);
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .dark-mode .custom-table tbody tr:hover {
            background: linear-gradient(90deg, rgba(91, 155, 213, 0.12) 0%, rgba(74, 144, 194, 0.06) 100%);
            box-shadow: 0 2px 8px rgba(91, 155, 213, 0.15);
        }

        .custom-table tbody tr:nth-child(even) {
            background-color: rgba(0, 123, 255, 0.02);
        }

        .dark-mode .custom-table tbody tr:nth-child(even) {
            background-color: rgba(45, 55, 72, 0.3);
        }

        .item-name {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-align: right;
            direction: rtl;
        }

        .dark-mode .item-name {
            color: var(--dark-text);
        }

        .item-price {
            font-weight: 700;
            color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-align: right;
            direction: rtl;
        }

        .dark-mode .item-price {
            color: #4ade80;
        }

        .item-icon {
            margin-right: 10px;
            margin-left: 0;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .custom-table tbody tr:hover .item-icon {
            transform: scale(1.1);
            color: var(--blue-hover);
        }

        .dark-mode .custom-table tbody tr:hover .item-icon {
            color: var(--blue-soft);
        }

        /* تفاصيل الصنف في الثيم الداكن */
        .dark-mode .item-details {
            color: var(--dark-text-muted) !important;
        }

        .item-details {
            color: #6c757d;
        }

        /* No Results */
        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: var(--secondary-color);
        }

        .dark-mode .no-results {
            color: var(--dark-text-secondary);
        }

        .no-results-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .no-results-text {
            font-size: 1.2rem;
            font-weight: 600;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-icon {
            font-size: 4rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .dark-mode .empty-icon {
            color: var(--dark-text-muted);
        }

        .empty-text {
            font-size: 1.3rem;
            color: var(--secondary-color);
            font-weight: 600;
        }

        .dark-mode .empty-text {
            color: var(--dark-text-secondary);
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .scroll-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .dark-mode .scroll-to-top {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .scroll-to-top:hover {
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
            background: linear-gradient(135deg, var(--blue-hover) 0%, var(--blue-soft) 100%);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 10px 15px;
            }

            .store-name {
                font-size: 1.2rem;
            }

            .main-content {
                padding: 20px 15px 100px;
            }

            .page-title {
                font-size: 2rem;
            }

            .store-info {
                font-size: 1rem;
            }

            .custom-table th,
            .custom-table td {
                padding: 12px 15px;
            }

            .custom-table th {
                font-size: 1rem;
            }

            .search-container {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .custom-table th,
            .custom-table td {
                padding: 6px 4px;
                font-size: 0.8rem;
            }
            .item-name,
            .item-price {
                font-size: 0.8rem;
            }
            .page-title {
                font-size: 1.8rem;
            }

            .custom-table th,
            .custom-table td {
                padding: 10px 12px;
            }

            .item-name,
            .item-price {
                font-size: 0.9rem;
            }
        }

        /* View Images Button Styles */
        .view-images-btn {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
            border: none;
            color: var(--white);
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }

        .view-images-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        }

        .dark-mode .view-images-btn {
            background: linear-gradient(135deg, var(--blue-soft) 0%, var(--blue-accent) 100%);
            box-shadow: 0 2px 8px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .view-images-btn:hover {
            background: linear-gradient(135deg, var(--blue-accent) 0%, var(--blue-muted) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4);
        }

        /* Disabled eye button styles */
        .btn-secondary:disabled {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #5a6268 100%);
            border: none;
            color: var(--white);
            opacity: 0.6;
            cursor: not-allowed;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto;
        }

        .dark-mode .btn-secondary:disabled {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            opacity: 0.5;
        }

        /* Voice Search Button */
        .voice-search-btn {
            position: absolute;
            left: 50px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .voice-search-btn:hover {
            background: rgba(0, 123, 255, 0.1);
            color: var(--primary-color);
        }

        .dark-mode .voice-search-btn:hover {
            background: rgba(91, 155, 213, 0.1);
            color: var(--blue-soft);
        }

        /* Image Modal Styles */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(5px);
        }

        .image-modal-content {
            position: relative;
            background: var(--white);
            margin: 2% auto;
            padding: 0;
            width: 90%;
            max-width: 900px;
            max-height: 90%;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            animation: modalSlideIn 0.4s ease-out;
        }

        .dark-mode .image-modal-content {
            background: var(--dark-surface-light);
            box-shadow: 0 20px 60px rgba(26, 35, 50, 0.5);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .image-modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dark-mode .image-modal-header {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
        }

        .image-modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            font-family: 'Cairo', Arial, sans-serif;
        }

        .image-modal-close {
            background: none;
            border: none;
            color: var(--white);
            font-size: 2rem;
            cursor: pointer;
            padding: 0;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .image-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .image-modal-body {
            padding: 30px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .images-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .image-item {
            text-align: center;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            padding: 15px;
            background: var(--light-bg);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .image-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .dark-mode .image-item {
            background: var(--dark-surface);
            border-color: var(--dark-border-light);
            box-shadow: 0 4px 15px rgba(26, 35, 50, 0.3);
        }

        .dark-mode .image-item:hover {
            border-color: var(--blue-soft);
            box-shadow: 0 8px 25px rgba(26, 35, 50, 0.4);
        }

        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-item img:hover {
            transform: scale(1.05);
        }

        .no-images {
            text-align: center;
            padding: 60px 20px;
            color: var(--secondary-color);
        }

        .dark-mode .no-images {
            color: var(--dark-text-muted);
        }

        .no-images i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .no-images p {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        /* Full size image modal */
        .fullsize-modal {
            display: none;
            position: fixed;
            z-index: 10001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
        }

        .fullsize-modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 10px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .fullsize-close {
            position: absolute;
            top: 20px;
            right: 35px;
            color: var(--white);
            font-size: 3rem;
            font-weight: bold;
            cursor: pointer;
            z-index: 10002;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .fullsize-close:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Search Suggestions Styles */
        .search-suggestions::-webkit-scrollbar {
            width: 6px;
        }

        .search-suggestions::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .search-suggestions::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .dark-mode .search-suggestions::-webkit-scrollbar-thumb {
            background: var(--blue-soft);
        }

        /* Suggestion Tags */
        .suggestion-tag:hover {
            background: var(--primary-dark) !important;
            transform: scale(1.05) !important;
            transition: all 0.2s ease !important;
        }

        .dark-mode .suggestion-tag {
            background: var(--blue-muted) !important;
        }

        .dark-mode .suggestion-tag:hover {
            background: var(--blue-accent) !important;
        }

        /* High Score Results */
        .high-score-result {
            background: linear-gradient(90deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%) !important;
            border-left: 4px solid var(--primary-color) !important;
        }

        .dark-mode .high-score-result {
            background: linear-gradient(90deg, rgba(91, 155, 213, 0.08) 0%, rgba(91, 155, 213, 0.03) 100%) !important;
            border-left-color: var(--blue-soft) !important;
        }

        /* Favorite Button Styles */
        .favorite-btn {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto;
            border: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .favorite-btn.not-favorite {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--secondary-color);
        }

        .favorite-btn.not-favorite:hover {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: var(--white);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
        }

        .favorite-btn.is-favorite {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: var(--white);
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        .favorite-btn.is-favorite:hover {
            background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.4);
        }

        .dark-mode .favorite-btn.not-favorite {
            background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-surface-light) 100%);
            color: var(--dark-text-muted);
        }

        .dark-mode .favorite-btn.not-favorite:hover {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: var(--white);
        }

        /* Filter Container */
        .filter-container {
            background: var(--white);
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .dark-mode .filter-container {
            background: var(--dark-surface-light);
            box-shadow: 0 4px 15px rgba(26, 35, 50, 0.3);
        }

        .filter-btn {
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            background: var(--white);
            color: var(--secondary-color);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            min-width: 45px;
            min-height: 45px;
        }

        .filter-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .dark-mode .filter-btn {
            background: var(--dark-surface);
            border-color: var(--dark-border-light);
            color: var(--dark-text-muted);
        }

        .dark-mode .filter-btn:hover {
            border-color: var(--blue-soft);
            color: var(--blue-soft);
        }

        .dark-mode .filter-btn.active {
            background: var(--blue-soft);
            border-color: var(--blue-soft);
            color: var(--white);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .custom-table th:nth-child(2) {
                width: 60px;
                padding: 10px 5px;
            }

            .view-images-btn,
            .btn-secondary:disabled {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }

            .voice-search-btn {
                left: 45px;
                font-size: 14px;
            }

            .image-modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .images-gallery {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }

            .image-item {
                padding: 10px;
            }

            .image-item img {
                height: 150px;
            }

            .search-suggestions {
                max-height: 150px !important;
                font-size: 0.9rem !important;
            }
            
            .suggestion-item {
                padding: 10px 12px !important;
            }
        }
            /* Fit the table within small screens without horizontal scrolling */
        @media (max-width: 768px) {
            .table-container {
                overflow-x: visible; /* لا تمرير أفقي */
            }
            /* Prevent columns from squishing too much */
            .custom-table {
                width: 100%;
                table-layout: fixed; /* يجبر الأعمدة على الانكماش */
            }
            .custom-table th,
            .custom-table td {
                padding: 8px 6px;
                font-size: 0.9rem;
                white-space: normal; /* السماح بلف النص */
            }
            .item-name,
            .item-price {
                font-size: 0.9rem;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <button id="theme-toggle" class="btn-toggle">
            <i class="fas fa-moon light-icon"></i>
            <i class="fas fa-sun dark-icon"></i>
        </button>
        <h1 class="store-name">ELWALED MARKET</h1>
    </header>

    <!-- Search Bar -->
    <div class="search-container">
        <div class="search-wrapper">
            <input type="text" id="search-input" class="search-input" placeholder="ابحث عن منتج... (اسم المنتج، الباركود، أو التصنيف)">
            <i class="fas fa-search search-icon"></i>
            <button type="button" id="clear-search" class="clear-search-btn" style="display: none;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <h2 class="page-title">قائمة المنتجات</h2>
        <p class="store-info">الفرع: <?= htmlspecialchars($storeName) ?></p>

        <!-- Filter Container -->
        <div class="filter-container">
            <div style="display: flex; align-items: center; gap: 10px; font-weight: 600; color: var(--primary-color);">
                <i class="fas fa-filter"></i>
                <span>فلترة:</span>
            </div>
            <button class="filter-btn active" data-filter="all" title="جميع المنتجات">
                <i class="fas fa-list"></i>
            </button>
            <button class="filter-btn" data-filter="favorites" title="المفضلة فقط">
                <i class="fas fa-star"></i>
            </button>
            <button class="filter-btn" data-filter="with-images" title="مع صور">
                <i class="fas fa-images"></i>
            </button>
            <button class="filter-btn" data-filter="no-images" title="بدون صور">
                <i class="fas fa-image"></i>
            </button>
        </div>

        <!-- Products Table -->
        <div class="table-container">
            <table class="custom-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-box item-icon"></i>اسم المنتج</th>
                        <th style="text-align: center; width: 80px;">
                            <i class="fas fa-star" style="margin-bottom: 5px; display: block;"></i>
                            المفضلة
                        </th>
                        <?php if ($canViewImages): ?>
                        <th style="text-align: center; width: 80px;">
                            <i class="fas fa-eye" style="margin-bottom: 5px; display: block;"></i>
                            الصور
                        </th>
                        <?php endif; ?>
                        <?php if ($canViewPrices): ?>
                        <th><i class="fas fa-tag item-icon"></i>السعر</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody id="items-table-body">
                    <?php if (!empty($initialItems)): ?>
                        <?php foreach ($initialItems as $item): ?>
                            <tr data-item-id="<?= $item['item_id'] ?>" data-item-name="<?= htmlspecialchars($item['name']) ?>" data-image-count="<?= $item['image_count'] ?>" data-is-favorite="<?= $item['is_favorite'] ?>">
                                <td>
                                    <div class="item-name">
                                        <i class="fas fa-box-open item-icon text-primary"></i>
                                        <?= htmlspecialchars($item['name']) ?>
                                    </div>
                                </td>
                                <td style="text-align: center;">
                                    <button class="favorite-btn <?= $item['is_favorite'] > 0 ? 'is-favorite' : 'not-favorite' ?>" 
                                            data-item-id="<?= $item['item_id'] ?>" 
                                            title="<?= $item['is_favorite'] > 0 ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة' ?>">
                                        <i class="fas fa-star"></i>
                                    </button>
                                </td>
                                <?php if ($canViewImages): ?>
                                <td style="text-align: center;">
                                    <?php if ($item['image_count'] > 0): ?>
                                        <button class="btn btn-info btn-sm view-images-btn" data-item-id="<?= $item['item_id'] ?>" title="عرض صور الصنف">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-secondary btn-sm" disabled title="لا توجد صور لهذا الصنف">
                                            <i class="fas fa-eye-slash"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                                <?php endif; ?>
                                <?php if ($canViewPrices): ?>
                                <td>
                                    <div class="item-price">
                                        <i class="fas fa-coins item-icon"></i>
                                        <?= number_format($item['price'], 2) ?> جنيه
                                    </div>
                                </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?= 2 + ($canViewImages ? 1 : 0) + ($canViewPrices ? 1 : 0) ?>">
                                <div class="empty-state">
                                    <i class="fas fa-inbox empty-icon"></i>
                                    <div class="empty-text">لا توجد منتجات متاحة</div>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <!-- No Results Message -->
            <div id="no-results" class="no-results" style="display: none;">
                <i class="fas fa-search no-results-icon"></i>
                <div class="no-results-text">لا توجد نتائج مطابقة للبحث</div>
            </div>
        </div>
    </div>

    <!-- Scroll to Top Button -->
    <button id="scroll-to-top" class="scroll-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <div class="image-modal-header">
                <h3 class="image-modal-title" id="modalItemName">صور الصنف</h3>
                <button class="image-modal-close" onclick="closeImageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="image-modal-body" id="modalImageContent">
                <!-- Images will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Full Size Image Modal -->
    <div id="fullsizeModal" class="fullsize-modal">
        <span class="fullsize-close" onclick="closeFullsizeModal()">
            <i class="fas fa-times"></i>
        </span>
        <img class="fullsize-modal-content" id="fullsizeImage">
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Initialize theme
        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;
        const userTheme = '<?= $userTheme ?>';

        // Set initial theme
        if (userTheme === 'Dark') {
            body.classList.add('dark-mode');
        }

        // Theme toggle functionality
        themeToggle.addEventListener('click', () => {
            const isDarkMode = body.classList.toggle('dark-mode');
            
            // Update theme in database
            fetch('update_theme.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    account_id: <?= json_encode($account_id) ?>,
                    theme: isDarkMode ? 'Dark' : 'Light'
                })
            }).catch(error => {
                console.error('Error updating theme:', error);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('search-input');
        const clearSearchBtn = document.getElementById('clear-search');
        const itemsTableBody = document.getElementById('items-table-body');
        const noResults = document.getElementById('no-results');
        const initialItems = <?php echo json_encode($initialItems); ?>;
        const storeId = <?php echo json_encode($encrypted_store_id); ?>;
        const encryptedAccountId = <?php echo json_encode($encrypted_account_id); ?>;
        const canViewImages = <?php echo json_encode($canViewImages); ?>;
        const canViewPrices = <?php echo json_encode($canViewPrices); ?>;

        let searchTimeout;
        let searchCache = new Map();
        let currentSearchRequest = null;
        let currentFilter = 'all';
        let userFavorites = new Set();

        // وظيفة تنظيف النص للبحث الأفضل والأكثر شمولية
        function normalizeText(text) {
            return text.toLowerCase()
                // تطبيع الأحرف العربية
                .replace(/[أإآ]/g, 'ا')
                .replace(/[ة]/g, 'ه')
                .replace(/[ى]/g, 'ي')
                .replace(/[ء]/g, '')
                // إزالة التشكيل
                .replace(/[\u064B-\u0652]/g, '')
                // تطبيع الأرقام العربية والإنجليزية
                .replace(/[٠-٩]/g, (match) => String.fromCharCode(match.charCodeAt(0) - '٠'.charCodeAt(0) + '0'.charCodeAt(0)))
                // إزالة علامات الترقيم والرموز
                .replace(/[^\w\s\u0600-\u06FF]/g, ' ')
                // إزالة المسافات الزائدة
                .replace(/\s+/g, ' ')
                .trim();
        }

        // وظيفة البحث الذكي مع نظام النقاط محسن
        function calculateSearchScore(itemName, searchQuery) {
            const normalizedItem = normalizeText(itemName);
            const normalizedQuery = normalizeText(searchQuery);
            
            if (normalizedQuery === '') return 0;
            
            let score = 0;
            const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0);
            const itemWords = normalizedItem.split(' ').filter(word => word.length > 0);
            
            // مطابقة تامة للنص كاملاً (أعلى أولوية)
            if (normalizedItem === normalizedQuery) {
                return 1000;
            }
            
            // مطابقة في بداية النص (أولوية عالية جداً)
            if (normalizedItem.startsWith(normalizedQuery)) {
                return 900;
            }
            
            // مطابقة جزئية للنص كاملاً
            if (normalizedItem.includes(normalizedQuery)) {
                score += 500;
            }
            
            // حساب نقاط الكلمات
            let wordMatches = 0;
            let exactWordMatches = 0;
            
            queryWords.forEach(queryWord => {
                let bestWordScore = 0;
                let wordFound = false;
                
                itemWords.forEach((itemWord, index) => {
                    let wordScore = 0;
                    
                    // مطابقة تامة للكلمة (أعلى نقاط)
                    if (itemWord === queryWord) {
                        wordScore = 200;
                        exactWordMatches++;
                        wordFound = true;
                    }
                    // مطابقة في بداية الكلمة
                    else if (itemWord.startsWith(queryWord)) {
                        wordScore = 150;
                        wordFound = true;
                    }
                    // مطابقة في نهاية الكلمة
                    else if (itemWord.endsWith(queryWord)) {
                        wordScore = 100;
                        wordFound = true;
                    }
                    // مطابقة جزئية في وسط الكلمة
                    else if (itemWord.includes(queryWord)) {
                        wordScore = 80;
                        wordFound = true;
                    }
                    
                    // نقاط إضافية للكلمة الأولى
                    if (index === 0 && wordScore > 0) {
                        wordScore += 50;
                    }
                    
                    // نقاط إضافية للكلمة الثانية
                    if (index === 1 && wordScore > 0) {
                        wordScore += 25;
                    }
                    
                    bestWordScore = Math.max(bestWordScore, wordScore);
                });
                
                if (wordFound) {
                    wordMatches++;
                    score += bestWordScore;
                }
            });
            
            // مكافأة للمطابقات المتعددة
            if (wordMatches > 1) {
                score += wordMatches * 30;
            }
            
            // مكافأة إضافية للمطابقات التامة
            if (exactWordMatches > 0) {
                score += exactWordMatches * 100;
            }
            
            // مكافأة للنصوص القصيرة (أكثر دقة)
            if (itemWords.length <= 3 && wordMatches > 0) {
                score += 50;
            }
            
            // مكافأة إذا كانت نسبة المطابقة عالية
            const matchRatio = wordMatches / queryWords.length;
            if (matchRatio >= 0.8) {
                score += 100;
            } else if (matchRatio >= 0.5) {
                score += 50;
            }
            
            return score;
        }

        // وظيفة اقتراح كلمات مشابهة
        function getSuggestions(query, allItems) {
            const suggestions = [];
            const normalizedQuery = normalizeText(query);
            
            allItems.forEach(item => {
                const itemName = item.name;
                if (itemName) {
                    const words = normalizeText(itemName).split(' ');
                    words.forEach(word => {
                        if (word.length > 2 && word.includes(normalizedQuery.substring(0, 2))) {
                            if (!suggestions.includes(word) && suggestions.length < 5) {
                                suggestions.push(word);
                            }
                        }
                    });
                }
            });
            
            return suggestions;
        }

        let searchHistory = JSON.parse(localStorage.getItem('usersSearchHistory') || '[]');

        // تحميل المفضلة من البيانات الأولية
        initialItems.forEach(item => {
            if (item.is_favorite > 0) {
                userFavorites.add(item.item_id);
            }
        });

        // إضافة إلى تاريخ البحث
        function addToSearchHistory(query) {
            if (query.trim() && !searchHistory.includes(query)) {
                searchHistory.unshift(query);
                searchHistory = searchHistory.slice(0, 10); // الاحتفاظ بآخر 10 عمليات بحث
                localStorage.setItem('usersSearchHistory', JSON.stringify(searchHistory));
            }
        }

        // وظيفة إدارة المفضلة
        async function toggleFavorite(itemId, button) {
            const isFavorite = userFavorites.has(parseInt(itemId));
            const action = isFavorite ? 'remove' : 'add';
            
            try {
                const response = await fetch('manage_favorites.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        account_id: encryptedAccountId,
                        item_id: itemId,
                        action: action
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    if (action === 'add') {
                        userFavorites.add(parseInt(itemId));
                        button.classList.remove('not-favorite');
                        button.classList.add('is-favorite');
                        button.title = 'إزالة من المفضلة';
                    } else {
                        userFavorites.delete(parseInt(itemId));
                        button.classList.remove('is-favorite');
                        button.classList.add('not-favorite');
                        button.title = 'إضافة إلى المفضلة';
                    }
                    
                    // تحديث data attribute
                    const row = button.closest('tr');
                    row.setAttribute('data-is-favorite', action === 'add' ? '1' : '0');
                    
                    // إعادة تطبيق الفلتر الحالي
                    applyCurrentFilter();
                    
                } else {
                    console.error('خطأ في إدارة المفضلة:', result.message);
                }
                
            } catch (error) {
                console.error('خطأ في الشبكة:', error);
            }
        }

        // وظيفة تطبيق الفلتر
        function applyFilter(filterType) {
            currentFilter = filterType;
            
            // تحديث أزرار الفلتر
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');
            
            applyCurrentFilter();
        }

        function applyCurrentFilter() {
            const rows = document.querySelectorAll('#items-table-body tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const itemId = row.getAttribute('data-item-id');
                const imageCount = parseInt(row.getAttribute('data-image-count') || 0);
                const isFavorite = userFavorites.has(parseInt(itemId));
                
                let shouldShow = true;
                
                switch (currentFilter) {
                    case 'favorites':
                        shouldShow = isFavorite;
                        break;
                    case 'with-images':
                        shouldShow = imageCount > 0;
                        break;
                    case 'no-images':
                        shouldShow = imageCount === 0;
                        break;
                    case 'all':
                    default:
                        shouldShow = true;
                        break;
                }
                
                if (shouldShow) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // إظهار/إخفاء رسالة عدم وجود نتائج
            if (visibleCount === 0) {
                let message = '';
                switch (currentFilter) {
                    case 'favorites':
                        message = 'لا توجد أصناف في المفضلة';
                        break;
                    case 'with-images':
                        message = 'لا توجد أصناف مع صور';
                        break;
                    case 'no-images':
                        message = 'لا توجد أصناف بدون صور';
                        break;
                    default:
                        message = 'لا توجد أصناف';
                        break;
                }
                noResults.innerHTML = `
                    <i class="fas fa-search no-results-icon"></i>
                    <div class="no-results-text">${message}</div>
                `;
                noResults.style.display = 'block';
            } else {
                noResults.style.display = 'none';
            }
        }

        // تحسين البحث مع التخزين المؤقت والبحث الذكي
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            // إلغاء الطلب السابق إذا كان موجوداً
            if (currentSearchRequest) {
                currentSearchRequest.abort();
                currentSearchRequest = null;
            }
            
            // Remove existing spinner
            const existingSpinner = searchInput.parentNode.querySelector('.loading-spinner');
            if (existingSpinner) existingSpinner.remove();
            
            // البحث الفوري للنتائج المحفوظة مؤقتاً
            if (query.length === 0) {
                displayResults(initialItems);
                clearSearchBtn.style.display = 'none';
                return;
            }
            
            // إظهار زر المسح
            clearSearchBtn.style.display = 'flex';
            
            // التحقق من التخزين المؤقت أولاً
            if (searchCache.has(query)) {
                displayResults(searchCache.get(query));
                return;
            }
            
            // البحث المحل�� السريع في النتائج الحالية
            // البحث المحلي الذكي في النتائج الحالية
            if (query.length >= 1) {
                const scoredItems = [];
                
                initialItems.forEach(item => {
                    const score = calculateSearchScore(item.name, query);
                    if (score > 50) {
                        scoredItems.push({
                            ...item,
                            score: score
                        });
                    }
                });
                
                // ترتيب النتائج حسب النقاط
                scoredItems.sort((a, b) => b.score - a.score);
                
                if (scoredItems.length > 0) {
                    displayResults(scoredItems);
                    return;
                }
            }
            
            // Add loading spinner
            const spinner = document.createElement('div');
            spinner.className = 'loading-spinner';
            searchInput.parentNode.appendChild(spinner);
            
            // Debounce search مع تقليل الوقت
            searchTimeout = setTimeout(() => {
                // إنشاء AbortController للتحكم في الطلب
                const controller = new AbortController();
                currentSearchRequest = controller;
                
                fetch(`search_items.php?q=${encodeURIComponent(query)}&store_id=${encodeURIComponent(storeId)}`, {
                    signal: controller.signal
                })
                    .then(response => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(data => {
                        // Remove spinner
                        const spinner = searchInput.parentNode.querySelector('.loading-spinner');
                        if (spinner) spinner.remove();
                        
                        // حفظ النتيجة في التخزين المؤقت
                        searchCache.set(query, data);
                        
                        // تنظيف التخزين المؤقت إذا أصبح كبيراً
                        if (searchCache.size > 50) {
                            const firstKey = searchCache.keys().next().value;
                            searchCache.delete(firstKey);
                        }
                        
                        displayResults(data);
                        addToSearchHistory(query);
                        currentSearchRequest = null;
                    })
                    .catch(error => {
                        if (error.name !== 'AbortError') {
                            console.error('Search error:', error);
                            const spinner = searchInput.parentNode.querySelector('.loading-spinner');
                            if (spinner) spinner.remove();
                        }
                        currentSearchRequest = null;
                    });
            }, 150); // تقليل وقت التأخير من 300 إلى 150
        });

        // إضافة البحث بالضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                if (query.length > 0) {
                    // تنفيذ البحث فوراً
                    const event = new Event('input');
                    this.dispatchEvent(event);
                }
            }
        });

        // وظيفة زر المسح
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearSearchBtn.style.display = 'none';
            displayResults(initialItems);
            searchInput.focus();
        });

        // إضافة ميزة الإكمال التلقائي
        searchInput.addEventListener('focus', function() {
            showSearchSuggestions();
        });

        function showSearchSuggestions() {
            const currentValue = searchInput.value.toLowerCase().trim();
            let suggestions = [];
            
            // إضافة تاريخ البحث
            if (currentValue === '') {
                suggestions = searchHistory.slice(0, 5);
            } else {
                // البحث في تاريخ البحث
                suggestions = searchHistory.filter(item => 
                    normalizeText(item).includes(normalizeText(currentValue))
                ).slice(0, 3);
                
                // إضافة اقتراحات من أسماء المنتجات
                const productSuggestions = getSuggestions(currentValue, initialItems);
                suggestions = suggestions.concat(productSuggestions.slice(0, 5));
            }
            
            // إنشاء قائمة الاقتراحات
            createSuggestionDropdown(suggestions);
        }

        function createSuggestionDropdown(suggestions) {
            // إزالة القائمة السابقة
            const existingDropdown = document.querySelector('.search-suggestions');
            if (existingDropdown) {
                existingDropdown.remove();
            }
            
            if (suggestions.length === 0) return;
            
            const dropdown = document.createElement('div');
            dropdown.className = 'search-suggestions';
            dropdown.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--white);
                border: 2px solid var(--border-color);
                border-top: none;
                border-radius: 0 0 15px 15px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                max-height: 200px;
                overflow-y: auto;
                font-family: 'Cairo', Arial, sans-serif;
                direction: rtl;
            `;
            
            // تطبيق الثيم الداكن
            if (document.body.classList.contains('dark-mode')) {
                dropdown.style.background = 'var(--dark-surface-light)';
                dropdown.style.borderColor = 'var(--dark-border-light)';
                dropdown.style.boxShadow = '0 4px 15px rgba(26, 35, 50, 0.3)';
            }
            
            suggestions.forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.style.cssText = `
                    padding: 12px 15px;
                    cursor: pointer;
                    border-bottom: 1px solid var(--border-color);
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                `;
                
                // تحديد نوع الاقتراح
                const isHistory = searchHistory.includes(suggestion);
                const icon = isHistory ? 'fas fa-history' : 'fas fa-search';
                const iconColor = isHistory ? 'var(--secondary-color)' : 'var(--primary-color)';
                
                item.innerHTML = `
                    <i class="${icon}" style="color: ${iconColor}; font-size: 0.9rem;"></i>
                    <span>${suggestion}</span>
                    ${isHistory ? '<i class="fas fa-times" style="margin-left: auto; color: var(--danger-color); font-size: 0.8rem;" onclick="removeFromHistory(event, \'' + suggestion + '\')"></i>' : ''}
                `;
                
                // تطبيق الثيم الداكن
                if (document.body.classList.contains('dark-mode')) {
                    item.style.borderBottomColor = 'var(--dark-border-light)';
                    item.style.color = 'var(--dark-text)';
                }
                
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
                
                item.addEventListener('click', function(e) {
                    if (!e.target.classList.contains('fa-times')) {
                        searchInput.value = suggestion;
                        searchInput.dispatchEvent(new Event('input'));
                        dropdown.remove();
                        addToSearchHistory(suggestion);
                    }
                });
                
                dropdown.appendChild(item);
            });
            
            searchInput.parentElement.appendChild(dropdown);
        }

        // وظيفة حذف من تاريخ البحث
        window.removeFromHistory = function(event, suggestion) {
            event.stopPropagation();
            searchHistory = searchHistory.filter(item => item !== suggestion);
            localStorage.setItem('usersSearchHistory', JSON.stringify(searchHistory));
            showSearchSuggestions();
        };

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !e.target.closest('.search-suggestions')) {
                const dropdown = document.querySelector('.search-suggestions');
                if (dropdown) {
                    dropdown.remove();
                }
            }
        });

        function displayResults(items) {
            itemsTableBody.innerHTML = '';
            
            if (items.length > 0) {
                items.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.className = 'fade-in';
                    row.style.animationDelay = `${index * 0.05}s`; // تأثير تدريجي
                    row.setAttribute('data-item-id', item.item_id || '');
                    row.setAttribute('data-item-name', item.name || '');
                    row.setAttribute('data-image-count', item.image_count || 0);
                    
                    // إضافة معلومات إضافية إذا كانت متوفرة
                    let categoryInfo = '';
                    if (item.category_name && item.category_name !== item.name) {
                        categoryInfo = `<small class="item-details" style="display: block; font-size: 0.8em; margin-top: 2px;">التصنيف: ${item.category_name}</small>`;
                    }
                    
                    let barcodeInfo = '';
                    if (item.barcode && item.barcode.trim() !== '') {
                        barcodeInfo = `<small class="item-details" style="display: block; font-size: 0.8em; margin-top: 2px;">الباركود: ${item.barcode}</small>`;
                    }

                    // إنشاء زر المفضلة
                    const isFavorite = userFavorites.has(item.item_id);
                    const favoriteButton = `<button class="favorite-btn ${isFavorite ? 'is-favorite' : 'not-favorite'}" 
                                                    data-item-id="${item.item_id}" 
                                                    title="${isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة'}">
                                                <i class="fas fa-star"></i>
                                            </button>`;

                    // إنشاء زر عرض الصور (فقط إذا كانت هناك صلاحية)
                    const imageCount = item.image_count || 0;
                    let imageCell = '';
                    if (canViewImages) {
                        if (imageCount > 0) {
                            imageCell = `<td style="text-align: center;">
                                            <button class="btn btn-info btn-sm view-images-btn" data-item-id="${item.item_id}" title="عرض صور الصنف">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>`;
                        } else {
                            imageCell = `<td style="text-align: center;">
                                            <button class="btn btn-secondary btn-sm" disabled title="لا توجد صور لهذا الصنف">
                                                <i class="fas fa-eye-slash"></i>
                                            </button>
                                        </td>`;
                        }
                    }
                    
                    // إنشاء خلية السعر (فقط إذا كانت هناك صلاحية)
                    let priceCell = '';
                    if (canViewPrices) {
                        priceCell = `<td>
                                        <div class="item-price">
                                            <i class="fas fa-coins item-icon"></i>
                                            ${parseFloat(item.price).toFixed(2)} جنيه
                                        </div>
                                    </td>`;
                    }

                    row.innerHTML = `
                        <td>
                            <div class="item-name">
                                <i class="fas fa-box-open item-icon text-primary"></i>
                                <div style="flex: 1;">
                                    <div>${item.name}</div>
                                    ${categoryInfo}
                                    ${barcodeInfo}
                                </div>
                            </div>
                        </td>
                        <td style="text-align: center;">
                            ${favoriteButton}
                        </td>
                        ${imageCell}
                        ${priceCell}
                    `;
                    itemsTableBody.appendChild(row);
                });
                
                // إضافة event listeners لأزرار المفضلة
                document.querySelectorAll('.favorite-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.getAttribute('data-item-id');
                        toggleFavorite(itemId, this);
                    });
                });

                // إضافة event listeners لأزرار عرض الصور
                document.querySelectorAll('.view-images-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.getAttribute('data-item-id');
                        const row = this.closest('tr');
                        const itemName = row.getAttribute('data-item-name');
                        viewItemImages(itemId, itemName);
                    });
                });

                // تطبيق الفلتر الحالي
                applyCurrentFilter();
                
                noResults.style.display = 'none';
            } else {
                // إنشاء رسالة "لا توجد نتائج" مع اقتراحات
                const query = searchInput.value.trim();
                if (query !== '') {
                    // الحصول على اقتراحات
                    const suggestions = getSuggestions(query, initialItems);
                    
                    let suggestionsHtml = '';
                    if (suggestions.length > 0) {
                        suggestionsHtml = `
                            <div style="margin-top: 15px;">
                                <div style="font-size: 0.9rem; margin-bottom: 8px; color: var(--primary-color);">اقتراحات:</div>
                                <div style="display: flex; flex-wrap: wrap; gap: 5px; justify-content: center;">
                                    ${suggestions.map(suggestion => 
                                        `<span class="suggestion-tag" style="background: var(--primary-color); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; cursor: pointer; transition: all 0.2s ease;" onclick="searchInput.value='${suggestion}'; searchInput.dispatchEvent(new Event('input'));">${suggestion}</span>`
                                    ).join('')}
                                </div>
                            </div>
                        `;
                    }
                    
                    noResults.innerHTML = `
                        <i class="fas fa-search no-results-icon"></i>
                        <div class="no-results-text">لا توجد نتائج للبحث "${query}"</div>
                        <div style="font-size: 0.9rem; margin-top: 5px; color: var(--secondary-color);">جرب كلمات مختلفة أو تأكد من الإملاء</div>
                        ${suggestionsHtml}
                        <div style="margin-top: 15px; font-size: 0.8rem; color: var(--info-color);">
                            💡 نصيحة: جرب البحث بجزء من اسم المنتج أو استخدم كلمات مفتاحية
                        </div>
                    `;
                }
                noResults.style.display = 'block';
            }
        }

        // Scroll to top functionality
        const scrollToTopButton = document.getElementById('scroll-to-top');

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                scrollToTopButton.classList.add('show');
            } else {
                scrollToTopButton.classList.remove('show');
            }
        });

        scrollToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // إضافة البحث الصوتي
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            
            recognition.lang = 'ar-SA';
            recognition.continuous = false;
            recognition.interimResults = false;
            
            // إضافة زر البحث الصوتي
            const voiceButton = document.createElement('button');
            voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
            voiceButton.className = 'voice-search-btn';
            voiceButton.title = 'البحث الصوتي';
            
            voiceButton.addEventListener('click', function() {
                recognition.start();
                this.style.color = 'var(--danger-color)';
                this.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            });
            
            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                searchInput.value = transcript;
                searchInput.dispatchEvent(new Event('input'));
                voiceButton.style.color = 'var(--secondary-color)';
                voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
            };
            
            recognition.onerror = function() {
                voiceButton.style.color = 'var(--secondary-color)';
                voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
            };
            
            searchInput.parentElement.appendChild(voiceButton);
        }

        // وظائف عرض الصور
        window.viewItemImages = function(itemId, itemName) {
            const modal = document.getElementById('imageModal');
            const modalTitle = document.getElementById('modalItemName');
            const modalContent = document.getElementById('modalImageContent');
            
            modalTitle.textContent = `صور الصنف: ${itemName}`;
            modalContent.innerHTML = '<div style="text-align: center; padding: 40px;"><i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-color);"></i><br><br>جاري تحميل الصور...</div>';
            
            modal.style.display = 'block';
            
            // جلب الصور من الخادم
            fetch(`get_item_images.php?item_id=${encodeURIComponent(itemId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.images && data.images.length > 0) {
                            let imagesHtml = '<div class="images-gallery">';
                            data.images.forEach(image => {
                                imagesHtml += `
                                    <div class="image-item">
                                        <img src="${image.img_path}" 
                                             alt="صورة الصنف" 
                                             onclick="openFullsizeImage('${image.img_path}')"
                                             onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\'padding: 20px; color: var(--danger-color);\\'>فشل في تحميل الصورة</div>';">
                                    </div>
                                `;
                            });
                            imagesHtml += '</div>';
                            modalContent.innerHTML = imagesHtml;
                        } else {
                            modalContent.innerHTML = `
                                <div class="no-images">
                                    <i class="fas fa-image"></i>
                                    <p>لا توجد صور لهذا الصنف</p>
                                </div>
                            `;
                        }
                    } else {
                        modalContent.innerHTML = `
                            <div class="no-images">
                                <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                                <p style="color: var(--danger-color);">حدث خطأ في تحميل الصور</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error fetching images:', error);
                    modalContent.innerHTML = `
                        <div class="no-images">
                            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                            <p style="color: var(--danger-color);">حدث خطأ في الاتصال بالخادم</p>
                        </div>
                    `;
                });
        };

        window.closeImageModal = function() {
            document.getElementById('imageModal').style.display = 'none';
        };

        window.openFullsizeImage = function(imageSrc) {
            const fullsizeModal = document.getElementById('fullsizeModal');
            const fullsizeImage = document.getElementById('fullsizeImage');
            
            fullsizeImage.src = imageSrc;
            fullsizeModal.style.display = 'block';
        };

        window.closeFullsizeModal = function() {
            document.getElementById('fullsizeModal').style.display = 'none';
        };

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const imageModal = document.getElementById('imageModal');
            const fullsizeModal = document.getElementById('fullsizeModal');
            
            if (event.target === imageModal) {
                closeImageModal();
            }
            if (event.target === fullsizeModal) {
                closeFullsizeModal();
            }
        };

        // إغلاق النوافذ بمفتاح Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
                closeFullsizeModal();
            }
        });

        // إضافة event listeners لأزرار الفلترة
        document.querySelectorAll('.filter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const filterType = this.getAttribute('data-filter');
                applyFilter(filterType);
            });
        });

        // إضافة event listeners لأزرار المفضلة الموجودة
        document.querySelectorAll('.favorite-btn').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-item-id');
                toggleFavorite(itemId, this);
            });
        });

        // إضافة event listeners لأزرار عرض الصور الموجودة
        document.querySelectorAll('.view-images-btn').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-item-id');
                const row = this.closest('tr');
                const itemName = row.getAttribute('data-item-name');
                viewItemImages(itemId, itemName);
            });
        });
    </script>
</body>

<!-- Include Bottom Navigation -->
<?php include 'bottom_nav.php'; ?>

</html>