<?php

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_GET['store_id'], $key);
$account_id = isset($_GET['account_id']) ? $_GET['account_id'] : $_SESSION['account_id']; // Use account_id directly

$sql_categories = "SELECT * FROM categories WHERE store_id = ?";
$stmt = $conn->prepare($sql_categories);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$categories_result = $stmt->get_result();
$stmt->close();

// Get account_id for favorites
$decrypted_account_id = decrypt($account_id, $key);

if ($decrypted_account_id) {
    $sql_items = "SELECT i.*, 
                  (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite,
                  (SELECT COUNT(*) FROM item_images WHERE item_id = i.item_id) AS image_count
                  FROM items i 
                  JOIN categories c ON i.category_id = c.category_id 
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("iii", $decrypted_account_id, $store_id, $decrypted_account_id);
} else {
    $sql_items = "SELECT i.*, 0 as is_favorite, 0 as image_count
                  FROM items i 
                  JOIN categories c ON i.category_id = c.category_id 
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("i", $store_id);
}

$stmt->execute();
$items_result = $stmt->get_result();
$stmt->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فاتورة شراء</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/invoice.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
<div class="container">
    <div class="categories">
        <h3>تصنيف</h3>
        <input type="text" id="categorySearch" class="search-bar" placeholder="ابحث عن تصنيف...">
        <div class="category-boxes" id="categoryBoxes">
            <!-- إضافة مربع كل الأصناف بشكل افتراضي -->
            <div class="category-box active" data-category-id="all">
                <i class="fas fa-boxes"></i>
                <span>كل الأصناف</span>
            </div>
            <?php
            if ($categories_result->num_rows > 0) {
                while($row = $categories_result->fetch_assoc()) {
                    $encrypted_category_id = encrypt($row['category_id'], $key);
                    echo '<div class="category-box" data-category-id="' . $encrypted_category_id . '">';
                    echo '<i class="fas fa-box"></i>';
                    echo '<span>' . $row['name'] . '</span>';
                    echo '</div>';
                }
            }
            ?>
        </div>
    </div>

    <div class="items">
        <h3>الأصناف</h3>
        <input type="text" id="itemSearch" class="search-bar" placeholder="ابحث عن صنف...">
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th class="barcode-col">الباركود</th>
                    <th>الكمية</th>
                    <th>الصور</th>
                </tr>
            </thead>
            <tbody id="itemsList">
                <?php
                if ($items_result->num_rows > 0) {
                    while($row = $items_result->fetch_assoc()) {
                        $quantityClass = $row['quantity'] < 10 ? 'red' : 'green';
                        $favoriteIcon = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                        $favoriteClass = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? 'favorite-item' : '';
                        $image_class = $row['image_count'] > 0 ? 'has-images-icon' : 'no-images-icon';
                        $image_title = $row['image_count'] > 0 ? 'عرض الصور' : 'لا توجد صور';
                        $encId = htmlspecialchars(encrypt($row['item_id'], $key));
                        $row['encrypted_id'] = $encId;
                        $rowJson = htmlspecialchars(json_encode($row), ENT_QUOTES, 'UTF-8');
                        ?>
                        <tr class="<?php echo $favoriteClass; ?>" data-item="<?php echo $rowJson; ?>" onclick="addItemToInvoice(JSON.parse(this.dataset.item))">
                            <td><?php echo $favoriteIcon . $row['name']; ?></td>
                            <td class="barcode-col"><?php echo $row['barcode']; ?></td>
                            <td class="item-quantity <?php echo $quantityClass; ?>"><?php echo $row['quantity']; ?></td>
                            <td>
                                <?php
                                if ($row['image_count'] > 0) {
                                    echo "<button class='action-btn has-images-icon' type='button' title='عرض الصور' onclick=\"viewItemImages('{$encId}', event)\"><i class='fas fa-eye'></i></button>";
                                } else {
                                    echo "<button class='action-btn no-images-icon' type='button' title='لا توجد صور' disabled onclick='event.stopPropagation();'><i class='fas fa-eye-slash'></i></button>";
                                }
                                ?>
                            </td>
                        </tr>
                        <?php
                    }
                } else {
                    echo "<tr><td colspan='4'>لا توجد أصناف حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>

    <div class="selected-items">
        <h3>الأصناف المختارة</h3>
        <form id="purchaseInvoiceForm" method="POST" action="save_purchase_invoice.php" enctype="multipart/form-data">
            <input type="hidden" name="store_id" value="<?php echo htmlspecialchars($_GET['store_id']); ?>">
            <input type="hidden" name="account_id" value="<?php echo htmlspecialchars($account_id); ?>">
            <input type="hidden" name="total_amount" id="total_amount_input" />
            <div>
                <label for="invoice_status">حالة الفاتورة:</label>
                <select id="invoice_status" name="invoice_status" class="input-field" required>
                    <option value="confirmed" selected>مؤكدة</option>
                    <option value="pending">معلقة</option>
                </select>
            </div>
            <div>
                <label for="invoice_images">صور الفاتورة:</label>
                <div class="image-upload-container">
                    <label for="invoice_images" class="image-upload-label">
                        <i class="fas fa-upload"></i> اختر الصور
                    </label>
                    <input type="file" id="invoice_images" name="invoice_images[]" multiple accept="image/*" class="image-upload-input">
                    <div id="image-preview" class="image-preview"></div>
                </div>
            </div>
            <div class="table-wrapper">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الصنف</th>
                            <th class="barcode-col">الباركود</th>
                            <th>الكمية</th>
                            <th>الصور</th>
                            <th>جملة</th>
                            <th>إجمالي</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="selectedItemsTable">
                        <!-- Selected items will be added here -->
                    </tbody>
                </table>
            </div>
            <div id="total_amount" class="total-display">
                <span>إجمالي الفاتورة: <span id="total_amount_value">0.00</span></span>
                <button type="button" class="add-btn" onclick="confirmSaveInvoice()">حفظ الفاتورة</button>
            </div>
        </form>
    </div>
</div>

<style>
    /* أنماط مربعات التصنيفات */
    .category-boxes {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin: 20px 0;
        justify-content: center;
        padding: 10px;
    }
    
    .category-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 130px;
        height: 130px;
        background-color: var(--color-secondary);
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 2px solid #eee;
        text-align: center;
    }
    
    .category-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        border-color: var(--color-primary);
    }
    
    .category-box.active {
        background-color: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
        transform: translateY(-3px);
    }
    
    .category-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            135deg, 
            rgba(255, 255, 255, 0.3) 0%, 
            rgba(255, 255, 255, 0) 60%
        );
        z-index: 1;
    }
    
    .category-box i {
        font-size: 38px;
        margin-bottom: 12px;
        color: var(--color-primary);
        z-index: 2;
        position: relative;
    }
    
    .category-box.active i {
        color: white;
    }
    
    .category-box span {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        z-index: 2;
        position: relative;
    }
    
    /* تنسيق للوضع المظلم */
    [data-theme="dark"] .category-box {
        background-color: var(--color-secondary);
        border-color: #444;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    }
    
    [data-theme="dark"] .category-box:hover {
        border-color: var(--color-primary);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    }
    
    [data-theme="dark"] .category-box.active {
        background-color: var(--color-primary);
        border-color: var(--color-primary);
    }
    
    [data-theme="dark"] .category-box::before {
        background: linear-gradient(
            135deg, 
            rgba(255, 255, 255, 0.1) 0%, 
            rgba(255, 255, 255, 0) 60%
        );
    }
    
    /* تنسيق شريط البحث */
    .search-bar {
        width: 60%;
        padding: 12px 15px;
        border-radius: 25px;
        border: 1px solid #ddd;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        margin: 15px auto;
        top: 100px;
        display: block;
    }
    
    .search-bar:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
    }
    
    [data-theme="dark"] .search-bar {
        background-color: #333;
        color: #eee;
        border-color: #444;
    }
    
    /* أنماط الأصناف المفضلة */
    
    /* تحسين العرض على الشاشات الصغيرة */
    @media (max-width: 768px) {
        .category-box {
            width: 110px;
            height: 110px;
            padding: 10px;
        }
        
        .category-box i {
            font-size: 30px;
            margin-bottom: 8px;
        }
        
        .category-box span {
            font-size: 14px;
        }
    }
    
    @media (max-width: 480px) {
        .category-box {
            width: 90px;
            height: 90px;
        }
        
        .category-box i {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .category-box span {
            font-size: 12px;
        }
        
        .category-boxes {
            gap: 10px;
        }
    }
    
    .barcode-col {display:none;}
    .has-images-icon i {color:#28a745;}
    .no-images-icon i {color:#aaa;}
    .no-images-icon {cursor: default;pointer-events: none;opacity: 0.6;}
    .images-gallery {display:flex;flex-wrap:wrap;gap:10px;justify-content:center;}
</style>

<!-- Modal for viewing item images -->
<div id="viewImagesModal" class="modal">
    <div class="modal-content" style="max-width:800px;">
        <span class="close" onclick="closeViewImagesModal()">&times;</span>
        <h2 id="modalItemName">صور الصنف</h2>
        <div id="modalImagesContainer" class="images-gallery"></div>
    </div>
</div>

<script>
    function loadItems(encryptedCategoryId) {
        // إذا التصنيف هو "كل الأصناف"، عرض جميع الأصناف
        if (encryptedCategoryId === 'all') {
            document.querySelectorAll('#itemsList tr').forEach(row => {
                row.style.display = '';
            });
            return;
        }
        
        fetch(`get_items.php?category_id=${encodeURIComponent(encryptedCategoryId)}`)
            .then(response => response.json())
            .then(data => {
                const itemsList = document.getElementById('itemsList');
                
                // عرض فقط العناصر للتصنيف المحدد
                document.querySelectorAll('#itemsList tr').forEach(row => {
                    row.style.display = 'none';
                });
                
                data.items.forEach(item => {
                    const quantityClass = item.quantity < 10 ? 'red' : 'green';
                    const favoriteIcon = item.is_favorite && item.is_favorite > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                    const favoriteClass = item.is_favorite && item.is_favorite > 0 ? 'favorite-item' : '';
                    const image_class = item.image_count > 0 ? 'has-images-icon' : 'no-images-icon';
                    const image_title = item.image_count > 0 ? 'عرض الصور' : 'لا توجد صور';
                    
                    // البحث عن الصف الحالي أولاً
                    let tr = null;
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        const cellText = row.querySelector('td')?.textContent;
                        if (cellText && cellText.includes(item.name)) {
                            tr = row;
                            tr.style.display = '';
                            tr.className = favoriteClass;
                            // تحديث محتوى الخلية لإضافة أيقونة المفضلة
                            const firstCell = tr.querySelector('td');
                            if (firstCell && !firstCell.innerHTML.includes('fa-star')) {
                                firstCell.innerHTML = favoriteIcon + item.name;
                            }
                        }
                    });
                    
                    // إذا لم يتم العثور على الصف، قم بإنشائه
                    if (!tr) {
                        tr = document.createElement('tr');
                        tr.className = favoriteClass;
                        tr.innerHTML = `
                            <td>${favoriteIcon}${item.name}</td>
                            <td class="barcode-col">${item.barcode}</td>
                            <td class="item-quantity ${quantityClass}">${item.quantity}</td>
                            <td><button class='action-btn ${image_class}' type='button' title='${image_title}' onclick='viewItemImages("${item.encrypted_id}")'><i class='fas fa-eye'></i></button></td>
                        `;
                        tr.onclick = () => addItemToInvoice(item);
                        itemsList.appendChild(tr);
                    }
                });
            })
            .catch(error => console.error('Error:', error));
    }

    function saveTempItems() {
        const formData = new FormData();
        formData.append('account_id', "<?php echo htmlspecialchars($account_id); ?>"); // Pass account_id

        const items = [];
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        rows.forEach(row => {
            const itemId = row.getAttribute('data-item-id');
            const quantity = row.querySelector('input[type="number"]').value;
            const cost = row.cells[4].textContent;
            items.push({ item_id: itemId, quantity: parseFloat(quantity), cost: parseFloat(cost) });
        });

        formData.append('items', JSON.stringify(items));

        fetch('save_temp_purchase_invoice.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Error saving temporary items:', data.error);
            }
        })
        .catch(error => console.error('Error saving temporary items:', error));
    }

    function addItemToInvoice(item) {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        let existingRow = document.querySelector(`#selectedItemsTable tr[data-item-id="${item.item_id}"]`);
        if (existingRow) {
            const quantityInput = existingRow.querySelector('input[type="number"]');
            quantityInput.value = parseFloat(quantityInput.value) + 1;
        } else {
            const row = document.createElement('tr');
            row.setAttribute('data-item-id', item.item_id);
            row.innerHTML = `
                <td>${item.name}</td>
                <td class="barcode-col">${item.barcode}</td>
                <td>
                    <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" name="items[${item.item_id}][quantity]" value="1" min="0.1" step="0.1" class="quantity-input device-status green" required oninput="updateTotal(); saveTempItems()">
                    <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">
                        <i class="fas fa-plus"></i>
                    </button>
                </td>
                <td>
                    ${item.image_count > 0 ? `<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages('${item.encrypted_id}', event)"><i class="fas fa-eye"></i></button>` : `<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>`}
                </td>
                <td>${item.cost}</td>
                <td>0.00</td>
                <td>
                    <button type="button" class="action-btn" onclick="removeItemFromInvoice(this)">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            selectedItemsTable.appendChild(row);
        }
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function removeItemFromInvoice(button) {
        const row = button.parentElement.parentElement;
        row.remove();
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function increaseQuantity(button) {
        const input = button.previousElementSibling;
        input.value = (parseFloat(input.value) + 0.1).toFixed(1);
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function decreaseQuantity(button) {
        const input = button.nextElementSibling;
        if (input.value > 0.1) {
            input.value = (parseFloat(input.value) - 0.1).toFixed(1);
            updateTotal();
            saveTempItems(); // Save items to the temporary JSON file
        }
    }

    function updateTotal() {
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        let total = 0;
        rows.forEach(row => {
            const quantityInput = row.querySelector('input[type="number"]');
            const costCell = row.cells[4];
            const totalCell = row.cells[5];

            if (quantityInput && costCell && totalCell) { // Ensure all elements exist
                const quantity = parseFloat(quantityInput.value) || 0;
                const cost = parseFloat(costCell.textContent) || 0;
                const subtotal = quantity * cost;
                totalCell.textContent = subtotal.toFixed(2);
                total += subtotal;
            }
        });
        document.getElementById('total_amount_value').textContent = total.toFixed(2);
        document.getElementById('total_amount_input').value = total.toFixed(2);
    }

    document.getElementById('categorySearch').oninput = function() {
        const filter = this.value.toLowerCase();
        
        document.querySelectorAll('.category-box').forEach(box => {
            const text = box.querySelector('span').textContent.toLowerCase();
            if (text.includes(filter) || box.getAttribute('data-category-id') === 'all') {
                box.style.display = '';
            } else {
                box.style.display = 'none';
            }
        });
    }

    document.getElementById('itemSearch').oninput = function() {
        const filter = this.value.toLowerCase();
        
        const itemsList = document.getElementById('itemsList');
        const items = itemsList.getElementsByTagName('tr');
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const text = item.textContent || item.innerText;
            item.style.display = text.toLowerCase().indexOf(filter) > -1 ? '' : 'none';
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        // إضافة تحديث دوري للجلسة كل 10 دقائق
        setInterval(refreshSession, 10 * 60 * 1000);
        
        const accountId = "<?php echo htmlspecialchars($account_id); ?>"; // Pass account_id
        fetch(`load_temp_purchase_invoice.php?account_id=${encodeURIComponent(accountId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.items.length > 0) {
                    const selectedItemsTable = document.getElementById('selectedItemsTable');
                    const itemIds = data.items.map(item => item.item_id).join(',');

                    // Fetch item details from the database
                    fetch(`get_item_details.php?item_ids=${encodeURIComponent(itemIds)}`)
                        .then(response => response.json())
                        .then(itemDetails => {
                            data.items.forEach(item => {
                                const details = itemDetails.find(detail => detail.item_id == item.item_id);
                                if (details) {
                                    const row = document.createElement('tr');
                                    row.setAttribute('data-item-id', item.item_id);
                                    row.innerHTML = `
                                        <td>${details.name}</td>
                                        <td class="barcode-col">${details.barcode}</td>
                                        <td>
                                            <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" name="items[${item.item_id}][quantity]" value="${item.quantity}" min="0.1" step="0.1" class="quantity-input device-status green" required oninput="updateTotal(); saveTempItems()">
                                            <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </td>
                                        <td>
                                            ${item.image_count > 0 ? `<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages('${item.encrypted_id}', event)"><i class="fas fa-eye"></i></button>` : `<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>`}
                                        </td>
                                        <td>${item.cost}</td>
                                        <td>${(item.quantity * item.cost).toFixed(2)}</td>
                                        <td>
                                            <button type="button" class="action-btn" onclick="removeItemFromInvoice(this)">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    `;
                                    selectedItemsTable.appendChild(row);
                                }
                            
                            });
                            updateTotal();
                        })
                        .catch(error => console.error('Error fetching item details:', error));
                }
            })
            .catch(error => console.error('Error loading temporary items:', error));
    });

    document.getElementById('purchaseInvoiceForm').addEventListener('submit', async function (event) {
        event.preventDefault();
        const formData = new FormData(this);

        // Show loading modal
        Swal.fire({
            title: 'جاري تحميل الصور...',
            text: 'يرجى الانتظار حتى يتم تحميل الصور ...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Handle image uploads with compression
        const imageFiles = document.getElementById('invoice_images').files;
        const uploadedImagePaths = [];
        for (const file of imageFiles) {
            const compressedFile = await compressImage(file); // Compress the image
            const uploadFormData = new FormData();
            uploadFormData.append('image', compressedFile);
            uploadFormData.append('type', 'purchase'); // Specify the type as 'purchase'

            const response = await fetch('upload_image.php', {
                method: 'POST',
                body: uploadFormData
            });
            const result = await response.json();

            if (result.path) {
                uploadedImagePaths.push(result.path);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في تحميل الصور',
                    text: result.error || 'حدث خطأ أثناء تحميل الصور.'
                });
                return;
            }
        }

        // Add uploaded image paths to the form data
        uploadedImagePaths.forEach((path, index) => {
            formData.append(`uploaded_images[${index}]`, path);
        });

        // Submit the form data to save the purchase invoice
        fetch('save_purchase_invoice.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            Swal.close(); // Close the loading modal
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم إضافة الفاتورة بنجاح.',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    window.location.href = 'purchase_invoices.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في إضافة الفاتورة.',
                    text: data.message || 'حدث خطأ أثناء إضافة الفاتورة.',
                    showConfirmButton: true
                });
            }
        })
        .catch(error => {
            Swal.close(); // Close the loading modal
            console.error('Error saving purchase invoice:', error);
            Swal.fire({
                icon: 'error',
                title: 'فشل في إضافة الفاتورة.',
                text: 'حدث خطأ أثناء إضافة الفاتورة.',
                showConfirmButton: true
            });
        });
    });

    // Compress image before upload
    async function compressImage(file) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const maxDim = 800; // Maximum dimension for compression
                let { width, height } = img;
                if (width > height && width > maxDim) {
                    height = height * (maxDim / width);
                    width = maxDim;
                } else if (height > maxDim) {
                    width = width * (maxDim / height);
                    height = maxDim;
                }
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(blob => {
                    resolve(new File([blob], file.name, { type: 'image/jpeg' }));
                }, 'image/jpeg', 0.75); // Compress to 75% quality
            };
            img.src = URL.createObjectURL(file);
        });
    }

    document.getElementById('invoice_images').addEventListener('change', function (event) {
        const files = event.target.files;
        const previewContainer = document.getElementById('image-preview');
        previewContainer.innerHTML = ''; // Clear previous previews

        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function (e) {
                const previewDiv = document.createElement('div');
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <i class="fas fa-times remove-icon"></i>
                `;
                previewDiv.querySelector('.remove-icon').addEventListener('click', () => {
                    previewDiv.remove();
                });
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        });
    });

    function confirmSaveInvoice() {
        // تحديث الإجمالي قبل الحفظ مباشرة لضمان تحديثه
        updateTotal();
        
        // التحقق من وجود أصناف في الفاتورة
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        if (rows.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'لا يمكن حفظ فاتورة فارغة، يرجى إضافة أصناف أولاً'
            });
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'هل تريد حفظ الفاتورة؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، حفظ',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const form = document.getElementById('purchaseInvoiceForm');
                const formData = new FormData(form);
                
                // إضافة الإجمالي مرة أخرى للتأكد من إرساله
                const totalAmount = document.getElementById('total_amount_value').textContent;
                formData.set('total_amount', totalAmount);
                
                // تحديث حالة جلسة المستخدم قبل الإرسال
                fetch('refresh_session.php')
                    .then(() => {
                        // Show loading modal
                        Swal.fire({
                            title: 'جاري حفظ الفاتورة...',
                            text: 'يرجى الانتظار حتى يتم حفظ الفاتورة.',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Submit the form data via fetch
                        fetch('save_purchase_invoice.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            Swal.close(); // Close the loading modal
                            if (data.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'تم بنجاح',
                                    text: 'تم حفظ فاتورة الشراء بنجاح',
                                    confirmButtonText: 'موافق'
                                }).then(() => {
                                    // Redirect to purchase invoices page
                                    window.location.href = `purchase_invoices.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>`;
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'فشلت العملية',
                                    text: data.message || 'حدث خطأ أثناء حفظ الفاتورة',
                                    confirmButtonText: 'موافق'
                                });
                            }
                        })
                        .catch(error => {
                            Swal.close(); // Close the loading modal
                            console.error('Error saving purchase invoice:', error);
                            Swal.fire({
                                icon: 'error',
                                title: 'فشل في إضافة الفاتورة.',
                                text: 'حدث خطأ أثناء إضافة الفاتورة.',
                                showConfirmButton: true
                            });
                        });
                    })
                    .catch(error => {
                        console.error('Error refreshing session:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ في الاتصال',
                            text: 'يرجى التأكد من اتصالك بالإنترنت والمحاولة مرة أخرى.'
                        });
                    });
            }
        });
    }

    // دالة لتحديث جلسة المستخدم
    function refreshSession() {
        fetch('refresh_session.php')
            .then(response => response.json())
            .then(data => {
                console.log('Session refreshed successfully');
            })
            .catch(error => {
                console.error('Error refreshing session:', error);
            });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // إضافة معالج أحداث لمربعات التصنيف
        const categoryBoxes = document.querySelectorAll('.category-box');
        
        categoryBoxes.forEach(box => {
            box.addEventListener('click', function() {
                // إزالة الكلاس active من جميع المربعات
                categoryBoxes.forEach(b => b.classList.remove('active'));
                
                // إضافة الكلاس active للمربع المحدد
                this.classList.add('active');
                
                const categoryId = this.getAttribute('data-category-id');
                
                // إذا تم تحديد "كل الأصناف"
                if (categoryId === 'all') {
                    // عرض جميع الأصناف
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    // تحميل الأصناف للتصنيف المحدد
                    loadItems(categoryId);
                }
            });
        });
        
        // معالج أحداث البحث في التصنيفات
        document.getElementById('categorySearch').addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            
            document.querySelectorAll('.category-box').forEach(box => {
                const text = box.querySelector('span').textContent.toLowerCase();
                if (text.includes(searchText) || box.getAttribute('data-category-id') === 'all') {
                    box.style.display = '';
                } else {
                    box.style.display = 'none';
                }
            });
        });
    });

    // View Item Images Functions
    let currentItemId = null;
    function viewItemImages(encryptedItemId, evt) {
        if(evt){evt.stopPropagation();}
        currentItemId = encryptedItemId;
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const imagesContainer = document.getElementById('modalImagesContainer');
                    imagesContainer.innerHTML = '';
                    document.getElementById('modalItemName').textContent = data.item.name;

                    if (data.item.images && data.item.images.length > 0) {
                        data.item.images.forEach(img => {
                            const imgElem = document.createElement('img');
                            imgElem.src = img.img_path;
                            imgElem.style.maxWidth = '150px';
                            imgElem.style.borderRadius = '8px';
                            imagesContainer.appendChild(imgElem);
                        });
                    } else {
                        imagesContainer.innerHTML = '<p>لا توجد صور متاحة لهذا الصنف.</p>';
                    }

                    document.getElementById('viewImagesModal').classList.add('active');
                } else {
                    Swal.fire({icon:'error',title:'خطأ',text:'فشل في جلب الصور'});
                }
            })
            .catch(err => {console.error(err);Swal.fire({icon:'error',title:'خطأ',text:'تعذر الاتصال بالخادم'});});
    }

    function closeViewImagesModal() {
        document.getElementById('viewImagesModal').classList.remove('active');
    }

    window.onclick = function(event) {
        const modal = document.getElementById('viewImagesModal');
        if (event.target === modal) {
            modal.classList.remove('active');
        }
    };

    // ميزة قارئ الباركود
    let globalBarcode = "";
    let barcodeTimer = null;

    document.addEventListener('keypress', function(e) {
        // تجاهل إذا كان التركيز على حقل إدخال أو إذا كان المفتاح Enter
        if (document.activeElement.tagName.toLowerCase() === "input" || e.key === "Enter") return;

        globalBarcode += e.key;

        if (barcodeTimer) clearTimeout(barcodeTimer);

        barcodeTimer = setTimeout(() => {
            processBarcode(globalBarcode);
            globalBarcode = "";
        }, 150);
    });

    function processBarcode(barcode) {
        // البحث عن الصنف بناءً على الباركود من الأصناف المعروضة حالياً
        const itemRows = document.querySelectorAll('#itemsList tr');
        let found = null;

        itemRows.forEach(row => {
            const barcodeCell = row.querySelector('.barcode-col');
            if (barcodeCell && barcodeCell.textContent.trim() === barcode.trim()) {
                const itemData = row.getAttribute('data-item');
                if (itemData) {
                    try {
                        found = JSON.parse(itemData);
                    } catch (e) {
                        console.error('Error parsing item data:', e);
                    }
                }
            }
        });

        if (found) {
            addItemToInvoice(found);
            // إظهار رسالة نجاح
            Swal.fire({
                icon: 'success',
                title: 'تم إضافة الصنف',
                text: `تم إضافة ${found.name} للفاتورة`,
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        } else {
            // إظهار رسالة خطأ
            Swal.fire({
                icon: 'error',
                title: 'صنف غير موجود',
                text: "لم يتم العثور على صنف بهذا الباركود: " + barcode,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }
</script>

<script src="js/theme.js"></script>

</body>
</html>

<?php
$conn->close();
?>
