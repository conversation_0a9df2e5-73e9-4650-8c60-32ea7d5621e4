<?php
// منع التخزين المؤقت لضمان تحديث البيانات فوراً
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: application/json; charset=utf-8');

include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$response = [];

// التحقق من مفتاح التشفير
if (empty($key)) {
    $response['success'] = false;
    $response['message'] = 'خطأ في إعدادات النظام.';
    echo json_encode($response);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['account_id'])) {
    try {
        $account_id = decrypt($_POST['account_id'], $key);

        if (!$account_id || !is_numeric($account_id)) {
            $response['success'] = false;
            $response['message'] = 'معرف الحساب غير صالح.';
            echo json_encode($response);
            exit();
        }

        $username = trim($_POST['username'] ?? '');
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $theme = trim($_POST['theme'] ?? '');
        $password = $_POST['password'] ?? '';

        // التحقق من صحة الثيم
        if (!empty($theme) && !in_array(strtolower($theme), ['light', 'dark'])) {
            $response['success'] = false;
            $response['message'] = 'قيمة الثيم غير صالحة.';
            echo json_encode($response);
            exit();
        }

        // تحويل الثيم إلى أحرف صغيرة للتوحيد
        $theme = strtolower($theme);

    // Get current password and image path
    $stmt = $conn->prepare("SELECT password, img_path FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $stmt->bind_result($current_password, $current_img_path);
    $stmt->fetch();
    $stmt->close();

    // Handle profile image upload - updated to match update_account.php logic
    $img_path = $current_img_path;
    if (isset($_FILES['profile_img']) && $_FILES['profile_img']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/profile_images/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        $file_name = uniqid() . '_' . basename($_FILES['profile_img']['name']);
        $target_file = $upload_dir . $file_name;
        if (move_uploaded_file($_FILES['profile_img']['tmp_name'], $target_file)) {
            $img_path = $target_file;
            if ($current_img_path && file_exists($current_img_path)) {
                unlink($current_img_path);
            }
        } else {
            $response['success'] = false;
            $response['message'] = 'فشل في رفع الصورة.';
            echo json_encode($response);
            exit();
        }
    }

    if (!empty($password)) {
        if (!password_verify($password, $current_password)) {
            $new_password = password_hash($password, PASSWORD_BCRYPT);
        } else {
            $new_password = $current_password;
        }
    } else {
        $new_password = $current_password;
    }

        $stmt = $conn->prepare("UPDATE accounts SET username = ?, password = ?, name = ?, phone = ?, theme = ?, img_path = ? WHERE account_id = ? AND status = 'active'");
        $stmt->bind_param("ssssssi", $username, $new_password, $name, $phone, $theme, $img_path, $account_id);

        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $response['success'] = true;
                $response['message'] = 'تم تعديل البيانات بنجاح.';
                $response['theme'] = $theme; // إرجاع الثيم المحدث

                // تسجيل العملية في السجلات
                error_log("update_profile.php: Profile updated successfully for account " . $account_id . " - Theme: " . $theme);
            } else {
                $response['success'] = false;
                $response['message'] = 'لم يتم العثور على الحساب أو لا توجد تغييرات.';
            }
        } else {
            $response['success'] = false;
            $response['message'] = 'حدث خطأ أثناء تعديل البيانات: ' . $stmt->error;
            error_log("update_profile.php: Database error - " . $stmt->error);
        }
        $stmt->close();

    } catch (Exception $e) {
        $response['success'] = false;
        $response['message'] = 'حدث خطأ في النظام.';
        error_log("update_profile.php: Exception - " . $e->getMessage());
    }
} else {
    $response['success'] = false;
    $response['message'] = 'طلب غير صالح.';
}

echo json_encode($response);
exit();
?>
