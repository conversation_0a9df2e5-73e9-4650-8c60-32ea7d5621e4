<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

// إعداد نظام تتبع العمليات
$operation_log = [];
$operation_start_time = microtime(true);

function log_operation($step, $status, $details = '') {
    global $operation_log;
    $operation_log[] = [
        'step' => $step,
        'status' => $status,
        'details' => $details,
        'timestamp' => date('Y-m-d H:i:s'),
        'execution_time' => round((microtime(true) - $GLOBALS['operation_start_time']) * 1000, 2) . 'ms'
    ];
}

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_POST['store_id'], $key);
$total_amount = $_POST['total_amount']; // Get total amount from the form
$account_id = isset($_POST['account_id']) ? decrypt($_POST['account_id'], $key) : decrypt($_SESSION['account_id'], $key); // Use passed account_id or fallback to session
$invoice_status = $_POST['invoice_status']; // Get invoice status from the form

$response = [];

log_operation('بدء العملية', 'نجح', 'تم بدء عملية حفظ فاتورة الشراء');

// التحقق من صحة البيانات
if (empty($store_id) || empty($account_id) || empty($total_amount)) {
    log_operation('التحقق من البيانات', 'فشل', 'بيانات مفقودة أو غير صحيحة');
    $response['success'] = false;
    $response['message'] = 'بيانات الفاتورة غير مكتملة';
    echo json_encode($response);
    exit;
}

log_operation('التحقق من البيانات', 'نجح', 'تم التحقق من صحة البيانات الأساسية');

$conn->begin_transaction();
log_operation('بدء المعاملة', 'نجح', 'تم بدء معاملة قاعدة البيانات');

try {
    // إنشاء الفاتورة الرئيسية
    $stmt = $conn->prepare("INSERT INTO purchase_invoices (store_id, account_id, total_amount, status) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("iids", $store_id, $account_id, $total_amount, $invoice_status);
    
    if (!$stmt->execute()) {
        throw new Exception('فشل في إنشاء الفاتورة الرئيسية: ' . $stmt->error);
    }
    
    $invoice_id = $stmt->insert_id;
    $stmt->close();
    
    log_operation('إنشاء الفاتورة', 'نجح', "تم إنشاء فاتورة برقم: $invoice_id");

    // جلب تاريخ إنشاء الفاتورة
    $stmt = $conn->prepare("SELECT created_at FROM purchase_invoices WHERE invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    
    if (!$stmt->execute()) {
        throw new Exception('فشل في جلب تاريخ الفاتورة: ' . $stmt->error);
    }
    
    $stmt->bind_result($invoice_date);
    $stmt->fetch();
    $stmt->close();
    
    log_operation('جلب تاريخ الفاتورة', 'نجح', "تاريخ الفاتورة: $invoice_date");

    // معالجة أصناف الفاتورة
    $items_processed = 0;
    $total_items = count($_POST['items']);
    
    log_operation('بدء معالجة الأصناف', 'نجح', "عدد الأصناف المطلوب معالجتها: $total_items");
    
    foreach ($_POST['items'] as $item_id => $item) {
        $quantity = floatval($item['quantity']);
        $cost = isset($item['cost']) ? floatval($item['cost']) : 0;
        $total = $quantity * $cost;

        // التحقق من صحة بيانات الصنف
        if ($quantity <= 0) {
            throw new Exception("كمية غير صحيحة للصنف رقم: $item_id");
        }

        // إدراج بيانات الشراء
        $stmt = $conn->prepare("INSERT INTO purchases (invoice_id, store_id, item_id, quantity, total_amount) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("iiidd", $invoice_id, $store_id, $item_id, $quantity, $total);
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في إدراج بيانات الشراء للصنف رقم: $item_id - " . $stmt->error);
        }
        
        $purchase_id = $stmt->insert_id;
        $stmt->close();
        
        $items_processed++;
        log_operation('إدراج صنف', 'نجح', "تم إدراج الصنف رقم: $item_id، الكمية: $quantity، المبلغ: $total");

        // إذا كانت الفاتورة مؤكدة، تحديث المخزون والمعاملات
        if ($invoice_status === 'confirmed') {
            // إدراج معاملة المخزون
            $stmt = $conn->prepare("INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity, transaction_date) VALUES (?, 'purchase', ?, ?, ?)");
            $stmt->bind_param("iids", $item_id, $purchase_id, $quantity, $invoice_date);
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في إدراج معاملة المخزون للصنف رقم: $item_id - " . $stmt->error);
            }
            $stmt->close();
            
            log_operation('إدراج معاملة مخزون', 'نجح', "تم إدراج معاملة مخزون للصنف رقم: $item_id");

            // تحديث كمية الصنف في المخزون
            $stmt = $conn->prepare("UPDATE items SET quantity = quantity + ? WHERE item_id = ?");
            $stmt->bind_param("di", $quantity, $item_id);
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في تحديث كمية المخزو�� للصنف رقم: $item_id - " . $stmt->error);
            }
            $stmt->close();
            
            log_operation('تحديث المخزون', 'نجح', "تم تحديث كمية المخزون للصنف رقم: $item_id بإضافة: $quantity");
        }
    }
    
    log_operation('انتهاء معالجة الأصناف', 'نجح', "تم معالجة $items_processed من أصل $total_items صنف بنجاح");

    // تسجيل عملية إضافة الفاتورة في سجل النظام
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    
    if (!$stmt->execute()) {
        throw new Exception('فشل في جلب اسم المتجر: ' . $stmt->error);
    }
    
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
    
    log_operation('جلب اسم المتجر', 'نجح', "اسم المتجر: $store_name");

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'add', 'purchase_invoices', ?, ?)";
    $description = "تم إضافة فاتورة شراء جديدة برقم $invoice_id بقيمة $total_amount إلى الفرع $store_name";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
    
    if (!$log_stmt->execute()) {
        throw new Exception('فشل في تسجيل العملية في سجل النظام: ' . $log_stmt->error);
    }
    $log_stmt->close();
    
    log_operation('تسجيل في سجل النظام', 'نجح', 'تم تسجيل العملية في سجل النظام بنجاح');

    // معالجة الصور المرفوعة
    $images_processed = 0;
    if (!empty($_POST['uploaded_images'])) {
        $total_images = count($_POST['uploaded_images']);
        log_operation('بدء معالجة الصور', 'نجح', "عدد الصور المطلوب معالجتها: $total_images");
        
        foreach ($_POST['uploaded_images'] as $relativePath) {
            $stmt = $conn->prepare("INSERT INTO invoice_images (purchase_invoice_id, img_path) VALUES (?, ?)");
            $stmt->bind_param("is", $invoice_id, $relativePath);
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في حفظ الصورة: $relativePath - " . $stmt->error);
            }
            $stmt->close();
            
            $images_processed++;
            log_operation('حفظ صورة', 'نجح', "تم حفظ الصورة: $relativePath");
        }
        
        log_operation('انتهاء معالجة الصور', 'نجح', "تم معالجة $images_processed من أصل $total_images صورة بنجاح");
    }

    // تأكيد المعاملة
    $conn->commit();
    log_operation('تأكيد المعاملة', 'نجح', 'تم تأكيد جميع العمليات في قاعدة البيانات');
    
    $response['success'] = true;
    $response['message'] = 'تم حفظ فاتورة الشراء بنجاح';

    // حذف الملف المؤقت بعد نجاح العملية
    $tempInvoicesDir = __DIR__ . '/temp_purchase_invoices';
    $decrypted_account_id = decrypt($_POST['account_id'], $key);
    $jsonFilePath = $tempInvoicesDir . "/account_{$decrypted_account_id}.json";
    
    if (file_exists($jsonFilePath)) {
        if (unlink($jsonFilePath)) {
            log_operation('حذف الملف المؤقت', 'نجح', 'تم حذف الملف المؤقت بنجاح');
        } else {
            log_operation('حذف الملف المؤقت', 'تحذير', 'فشل في حذف الملف المؤقت');
        }
    }
    
    log_operation('انتهاء العملية', 'نجح', 'تم إنجاز جميع العمليات بنجاح');
} catch (Exception $e) {
    // تسجيل الخطأ
    log_operation('خطأ في العملية', 'فشل', $e->getMessage());
    
    // التراجع عن المعاملة إذا كان الاتصال ما زال نشطاً
    if ($conn->ping()) {
        $conn->rollback();
        log_operation('التراجع عن المعاملة', 'نجح', 'تم التراجع عن جميع العمليات بنجاح');
    }
    
    $response['success'] = false;
    $response['message'] = 'حدث خطأ أثناء حفظ فاتورة الشراء';
}

// إرسال الاستجابة النهائية
header('Content-Type: application/json; charset=utf-8');
echo json_encode($response, JSON_UNESCAPED_UNICODE);

// إغلاق الاتصال
if ($conn->ping()) {
    $conn->close();
}
?>
