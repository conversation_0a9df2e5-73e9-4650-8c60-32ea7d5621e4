<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الباركود المخصص</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>اختبار الباركود المخصص</h1>

        <div class="test-section">
            <h3>شرح نظام الباركود المخصص:</h3>
            <div class="info">
                <p><strong>تركيب الباركود المخصص (13 رقم):</strong></p>
                <ul>
                    <li>الرقم الأول: 2 (يشير إلى أنه باركود مخصص)</li>
                    <li>الأرقام 2-7: باركود الصنف (6 أرقام)</li>
                    <li>الرقم 8: رقم فاصل (يمكن أن يكون أي رقم)</li>
                    <li>الأرقام 9-12: الوزن بالجرام (4 أرقام)</li>
                    <li>الرقم 13: رقم تحقق أو إضافي (لا يستخدم في الحساب)</li>
                </ul>
                <p><strong>مثال:</strong> 2000001002308</p>
                <ul>
                    <li>2: باركود مخصص</li>
                    <li>000001: باركود الصنف</li>
                    <li>0: فاصل</li>
                    <li>0230: الوزن = 230 جرام = 0.230 كيلو</li>
                    <li>8: رقم تحقق (لا يستخدم)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>اختبار معالجة الباركود:</h3>
            <input type="text" id="barcodeInput" class="test-input" placeholder="أدخل الباركود هنا (مثال: 2000001002308)" maxlength="13">
            <button onclick="testBarcode()">اختبار الباركود</button>
            <div id="result"></div>
        </div>

        <div class="test-section">
            <h3>أمثلة للاختبار:</h3>
            <button onclick="testExample('2000001002308')">جبنة تركي - 0.230 كيلو</button>
            <button onclick="testExample('2000002001759')">لحم بقري - 0.175 كيلو</button>
            <button onclick="testExample('2000003007501')">دجاج - 0.750 كيلو</button>
            <button onclick="testExample('123456789')">باركود عادي</button>
            <button onclick="testExample('2000001')">باركود خاطئ</button>
        </div>
    </div>

    <script>
        // دالة معالجة الباركود المخصص (نفس الدالة المستخدمة في النظام)
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            // استخراج باركود الصنف (6 أرقام بعد الرقم 2)
            const itemBarcode = barcode.substring(1, 7);

            // استخراج الوزن بالجرام (4 أرقام من الموضع 8 إلى 11، الرقم 13 ليس جزء من الوزن)
            const weightInGrams = parseInt(barcode.substring(8, 12));
            const quantityInKg = weightInGrams / 1000; // تحويل إلى كيلوجرام

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams
            };
        }

        function testBarcode() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            const resultDiv = document.getElementById('result');

            if (!barcode) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال باركود للاختبار</div>';
                return;
            }

            const customBarcodeData = processCustomBarcode(barcode);

            if (customBarcodeData) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ باركود مخصص صحيح!</h4>
                        <p><strong>الباركود الكامل:</strong> ${barcode}</p>
                        <p><strong>باركود الصنف:</strong> ${customBarcodeData.itemBarcode}</p>
                        <p><strong>الوزن بالجرام:</strong> ${customBarcodeData.weightInGrams}</p>
                        <p><strong>الكمية بالكيلو:</strong> ${customBarcodeData.quantity.toFixed(3)}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>ℹ️ باركود عادي</h4>
                        <p><strong>الباركود:</strong> ${barcode}</p>
                        <p>هذا باركود عادي وسيتم التعامل معه بالطريقة التقليدية (كمية = 1)</p>
                    </div>
                `;
            }
        }

        function testExample(barcode) {
            document.getElementById('barcodeInput').value = barcode;
            testBarcode();
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testExample('2000001002308');
        };

        // اختبار عند الضغط على Enter
        document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testBarcode();
            }
        });
    </script>
</body>

</html>