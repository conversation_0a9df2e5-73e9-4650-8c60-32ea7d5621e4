<?php


include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول لوحدة فواتير الشراء
checkPagePermission('purchase_invoices', 'view');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$store_id = decrypt($_GET['store_id'], $key);

// Fetch store name
$store_sql = "SELECT name FROM stores WHERE store_id = ?";
$store_stmt = $conn->prepare($store_sql);
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_result = $store_stmt->get_result();
$store_name = $store_result->fetch_assoc()['name'];
$store_stmt->close();

$sql = "SELECT pi.invoice_id, pi.status, pi.total_amount, DATE_FORMAT(pi.created_at, '%Y-%m-%d %r') AS created_at, 
               a.name AS account_name
        FROM purchase_invoices pi
        JOIN accounts a ON pi.account_id = a.account_id
        WHERE pi.store_id = ?
        ORDER BY pi.created_at DESC"; // Order by the most recent invoices
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير الشراء لـ <?php echo htmlspecialchars($store_name); ?></title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        /* أنماط خاصة بنظام الصلاحيات */
        .invoice-number {
            cursor: pointer;
            color: var(--color-primary);
            text-decoration: underline;
            font-weight: bold;
        }
        
        .invoice-number:hover {
            color: var(--color-primary-dark);
        }
        
        .invoice-number-disabled {
            cursor: default;
            color: var(--text-color);
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .status-frame.pending {
            cursor: pointer;
        }
        
        .status-frame.pending:hover {
            background-color: var(--warning-color);
            color: white;
        }
        
        .status-frame.confirmed {
            cursor: default;
        }
        
        /* تحسين مظهر الأزرار */
        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }
        
        .add-btn i {
            font-size: 14px;
        }
        
        /* رسائل عدم وجود صلاحيات */
        .no-permission-message {
            background: var(--error-bg);
            color: var(--error-color);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
            border: 1px solid var(--error-color);
        }
        
        .no-permission-message i {
            margin-left: 8px;
            font-size: 18px;
        }
        
        /* تحسين مظهر الجدول */
        .table-responsive {
            margin-top: 20px;
        }
        
        /* تحسين الوضع المظلم */
        [data-theme="dark"] .invoice-number {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .invoice-number:hover {
            color: var(--color-primary-light);
        }
        
        [data-theme="dark"] .no-permission-message {
            background: rgba(244, 67, 54, 0.1);
            border-color: rgba(244, 67, 54, 0.3);
            color: #f44336;
        }
    /* تحسين مظهر التقارير */
        .swal-rtl {
            direction: rtl;
            text-align: right;
        }
        
        .swal-title-custom {
            font-family: 'Cairo', sans-serif !important;
            color: #007bff !important;
        }
        
        /* تحسين الأنماط للوضع المظلم في التقارير */
        [data-theme="dark"] .swal2-popup {
            background-color: #0d1117 !important;
            color: #c9d1d9 !important;
            border: 1px solid #30363d !important;
        }
        
        [data-theme="dark"] .swal2-title {
            color: var(--color-primary) !important;
        }
        
        [data-theme="dark"] .swal2-html-container {
            color: #c9d1d9 !important;
        }
        
        /* تحسين العناصر داخل النافذة للوضع المظلم */
        [data-theme="dark"] .swal2-popup .swal2-html-container div[style*="background: linear-gradient(135deg, #f8f9fa"] {
            background: linear-gradient(135deg, #21262d 0%, #161b22 100%) !important;
            border-left-color: var(--color-primary) !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container div[style*="background: linear-gradient(135deg, #d4edda"] {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%) !important;
            border-left-color: #28a745 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container div[style*="background: linear-gradient(135deg, #fff3cd"] {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%) !important;
            border-left-color: #ffc107 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #495057"] {
            color: #8b949e !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #007bff"] {
            color: var(--color-primary) !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #28a745"] {
            color: #3fb950 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #155724"] {
            color: #3fb950 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #856404"] {
            color: #d29922 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container span[style*="color: #ffc107"] {
            color: #f7cc47 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container h4[style*="color: #007bff"] {
            color: var(--color-primary) !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container h4[style*="color: #155724"] {
            color: #3fb950 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container h4[style*="color: #856404"] {
            color: #d29922 !important;
        }
        
        [data-theme="dark"] .swal2-popup .swal2-html-container div[style*="border-bottom: 1px solid #dee2e6"] {
            border-bottom-color: #30363d !important;
        }
        
        /* تحسين الأزرار في الوضع المظلم */
        [data-theme="dark"] .swal2-confirm {
            background-color: var(--color-primary) !important;
            border-color: var(--color-primary) !important;
        }
        
        [data-theme="dark"] .swal2-confirm:hover {
            background-color: var(--color-primary-dark) !important;
            border-color: var(--color-primary-dark) !important;
        }
        
        /* تحسين الخطوط في الوضع المظلم */
        [data-theme="dark"] .swal2-popup * {
            font-family: 'Cairo', sans-serif !important;
        }
        
        /* ضمان عمل أيقونات Font Awesome في SweetAlert2 */
        .swal2-popup .fas,
        .swal2-popup .far,
        .swal2-popup .fab {
            font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
            font-weight: 900 !important;
            display: inline-block !important;
        }
        
        .swal2-title .fas {
            margin-left: 8px;
        }
        
        /* تحسين مظهر صف الإجمالي العام */
        .total-row {
            background-color: rgba(0, 123, 255, 0.1) !important;
            font-weight: bold !important;
            border-top: 2px solid var(--color-primary) !important;
        }
        
        [data-theme="dark"] .total-row {
            background-color: rgba(74, 144, 226, 0.15) !important;
            border-top-color: var(--color-primary) !important;
        }
        
        /* تحسين مظهر الجدول في نافذة تفاصيل الفاتورة */
        #invoiceItemsModal table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        #invoiceItemsModal table th,
        #invoiceItemsModal table td {
            padding: 10px;
            text-align: center;
            border: 1px solid var(--border-color);
        }
        
        #invoiceItemsModal table th {
            background-color: var(--color-primary);
            color: white;
            font-weight: bold;
        }
        
        [data-theme="dark"] #invoiceItemsModal table th {
            background-color: var(--color-primary);
        }
        
        [data-theme="dark"] #invoiceItemsModal table td {
            border-color: #30363d;
        }
        
        /* أنماط نافذة عرض الصور المتقدمة */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: transparent;
            pointer-events: auto;
        }
        
        .image-modal.active {
            display: block;
        }
        
        .image-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-color);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            min-width: 400px;
            min-height: 300px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            border: 1px solid var(--border-color);
            cursor: move;
            resize: both;
            pointer-events: all;
            will-change: transform;
        }
        
        .image-modal-header {
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            user-select: none;
        }
        
        .image-modal-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        .image-controls {
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .control-btn:active {
            transform: scale(0.95);
        }
        
        .image-container {
            padding: 20px;
            background: var(--bg-secondary);
            height: calc(100% - 60px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: auto;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            cursor: grab;
            user-select: none;
        }
        
        .image-container img:active {
            cursor: grabbing;
        }
        
        .image-resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30px;
            height: 30px;
            background: var(--color-primary);
            cursor: se-resize;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            border-top-left-radius: 8px;
        }
        
        .image-resize-handle:hover {
            background: var(--color-primary-dark);
        }
        
        /* الوضع المظلم لنافذة الصور */
        [data-theme="dark"] .image-modal-content {
            background: #161b22;
            border-color: #30363d;
        }
        
        [data-theme="dark"] .image-modal-header {
            background: var(--color-primary);
        }
        
        [data-theme="dark"] .image-container {
            background: #0d1117;
        }
        
        /* تحسين الاستجابية للهواتف */
        @media (max-width: 768px) {
            .image-modal-content {
                min-width: 90vw;
                min-height: 60vh;
                max-width: 95vw;
                max-height: 85vh;
            }
            
            .image-modal-header {
                padding: 10px 15px;
            }
            
            .image-modal-header h3 {
                font-size: 14px;
            }
            
            .control-btn {
                padding: 4px 8px;
                font-size: 11px;
            }
            
            .image-container {
                padding: 15px;
            }
        }
        
        /* تأثيرات انتقالية */
        .image-modal {
            animation: fadeIn 0.3s ease;
        }
        
        .image-modal-content {
            animation: slideIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to { 
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }
        
        /* تحسين مظهر شريط التمرير */
        .image-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .image-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 10px;
        }
        
        .image-container::-webkit-scrollbar-thumb {
            background: var(--color-primary);
            border-radius: 10px;
        }
        
        .image-container::-webkit-scrollbar-thumb:hover {
            background: var(--color-primary-dark);
        }
        
        /* تحسين التفاعل */
        .image-modal-content:hover {
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }
        
        /* تحسين مؤشر التحميل */
        .image-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .image-loading::before {
            content: "⏳";
            margin-left: 8px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        

    </style>
</head>
<body>
<?php include 'sidebar.php'; ?>
<div class="container">
    <h2>فواتير الشراء لـ <?php echo htmlspecialchars($store_name); ?></h2>
    
    <!-- أزرار العمليات مع فحص الصلاحيات -->
    <?php if (hasPermission('purchase_invoices', 'add_purchase_invoice')): ?>
        <button class="add-btn" onclick="window.location.href='add_purchase_invoice.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>&account_id=<?php echo urlencode($_SESSION['account_id']); ?>'" title="إضافة فاتورة شراء جديدة">
            <i class="fas fa-plus"></i> إضافة فاتورة جديدة
        </button>
    <?php endif; ?>
    
    <?php if (hasPermission('purchase_invoices', 'export_excel')): ?>
        <button class="add-btn" onclick="window.location.href='export_purchase_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>'" title="تصدير البيانات إلى Excel">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </button>
    <?php endif; ?>
    
    <?php if (hasPermission('purchase_invoices', 'comprehensive_report')): ?>
        <button class="add-btn" onclick="showReport()" title="عرض التقرير الشامل">
            <i class="fas fa-chart-pie"></i> تقرير
        </button>
    <?php endif; ?>

    <input type="text" id="item-search-input" class="search-bar" placeholder="ابحث عن صنف...">

    <?php
    // عرض رسالة توضيحية إذا لم يكن لدى المستخدم أي صلاحيات للعمليات
    $has_any_action_permission = hasPermission('purchase_invoices', 'add_purchase_invoice') || 
                                  hasPermission('purchase_invoices', 'edit_purchase_invoice') || 
                                  hasPermission('purchase_invoices', 'delete_purchase_invoice') || 
                                  hasPermission('purchase_invoices', 'export_excel') || 
                                  hasPermission('purchase_invoices', 'comprehensive_report');
    
    if (!$has_any_action_permission): ?>
        <div class="no-permission-message">
            <i class="fas fa-info-circle"></i>
            <strong>ملاحظة:</strong> لديك صلاحية عرض الفواتير فقط. للقيام بعمليات أخرى، يرجى التواصل مع المدير لمنحك الصلاحيات المطلوبة.
        </div>
    <?php endif; ?>

    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>الشخص</th>
                    <th>الحالة</th>
                    <th>إجمالي السعر</th>
                    <th>المدفوع</th>
                    <th>الباقي</th>
                    <th>توقيت الفاتورة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="invoice-table-body">
                <?php
                
                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $encrypted_invoice_id = encrypt($row['invoice_id'], $key);
                        $encrypted_store_id = encrypt($store_id, $key);
                        $status = strtolower(trim($row['status'])); // Normalize value
                        $status_class = $status === 'confirmed' ? 'confirmed' : 'pending';
                        $status_text = $status === 'confirmed' ? 'مؤكد' : 'معلق';
                        // فحص صلاحية تأكيد الفاتورة
                        $can_confirm = hasPermission('purchase_invoices', 'confirm_invoice');
                        $status_onclick = ($status === 'pending' && $can_confirm) ? "onclick='changeInvoiceStatus(\"{$encrypted_invoice_id}\")'" : '';
                        
                        // فحص صلاحية عرض تفاصيل الفاتورة
                        $can_view_items = hasPermission('purchase_invoices', 'view_invoice_items');
                        $invoice_number_onclick = $can_view_items ? "onclick='showInvoiceItems(\"{$encrypted_invoice_id}\")'" : '';
                        $invoice_number_class = $can_view_items ? 'invoice-number' : 'invoice-number-disabled';
                        
                        echo "<tr data-invoice-id='{$row['invoice_id']}'>
                                <td><span class='{$invoice_number_class}' {$invoice_number_onclick}>{$row['invoice_id']}</span></td>
                                <td>{$row['account_name']}</td>
                                <td><span class='status-frame {$status_class}' {$status_onclick}>{$status_text}</span></td>
                                <td>{$row['total_amount']}</td>
                                <td>{$row['total_amount']}</td>
                                <td>0.00</td>
                                <td>{$row['created_at']}</td>
                                <td>
                                    <div class='action-buttons'>";
                        
                        // زر التعديل - فحص صلاحية التعديل
                        if (hasPermission('purchase_invoices', 'edit_purchase_invoice')) {
                            echo "<button class='action-btn' onclick='window.location.href=\"edit_purchase_invoice.php?store_id={$encrypted_store_id}&invoice_id={$encrypted_invoice_id}\"' title='تعديل الفاتورة'>
                                    <i class='fas fa-edit'></i>
                                  </button>";
                        }
                        
                        // زر الحذف - فح�� صلاحية الحذف
                        if (hasPermission('purchase_invoices', 'delete_purchase_invoice')) {
                            echo "<button class='action-btn' onclick='deleteInvoice(\"{$encrypted_invoice_id}\")' title='حذف الفاتورة'>
                                    <i class='fas fa-trash-alt'></i>
                                  </button>";
                        }
                        
                        echo "    </div>
                                </td>
                              </tr>";

                        // Fetch and display items for this invoice
                        $stmt_items = $conn->prepare("SELECT i.name, p.quantity, i.cost FROM purchases p JOIN items i ON p.item_id = i.item_id WHERE p.invoice_id = ?");
                        $stmt_items->bind_param("i", $row['invoice_id']);
                        $stmt_items->execute();
                        $items_result = $stmt_items->get_result();
                        $stmt_items->close();

                        if ($items_result->num_rows > 0) {
                            echo "<tr class='invoice-items' data-invoice-id='{$row['invoice_id']}' style='display: none;'>
                                    <td colspan='8'>
                                        <table class='table'>
                                            <thead>
                                                <tr>
                                                    <th>اسم الصنف</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>إجمالي السعر</th> <!-- New column -->
                                                </tr>
                                            </thead>
                                            <tbody>";
                            while ($item = $items_result->fetch_assoc()) {
                                $total_price = $item['quantity'] * $item['cost']; // Calculate total price
                                echo "<tr>
                                        <td>{$item['name']}</td>
                                        <td>{$item['quantity']}</td>
                                        <td>{$item['cost']}</td>
                                        <td>{$total_price}</td> <!-- Display total price -->
                                      </tr>";
                            }
                            echo "      </tbody>
                                        </table>
                                    </td>
                                  </tr>";
                        }
                    }
                } else {
                    echo "<tr><td colspan='8'>لا توجد فواتير حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
</div>

<div id="invoiceItemsModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>تفاصيل الفاتورة</h2>
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody id="invoiceItemsTableBody">
                <!-- Invoice items will be populated here -->
            </tbody>
        </table>
    </div>
</div>

<!-- نافذة عرض الصور المتقدمة -->
<div id="imageViewerModal" class="image-modal">
    <div class="image-modal-content" id="imageModalContent">
        <div class="image-modal-header" id="imageModalHeader">
            <h3>عرض الصورة</h3>
            <div class="image-controls">
                <button class="control-btn" onclick="zoomIn()" title="تكبير">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="control-btn" onclick="zoomOut()" title="تصغير">
                    <i class="fas fa-search-minus"></i>
                </button>
                <button class="control-btn" onclick="toggleFullscreen()" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="control-btn" onclick="resetImageSize()" title="إعادة تعيين الحجم">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="control-btn" onclick="closeImageModal()" title="إغلاق">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="image-container" id="imageContainer">
            <img id="modalImage" src="" alt="صورة الفاتورة">
        </div>
        <div class="image-resize-handle" id="resizeHandle">
            <i class="fas fa-expand-arrows-alt"></i>
        </div>
    </div>
</div>


<script>
    // متغيرات الصلاحيات للاستخدام في JavaScript
    const permissions = {
        view: <?php echo hasPermission('purchase_invoices', 'view') ? 'true' : 'false'; ?>,
        add_purchase_invoice: <?php echo hasPermission('purchase_invoices', 'add_purchase_invoice') ? 'true' : 'false'; ?>,
        edit_purchase_invoice: <?php echo hasPermission('purchase_invoices', 'edit_purchase_invoice') ? 'true' : 'false'; ?>,
        delete_purchase_invoice: <?php echo hasPermission('purchase_invoices', 'delete_purchase_invoice') ? 'true' : 'false'; ?>,
        view_invoice_items: <?php echo hasPermission('purchase_invoices', 'view_invoice_items') ? 'true' : 'false'; ?>,
        comprehensive_report: <?php echo hasPermission('purchase_invoices', 'comprehensive_report') ? 'true' : 'false'; ?>,
        export_excel: <?php echo hasPermission('purchase_invoices', 'export_excel') ? 'true' : 'false'; ?>,
        confirm_invoice: <?php echo hasPermission('purchase_invoices', 'confirm_invoice') ? 'true' : 'false'; ?>
    };

    function showInvoiceItems(encryptedInvoiceId) {
        // فحص صلاحية عرض تفاصيل الفاتورة
        if (!permissions.view_invoice_items) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لعرض تفاصيل الفاتورة',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        const formData = new FormData();
        formData.append('encrypted_invoice_id', encryptedInvoiceId);

        fetch('fetch_invoice_items.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tableBody = document.getElementById('invoiceItemsTableBody');
                tableBody.innerHTML = '';
                let grandTotal = 0;
                data.items.forEach(item => {
                    const row = document.createElement('tr');
                    const total = (parseFloat(item.quantity) * parseFloat(item.price)).toFixed(2);
                    grandTotal += parseFloat(total);
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.price}</td>
                        <td>${total}</td>
                    `;
                    tableBody.appendChild(row);
                });
                
                // إضافة صف الإجمالي العام
                const totalRow = document.createElement('tr');
                totalRow.className = 'total-row';
                totalRow.innerHTML = `
                    <td colspan="3" style="text-align: center;">الإجمالي العام</td>
                    <td>${grandTotal.toFixed(2)}</td>
                `;
                tableBody.appendChild(totalRow);

                // Display invoice images
                const modalContent = document.querySelector('#invoiceItemsModal .modal-content');
                const oldImagesContainer = modalContent.querySelector('.invoice-images');
                if (oldImagesContainer) {
                    oldImagesContainer.remove();
                }
                const imagesContainer = document.createElement('div');
                imagesContainer.classList.add('invoice-images');
                imagesContainer.style.marginTop = '20px';
                imagesContainer.innerHTML = '<h3>صور الفاتورة:</h3>';
                data.images.forEach(imagePath => {
                    const img = document.createElement('img');
                    img.src = imagePath;
                    img.style.width = '100px';
                    img.style.marginBottom = '10px';
                    img.style.cursor = 'pointer';
                    img.onclick = () => openImageModal(imagePath);
                    imagesContainer.appendChild(img);
                });
                modalContent.appendChild(imagesContainer);

                document.getElementById('invoiceItemsModal').classList.add("active"); // Use "active" class for showing
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في جلب تفاصيل الفاتورة',
                    text: 'حدث خطأ أثناء جلب تفاصيل الفاتورة. حاول مرة أخرى.'
                });
            }
        })
        .catch(error => {
            console.error("Error fetching invoice items:", error);
        });
    }

    // متغيرات للتحكم في النافذة
    let imageModal = {
        isDragging: false,
        isResizing: false,
        startX: 0,
        startY: 0,
        startWidth: 0,
        startHeight: 0,
        startLeft: 0,
        startTop: 0,
        listenersAdded: false,
        currentScale: 1
    };
    
    function openImageModal(imagePath) {
        const modal = document.getElementById('imageViewerModal');
        const modalImage = document.getElementById('modalImage');
        const modalContent = document.getElementById('imageModalContent');
        
        // تعيين مسار الصورة
        modalImage.src = imagePath;
        modalImage.onload = function() {
            resetImageModal();
        };
        
        // عرض النافذة
        modal.classList.add('active');
        
        // تهيئة خصائص السحب (مرة واحدة فقط)
        if (!imageModal.listenersAdded) {
            setupImageModalListeners();
            imageModal.listenersAdded = true;
        }
        
        // إعادة تعيين حالة النافذة
        imageModal.isDragging = false;
        imageModal.isResizing = false;
    }
    
    function resetImageModal() {
        const modalContent = document.getElementById('imageModalContent');
        const modalImage = document.getElementById('modalImage');
        
        // إعادة تعيين حجم وموقع النافذة
        modalContent.style.width = '600px';
        modalContent.style.height = '500px';
        modalContent.style.top = '50%';
        modalContent.style.left = '50%';
        modalContent.style.transform = 'translate(-50%, -50%)';
        modalContent.style.position = 'absolute';
        
        // إعادة تعيين الصورة
        modalImage.style.width = 'auto';
        modalImage.style.height = 'auto';
        modalImage.style.maxWidth = '100%';
        modalImage.style.maxHeight = '100%';
        modalImage.style.transform = 'scale(1)';
        
        imageModal.currentScale = 1;
    }
    
    function closeImageModal() {
        const modal = document.getElementById('imageViewerModal');
        modal.classList.remove('active');
        resetImageModal();
    }
    
    function toggleFullscreen() {
        const modalContent = document.getElementById('imageModalContent');
        
        if (modalContent.style.width === '95vw' || modalContent.style.width === '100vw') {
            // العودة للحجم العادي
            resetImageModal();
        } else {
            // ملء الشاشة
            modalContent.style.width = '95vw';
            modalContent.style.height = '95vh';
            modalContent.style.top = '2.5vh';
            modalContent.style.left = '2.5vw';
            modalContent.style.transform = 'none';
            modalContent.style.position = 'fixed';
        }
    }
    
    function resetImageSize() {
        resetImageModal();
    }
    
    function zoomIn() {
        const img = document.getElementById('modalImage');
        imageModal.currentScale *= 1.2;
        
        // حد أقصى للتكبير
        if (imageModal.currentScale > 5) {
            imageModal.currentScale = 5;
        }
        
        img.style.transform = `scale(${imageModal.currentScale})`;
        img.style.maxWidth = 'none';
        img.style.maxHeight = 'none';
    }
    
    function zoomOut() {
        const img = document.getElementById('modalImage');
        imageModal.currentScale *= 0.8;
        
        // حد أدنى للتصغير
        if (imageModal.currentScale < 0.2) {
            imageModal.currentScale = 0.2;
        }
        
        img.style.transform = `scale(${imageModal.currentScale})`;
        
        // إذا وصلنا للحجم الطبيعي، أعد تعيين الأنماط
        if (imageModal.currentScale <= 1) {
            imageModal.currentScale = 1;
            img.style.transform = 'scale(1)';
            img.style.maxWidth = '100%';
            img.style.maxHeight = '100%';
        }
    }
    
    function setupImageModalListeners() {
        const modal = document.getElementById('imageViewerModal');
        const modalContent = document.getElementById('imageModalContent');
        const header = document.getElementById('imageModalHeader');
        const resizeHandle = document.getElementById('resizeHandle');
        const imageContainer = document.getElementById('imageContainer');
        
        // إغلاق النافذة عند الضغط خارجها
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeImageModal();
            }
        });
        
        // دعم اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (modal.classList.contains('active')) {
                switch(e.key) {
                    case 'Escape':
                        closeImageModal();
                        break;
                    case 'F11':
                        e.preventDefault();
                        toggleFullscreen();
                        break;
                    case '=':
                    case '+':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            zoomIn();
                        }
                        break;
                    case '-':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            zoomOut();
                        }
                        break;
                    case '0':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            resetImageSize();
                        }
                        break;
                }
            }
        });
        
        // دعم التكبير والتصغير بعجلة الماوس
        imageContainer.addEventListener('wheel', function(e) {
            if (modal.classList.contains('active')) {
                e.preventDefault();
                if (e.deltaY > 0) {
                    zoomOut();
                } else {
                    zoomIn();
                }
            }
        });
        
        // السحب للماوس
        header.addEventListener('mousedown', startImageDrag);
        document.addEventListener('mousemove', dragImage);
        document.addEventListener('mouseup', stopImageDrag);
        
        // تغيير الحجم للماوس
        resizeHandle.addEventListener('mousedown', startImageResize);
        document.addEventListener('mousemove', resizeImage);
        document.addEventListener('mouseup', stopImageResize);
        
        // دعم اللمس للأجهزة المحمولة
        header.addEventListener('touchstart', startImageDragTouch, { passive: false });
        document.addEventListener('touchmove', dragImageTouch, { passive: false });
        document.addEventListener('touchend', stopImageDrag);
        
        resizeHandle.addEventListener('touchstart', startImageResizeTouch, { passive: false });
        document.addEventListener('touchmove', resizeImageTouch, { passive: false });
        document.addEventListener('touchend', stopImageResize);
        
        // منع انتشار أحداث أزرار التحكم
        const controlButtons = modal.querySelectorAll('.control-btn');
        controlButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    }
    
    // دوال السحب
    function startImageDrag(e) {
        if (e.target.closest('.image-controls')) return;
        
        imageModal.isDragging = true;
        const modalContent = document.getElementById('imageModalContent');
        const rect = modalContent.getBoundingClientRect();
        
        imageModal.startX = e.clientX - rect.left;
        imageModal.startY = e.clientY - rect.top;
        
        modalContent.style.transform = 'none';
        modalContent.style.cursor = 'grabbing';
        e.preventDefault();
    }
    
    function dragImage(e) {
        if (!imageModal.isDragging) return;
        
        e.preventDefault();
        const modalContent = document.getElementById('imageModalContent');
        
        let newX = e.clientX - imageModal.startX;
        let newY = e.clientY - imageModal.startY;
        
        // حدود الشاشة
        const maxX = window.innerWidth - modalContent.offsetWidth;
        const maxY = window.innerHeight - modalContent.offsetHeight;
        
        newX = Math.max(0, Math.min(newX, maxX));
        newY = Math.max(0, Math.min(newY, maxY));
        
        modalContent.style.left = newX + 'px';
        modalContent.style.top = newY + 'px';
        modalContent.style.position = 'fixed';
    }
    
    function stopImageDrag() {
        if (imageModal.isDragging) {
            imageModal.isDragging = false;
            const modalContent = document.getElementById('imageModalContent');
            modalContent.style.cursor = 'move';
        }
    }
    
    // دوال تغيير الحجم
    function startImageResize(e) {
        imageModal.isResizing = true;
        const modalContent = document.getElementById('imageModalContent');
        
        imageModal.startX = e.clientX;
        imageModal.startY = e.clientY;
        imageModal.startWidth = modalContent.offsetWidth;
        imageModal.startHeight = modalContent.offsetHeight;
        
        e.preventDefault();
        e.stopPropagation();
    }
    
    function resizeImage(e) {
        if (!imageModal.isResizing) return;
        
        e.preventDefault();
        const modalContent = document.getElementById('imageModalContent');
        
        const deltaX = e.clientX - imageModal.startX;
        const deltaY = e.clientY - imageModal.startY;
        
        let newWidth = imageModal.startWidth + deltaX;
        let newHeight = imageModal.startHeight + deltaY;
        
        // حدود الحجم
        const minWidth = 400;
        const minHeight = 300;
        const maxWidth = window.innerWidth * 0.95;
        const maxHeight = window.innerHeight * 0.95;
        
        newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
        newHeight = Math.max(minHeight, Math.min(newHeight, maxHeight));
        
        modalContent.style.width = newWidth + 'px';
        modalContent.style.height = newHeight + 'px';
    }
    
    function stopImageResize() {
        imageModal.isResizing = false;
    }
    
    // دوال دعم اللمس للأجهزة المحمولة
    function startImageDragTouch(e) {
        if (e.target.closest('.image-controls')) return;
        
        imageModal.isDragging = true;
        const modalContent = document.getElementById('imageModalContent');
        
        const touch = e.touches[0];
        const rect = modalContent.getBoundingClientRect();
        imageModal.startX = touch.clientX - rect.left;
        imageModal.startY = touch.clientY - rect.top;
        
        modalContent.style.transform = 'none';
        e.preventDefault();
    }
    
    function dragImageTouch(e) {
        if (!imageModal.isDragging) return;
        
        e.preventDefault();
        const modalContent = document.getElementById('imageModalContent');
        
        const touch = e.touches[0];
        let newX = touch.clientX - imageModal.startX;
        let newY = touch.clientY - imageModal.startY;
        
        const maxX = window.innerWidth - modalContent.offsetWidth;
        const maxY = window.innerHeight - modalContent.offsetHeight;
        
        newX = Math.max(0, Math.min(newX, maxX));
        newY = Math.max(0, Math.min(newY, maxY));
        
        modalContent.style.left = newX + 'px';
        modalContent.style.top = newY + 'px';
        modalContent.style.position = 'fixed';
    }
    
    function startImageResizeTouch(e) {
        imageModal.isResizing = true;
        const modalContent = document.getElementById('imageModalContent');
        
        const touch = e.touches[0];
        imageModal.startX = touch.clientX;
        imageModal.startY = touch.clientY;
        imageModal.startWidth = modalContent.offsetWidth;
        imageModal.startHeight = modalContent.offsetHeight;
        
        e.preventDefault();
        e.stopPropagation();
    }
    
    function resizeImageTouch(e) {
        if (!imageModal.isResizing) return;
        
        e.preventDefault();
        const modalContent = document.getElementById('imageModalContent');
        
        const touch = e.touches[0];
        const deltaX = touch.clientX - imageModal.startX;
        const deltaY = touch.clientY - imageModal.startY;
        
        let newWidth = imageModal.startWidth + deltaX;
        let newHeight = imageModal.startHeight + deltaY;
        
        // حجم أصغر للأجهزة المحمولة
        const minWidth = 300;
        const minHeight = 250;
        const maxWidth = window.innerWidth * 0.95;
        const maxHeight = window.innerHeight * 0.95;
        
        newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
        newHeight = Math.max(minHeight, Math.min(newHeight, maxHeight));
        
        modalContent.style.width = newWidth + 'px';
        modalContent.style.height = newHeight + 'px';
    }

    document.querySelectorAll('.close').forEach(span => {
        span.onclick = function() {
            // Identify the parent modal
            const modal = span.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        };
    });

    // Unified handler for closing modals when clicking outside
    window.addEventListener('click', event => {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('active');
        }
    });

    function changeInvoiceStatus(encryptedInvoiceId) {
        // فحص صلاحية تأكيد الفاتورة
        if (!permissions.confirm_invoice) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتأكيد الفواتير',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "سيتم تغيير حالة الفاتورة إلى مؤكد!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، تأكيد',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('encrypted_invoice_id', encryptedInvoiceId);

                fetch('change_invoice_status.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم تغيير حالة الفاتورة بنجاح',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            location.reload(); // Refresh the page after success
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message || 'حدث خطأ أثناء تغيير حالة الفاتورة.'
                        });
                    }
                })
                .catch(error => {
                    console.error("Error changing invoice status:", error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تغيير حالة الفاتورة.'
                    });
                });
            }
        });
    }

    function deleteInvoice(encryptedInvoiceId) {
        // فحص صلاحية حذف الفاتورة
        if (!permissions.delete_purchase_invoice) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لحذف الفواتير',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('encrypted_invoice_id', encryptedInvoiceId);

                fetch('delete_invoice.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم حذف الفاتورة بنجاح',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            location.reload(); // Refresh the page after the success message
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: 'حدث خطأ أثناء حذف الفاتورة.'
                        });
                    }
                })
                .catch(error => {
                    console.error("Error deleting invoice:", error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء حذف الفاتورة.'
                    });
                });
            }
        });
    }

    function showPopupMessage(message, type, duration = 3000) {
        Swal.fire({
            icon: type,
            title: message,
            showConfirmButton: false,
            timer: duration
        });
    }

    <?php if (isset($_SESSION['message'])): ?>
        showPopupMessage('<?php echo $_SESSION['message']; ?>', '<?php echo $_SESSION['message_type']; ?>');
        <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
    <?php endif; ?>

    document.getElementById('item-search-input').addEventListener('input', function () {
        const query = this.value.toLowerCase();
        const rows = document.querySelectorAll('#invoice-table-body tr[data-invoice-id]');
        rows.forEach(row => {
            const invoiceId = row.getAttribute('data-invoice-id');
            const itemRows = document.querySelectorAll(`.invoice-items[data-invoice-id="${invoiceId}"] tbody tr`);
            let matchFound = false;

            itemRows.forEach(itemRow => {
                const itemName = itemRow.cells[0].textContent.toLowerCase();
                if (itemName.includes(query)) {
                    matchFound = true;
                    itemRow.style.display = '';
                } else {
                    itemRow.style.display = 'none';
                }
            });

            if (query === '') {
                // Reset all rows if the search bar is cleared
                itemRows.forEach(itemRow => itemRow.style.display = '');
                row.style.display = '';
                const invoiceItemsRow = document.querySelector(`.invoice-items[data-invoice-id="${invoiceId}"]`);
                if (invoiceItemsRow) {
                    invoiceItemsRow.style.display = 'none';
                }
            } else {
                row.style.display = matchFound ? '' : 'none';
                const invoiceItemsRow = document.querySelector(`.invoice-items[data-invoice-id="${invoiceId}"]`);
                if (invoiceItemsRow) {
                    invoiceItemsRow.style.display = matchFound ? '' : 'none';
                }
            }
        });
    });

    function showReport() {
        // فحص صلاحية عرض التقارير الشاملة
        if (!permissions.comprehensive_report) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لعرض التقارير الشاملة',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        
        fetch('get_invoice_report.php?store_id=<?php echo urlencode($encrypted_store_id); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '<i class="fas fa-chart-pie" style="color: #007bff;"></i> تقرير فواتير الشراء',
                        html: `
                            <div style="text-align: right; line-height: 2; font-family: 'Cairo', sans-serif;">
                                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #007bff;">
                                    <h4 style="color: #007bff; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                        <i class="fas fa-chart-line" style="color: #007bff;"></i> إحصائيات عامة
                                    </h4>
                                    <div style="display: grid; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                            <span style="font-weight: 600; color: #495057;">إجمالي عدد الفواتير:</span>
                                            <span style="font-weight: bold; color: #007bff; font-size: 1.1em;">${data.total_invoices} فاتورة</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                            <span style="font-weight: 600; color: #495057;">إجمالي قيمة الفواتير:</span>
                                            <span style="font-weight: bold; color: #007bff; font-size: 1.1em;">${data.total_price} جنيه</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                                    <h4 style="color: #155724; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                        <i class="fas fa-check-circle" style="color: #28a745;"></i> الفواتير المؤكدة
                                    </h4>
                                    <div style="display: grid; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                            <span style="font-weight: 600; color: #155724;">العدد:</span>
                                            <span style="font-weight: bold; color: #28a745; font-size: 1.1em;">${data.confirmed_count} فاتورة</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                            <span style="font-weight: 600; color: #155724;">الإجمالي:</span>
                                            <span style="font-weight: bold; color: #28a745; font-size: 1.1em;">${data.confirmed_total} جنيه</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 20px; border-radius: 12px; border-left: 4px solid #ffc107;">
                                    <h4 style="color: #856404; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                        <i class="fas fa-clock" style="color: #ffc107;"></i> الفواتير المعلقة
                                    </h4>
                                    <div style="display: grid; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                            <span style="font-weight: 600; color: #856404;">العدد:</span>
                                            <span style="font-weight: bold; color: #ffc107; font-size: 1.1em;">${data.pending_count} فاتورة</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                            <span style="font-weight: 600; color: #856404;">الإجمالي:</span>
                                            <span style="font-weight: bold; color: #ffc107; font-size: 1.1em;">${data.pending_total} جنيه</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `,
                        icon: null,
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#007bff',
                        width: '700px',
                        customClass: {
                            popup: 'swal-rtl',
                            title: 'swal-title-custom'
                        },
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء جلب التقرير.'
                    });
                }
            })
            .catch(error => {
                console.error("Error fetching report:", error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب التقرير.'
                });
            });
    }
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();

?>
