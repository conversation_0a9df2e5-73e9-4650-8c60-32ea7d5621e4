<?php

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';



$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_GET['store_id'], $key);
$invoice_id = decrypt($_GET['invoice_id'], $key);
$account_id = decrypt($_SESSION['account_id'], $key);

$sql_categories = "SELECT * FROM categories WHERE store_id = ?";
$stmt = $conn->prepare($sql_categories);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$categories_result = $stmt->get_result();
$stmt->close();

// Get account_id for favorites
$decrypted_account_id = decrypt($_SESSION['account_id'], $key);

if ($decrypted_account_id) {
    $sql_items = "SELECT i.*, c.name AS category_name,
                  (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite,
                  (SELECT COUNT(*) FROM item_images WHERE item_id = i.item_id) AS image_count
                  FROM items i 
                  JOIN categories c ON i.category_id = c.category_id 
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("iii", $decrypted_account_id, $store_id, $decrypted_account_id);
} else {
    $sql_items = "SELECT i.*, c.name AS category_name, 0 as is_favorite, 0 as image_count
                  FROM items i 
                  JOIN categories c ON i.category_id = c.category_id 
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("i", $store_id);
}

$stmt->execute();
$items_result = $stmt->get_result();
$stmt->close();

$sql_invoice = "SELECT * FROM purchase_invoices WHERE invoice_id = ?";
$stmt = $conn->prepare($sql_invoice);
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$invoice_result = $stmt->get_result()->fetch_assoc();
$stmt->close();

$sql_purchases = "SELECT p.*, i.name AS item_name, i.barcode, i.cost FROM purchases p JOIN items i ON p.item_id = i.item_id WHERE p.invoice_id = ?";
$stmt = $conn->prepare($sql_purchases);
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$purchases_result = $stmt->get_result();
$purchases = [];
while ($row = $purchases_result->fetch_assoc()) {
    $purchases[] = $row;
}
$stmt->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل فاتورة شراء</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/invoice.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>
   

    /* أنماط مربعات التصنيفات */
    .category-boxes {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin: 20px 0;
        justify-content: center;
        padding: 10px;
    }
    
    .category-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 130px;
        height: 130px;
        background-color: var(--color-secondary);
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 2px solid #eee;
        text-align: center;
    }
    
    .category-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        border-color: var(--color-primary);
    }
    
    .category-box.active {
        background-color: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
        transform: translateY(-3px);
    }
    
    .category-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            135deg, 
            rgba(255, 255, 255, 0.3) 0%, 
            rgba(255, 255, 255, 0) 60%
        );
        z-index: 1;
    }
    
    .category-box i {
        font-size: 38px;
        margin-bottom: 12px;
        color: var(--color-primary);
        z-index: 2;
        position: relative;
    }
    
    .category-box.active i {
        color: white;
    }
    
    .category-box span {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        z-index: 2;
        position: relative;
    }
    
    /* تنسيق للوضع المظلم */
    [data-theme="dark"] .category-box {
        background-color: var(--color-secondary);
        border-color: #444;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    }
    
    [data-theme="dark"] .category-box:hover {
        border-color: var(--color-primary);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    }
    
    [data-theme="dark"] .category-box.active {
        background-color: var(--color-primary);
        border-color: var(--color-primary);
    }
    
    [data-theme="dark"] .category-box::before {
        background: linear-gradient(
            135deg, 
            rgba(255, 255, 255, 0.1) 0%, 
            rgba(255, 255, 255, 0) 60%
        );
    }
    
    /* تنسيق شريط البحث */
    .search-bar {
        width: 60%;
        padding: 12px 15px;
        border-radius: 25px;
        border: 1px solid #ddd;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        margin: 15px auto;
        top: 100px;
        display: block;
    }
    
    .search-bar:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
    }
    
    [data-theme="dark"] .search-bar {
        background-color: #333;
        color: #eee;
        border-color: #444;
    }
    
    /* تحسين العرض على الشاشات الصغيرة */
    @media (max-width: 768px) {
        .category-box {
            width: 110px;
            height: 110px;
            padding: 10px;
        }
        
        .category-box i {
            font-size: 30px;
            margin-bottom: 8px;
        }
        
        .category-box span {
            font-size: 14px;
        }
    }
    
    @media (max-width: 480px) {
        .category-box {
            width: 90px;
            height: 90px;
        }
        
        .category-box i {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .category-box span {
            font-size: 12px;
        }
        
        .category-boxes {
            gap: 10px;
        }
    }
    
    .barcode-col{display:none;}
    .has-images-icon i{color:#28a745;}
    .no-images-icon i{color:#aaa;}
    .no-images-icon{cursor:default;pointer-events:none;opacity:0.6;}
    .images-gallery{display:flex;flex-wrap:wrap;gap:10px;justify-content:center;}
    </style>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
<div class="container">
    <div class="categories">
        <h3>تصنيف</h3>
        <input type="text" id="categorySearch" class="search-bar" placeholder="ابحث عن تصنيف...">
        <div class="category-boxes" id="categoryBoxes">
            <!-- إضافة مربع كل الأصناف بشكل افتراضي -->
            <div class="category-box active" data-category-id="all">
                <i class="fas fa-boxes"></i>
                <span>كل الأصناف</span>
            </div>
            <?php
            if ($categories_result->num_rows > 0) {
                while($row = $categories_result->fetch_assoc()) {
                    $encrypted_category_id = encrypt($row['category_id'], $key);
                    echo '<div class="category-box" data-category-id="' . $encrypted_category_id . '">';
                    echo '<i class="fas fa-box"></i>';
                    echo '<span>' . $row['name'] . '</span>';
                    echo '</div>';
                }
            }
            ?>
        </div>
    </div>

    <div class="items">
        <h3>الأصناف</h3>
        <input type="search" id="itemSearch" class="search-bar" placeholder="ابحث عن صنف...">
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th class="barcode-col">الباركود</th>
                    <th>الكمية</th>
                    <th>الصور</th>
                </tr>
            </thead>
            <tbody id="itemsList">
                <?php
                if ($items_result->num_rows > 0) {
                    while($row = $items_result->fetch_assoc()) {
                        $quantityClass = $row['quantity'] < 10 ? 'red' : 'green';
                        $favoriteIcon = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                        $favoriteClass = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? 'favorite-item' : '';
                        $image_class = $row['image_count'] > 0 ? 'has-images-icon' : 'no-images-icon';
                        $image_title = $row['image_count'] > 0 ? 'عرض الصور' : 'لا توجد صور';

                        $encId = htmlspecialchars(encrypt($row['item_id'],$key));
                        $row['encrypted_id'] = $encId;
                        $imageBtn = ($row['image_count'] > 0)
                            ? "<button class='action-btn has-images-icon' type='button' title='عرض الصور' onclick=\"viewItemImages('{$encId}', event)\"><i class='fas fa-eye'></i></button>"
                            : "<button class='action-btn no-images-icon' type='button' title='لا توجد صور' disabled onclick='event.stopPropagation();'><i class='fas fa-eye-slash'></i></button>";

                        $rowJson = htmlspecialchars(json_encode($row), ENT_QUOTES, 'UTF-8');
                        echo "<tr class='{$favoriteClass}' data-item='{$rowJson}' onclick='addItemToInvoice(JSON.parse(this.dataset.item))'>
                                <td>{$favoriteIcon}{$row['name']}</td>
                                <td class='barcode-col'>{$row['barcode']}</td>
                                <td class='item-quantity {$quantityClass}'>{$row['quantity']}</td>
                                <td>{$imageBtn}</td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='4'>لا توجد أصناف حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>

    <div class="selected-items">
        <h3>الأصناف المختارة</h3>
        <form id="updateInvoiceForm" method="POST" action="update_purchase_invoice.php" enctype="multipart/form-data">
            <input type="hidden" name="invoice_id" value="<?php echo htmlspecialchars($_GET['invoice_id']); ?>">
            <input type="hidden" name="store_id" value="<?php echo htmlspecialchars($_GET['store_id']); ?>">
            <input type="hidden" name="account_id" value="<?php echo htmlspecialchars($_SESSION['account_id']); ?>">
            <input type="hidden" name="total_amount" id="total_amount_input">
            <input type="hidden" name="removed_images" id="removed_images" value="">

            <!-- Image Upload Section -->
            <div>
                <label for="invoice_images">صور الفاتورة:</label>
                <div class="image-upload-container">
                    <label for="invoice_images" class="image-upload-label">
                        <i class="fas fa-upload"></i> اختر الصور
                    </label>
                    <input type="file" id="invoice_images" name="invoice_images[]" multiple accept="image/*" class="image-upload-input">
                    <div id="image-preview" class="image-preview">
                        <?php
                        $stmt = $conn->prepare("SELECT img_path FROM invoice_images WHERE purchase_invoice_id = ?");
                        $stmt->bind_param("i", $invoice_id);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        while ($row = $result->fetch_assoc()) {
                            echo "<div data-img-path='{$row['img_path']}'>
                                    <img src='{$row['img_path']}' alt='Invoice Image'>
                                    <i class='fas fa-times remove-icon' onclick='removeExistingImage(this)'></i>
                                  </div>";
                        }
                        $stmt->close();
                        ?>
                    </div>
                </div>
            </div>

            <!-- Selected Items Table -->
            <table>
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th class="barcode-col">الباركود</th>
                        <th>الكمية</th>
                        <th>التكلفة</th>
                        <th>إجمالي</th>
                        <th>الصور</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="selectedItemsTable">
                    <?php
                    foreach ($purchases as $purchase) {
                        $item_name = isset($purchase['item_name']) ? $purchase['item_name'] : '';
                        $barcode = isset($purchase['barcode']) ? $purchase['barcode'] : '';
                        $cost = isset($purchase['cost']) ? $purchase['cost'] : 0;
                        $total_amount = isset($purchase['total_amount']) ? $purchase['total_amount'] : 0;
                        $image_count = isset($purchase['image_count']) ? $purchase['image_count'] : 0;
                        $image_class = $image_count > 0 ? 'has-images-icon' : 'no-images-icon';
                        $image_title = $image_count > 0 ? 'عرض الصور' : 'لا توجد صور';

                        $imageBtn = ($image_count > 0)
                            ? '<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages(\''.htmlspecialchars(encrypt($purchase['item_id'],$key)).'\', event)"><i class="fas fa-eye"></i></button>'
                            : '<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>';

                        echo "<tr data-item-id='{$purchase['item_id']}'>
                                <td>{$item_name}</td>
                                <td class='barcode-col'>{$barcode}</td>
                                <td>
                                    <button type='button' class='quantity-btn' onclick='decreaseQuantity(this)'>-</button>
                                    <input type='number' name='items[{$purchase['item_id']}][quantity]' value='{$purchase['quantity']}' min='0.1' step='0.1' class='quantity-input device-status green' required oninput='updateTotal()'>
                                    <button type='button' class='quantity-btn' onclick='increaseQuantity(this)'>+</button>
                                </td>
                                <td>{$cost}</td>
                                <td>{$total_amount}</td>
                                <td>{$imageBtn}</td>
                                <td>
                                    <button type='button' class='action-btn' title='حذف' onclick='removeItemFromInvoice(this)'><i class='fas fa-trash-alt'></i></button>
                                </td>
                              </tr>";
                    }
                    ?>
                </tbody>
            </table>

            <div id="total_amount" class="total-display">
                <span>إجمالي الفاتورة: <span id="total_amount_value"><?php echo isset($invoice_result['total_amount']) ? $invoice_result['total_amount'] : 0; ?></span></span>
                <button type="submit" class="add-btn">تحديث الفاتورة</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for viewing item images -->
<div id="viewImagesModal" class="modal">
    <div class="modal-content" style="max-width:800px;">
        <span class="close" onclick="closeViewImagesModal()">&times;</span>
        <h2 id="modalItemName">صور الصنف</h2>
        <div id="modalImagesContainer" class="images-gallery"></div>
    </div>
</div>

<script>
    function loadItems(encryptedCategoryId) {
        // إذا التصنيف هو "كل الأصناف"، عرض جميع الأصناف
        if (encryptedCategoryId === 'all') {
            document.querySelectorAll('#itemsList tr').forEach(row => {
                row.style.display = '';
            });
            return;
        }
        
        const storeId = "<?php echo htmlspecialchars($_GET['store_id']); ?>";
        const accountId = "<?php echo htmlspecialchars($_SESSION['account_id']); ?>";
        fetch(`get_items.php?category_id=${encodeURIComponent(encryptedCategoryId)}&store_id=${storeId}&account_id=${accountId}`)
            .then(response => response.json())
            .then(data => {
                const itemsList = document.getElementById('itemsList');
                
                // عرض فقط العناصر للتصنيف المحدد
                document.querySelectorAll('#itemsList tr').forEach(row => {
                    row.style.display = 'none';
                });
                
                data.items.forEach(item => {
                    const quantityClass = item.quantity < 10 ? 'red' : 'green';
                    const favoriteIcon = item.is_favorite && item.is_favorite > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                    const favoriteClass = item.is_favorite && item.is_favorite > 0 ? 'favorite-item' : '';
                    const image_class = item.image_count > 0 ? 'has-images-icon' : 'no-images-icon';
                    const image_title = item.image_count > 0 ? 'عرض الصور' : 'لا توجد صور';
                    
                    // البحث عن الصف الحالي أولاً
                    let tr = null;
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        const cellText = row.querySelector('td')?.textContent;
                        if (cellText && cellText.includes(item.name)) {
                            tr = row;
                            tr.style.display = '';
                            tr.className = favoriteClass;
                            // تحديث محتوى الخلية لإضافة أيقونة المفضلة
                            const firstCell = tr.querySelector('td');
                            if (firstCell && !firstCell.innerHTML.includes('fa-star')) {
                                firstCell.innerHTML = favoriteIcon + item.name;
                            }
                        }
                    });
                    
                    // إذا لم يتم العثور على الصف، قم بإنشائه
                    if (!tr) {
                        tr = document.createElement('tr');
                        tr.className = favoriteClass;
                        tr.setAttribute('data-item', JSON.stringify(item));
                        tr.innerHTML = `
                            <td>${favoriteIcon}${item.name}</td>
                            <td class='barcode-col'>${item.barcode}</td>
                            <td class='item-quantity ${quantityClass}'>${item.quantity}</td>
                            <td><button class='action-btn ${image_class}' type='button' title='${image_title}' onclick='viewItemImages("${item.encrypted_id}", event)'><i class='fas fa-eye'></i></button></td>
                        `;
                        tr.onclick = () => addItemToInvoice(item);
                        itemsList.appendChild(tr);
                    }
                });
            })
            .catch(error => console.error('Error:', error));
    }

    function addItemToInvoice(item, showSuccessMessage = false) {
        console.log('Adding item to invoice:', item.item_id, item.name);
        
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        
        // البحث عن الصف الموجود بطرق متعددة
        let existingRow = null;
        
        // الطريقة الأولى: البحث بـ data-item-id
        existingRow = document.querySelector(`#selectedItemsTable tr[data-item-id="${item.item_id}"]`);
        
        // الطريقة الثانية: البحث في جميع الصفوف يدوياً
        if (!existingRow) {
            const allRows = selectedItemsTable.querySelectorAll('tr');
            allRows.forEach(row => {
                const itemId = row.getAttribute('data-item-id');
                if (itemId == item.item_id) {
                    existingRow = row;
                }
            });
        }
        
        // الطريقة الثالثة: البحث بالاسم إذا لم نجد بالـ ID
        if (!existingRow) {
            const allRows = selectedItemsTable.querySelectorAll('tr');
            allRows.forEach(row => {
                const firstCell = row.querySelector('td');
                if (firstCell && firstCell.textContent.trim() === item.name.trim()) {
                    existingRow = row;
                }
            });
        }
        
        let isNewItem = false;
        
        if (existingRow) {
            console.log('Found existing row, updating quantity');
            const quantityInput = existingRow.querySelector('input[type="number"]');
            if (quantityInput) {
                const currentValue = parseFloat(quantityInput.value) || 0;
                quantityInput.value = (currentValue + 1).toFixed(1);
                console.log('Updated quantity to:', quantityInput.value);
            }
        } else {
            console.log('Creating new row');
            isNewItem = true;
            const row = document.createElement('tr');
            row.setAttribute('data-item-id', item.item_id);
            row.innerHTML = `
                <td>${item.name}</td>
                <td class='barcode-col'>
                    <input type="hidden" name="items[${item.item_id}][barcode]" value="${item.barcode}">
                    ${item.barcode}
                </td>
                <td>
                    <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">-</button>
                    <input type="number" name="items[${item.item_id}][quantity]" value="1.0" min="0.1" step="0.1" class="quantity-input device-status green" required oninput="updateTotal()">
                    <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">+</button>
                </td>
                <td>${item.cost}</td>
                <td>${item.cost}</td>
                <td>
                    ${item.image_count > 0 ? `<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages('${item.encrypted_id}', event)"><i class="fas fa-eye"></i></button>` : `<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>`}
                </td>
                <td>
                    <button type='button' class='action-btn' title='حذف' onclick='removeItemFromInvoice(this)'><i class='fas fa-trash-alt'></i></button>
                </td>
                <input type="hidden" name="items[${item.item_id}][name]" value="${item.name}">
            `;
            selectedItemsTable.appendChild(row);
        }
        
        updateTotal();
        
        // إظهار رسالة النجاح إذا طُلب ذلك
        if (showSuccessMessage) {
            const message = isNewItem ? `تم إضافة ${item.name} للفاتورة` : `تم زيادة كمية ${item.name}`;
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح',
                text: message,
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }

    function removeItemFromInvoice(button) {
        const row = button.parentElement.parentElement;
        row.remove();
        updateTotal();
    }

    function increaseQuantity(button) {
        const input = button.previousElementSibling;
        input.value = (parseFloat(input.value) + 0.1).toFixed(1);
        updateTotal();
    }

    function decreaseQuantity(button) {
        const input = button.nextElementSibling;
        if (input.value > 0.1) {
            input.value = (parseFloat(input.value) - 0.1).toFixed(1);
            updateTotal();
        }
    }

    function updateTotal() {
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        let total = 0;
        rows.forEach(row => {
            const quantityInput = row.querySelector('input[type="number"]');
            if (quantityInput) {
                const quantity = parseFloat(quantityInput.value) || 0;
                const costCell = row.cells[3];
                if (costCell) {
                    const cost = parseFloat(costCell.textContent) || 0;
                    const subtotal = quantity * cost;
                    const totalCell = row.cells[4];
                    if (totalCell) {
                        totalCell.textContent = subtotal.toFixed(2);
                    }
                    total += subtotal;
                }
            }
        });
        
        const totalValueElement = document.getElementById('total_amount_value');
        const totalInputElement = document.getElementById('total_amount_input');
        
        if (totalValueElement) {
            totalValueElement.textContent = total.toFixed(2);
        }
        if (totalInputElement) {
            totalInputElement.value = total.toFixed(2);
        }
        
        // إزالة هذا السطر لأن العنصر غير موجود في هذه الصفحة
        // document.getElementById('remaining').textContent = '0.00';
    }

    function removeExistingImage(icon) {
        const imgDiv = icon.parentElement;
        const imgPath = imgDiv.getAttribute('data-img-path');
        imgDiv.remove();
        const removedImagesInput = document.getElementById('removed_images');
        removedImagesInput.value += imgPath + ',';
    }

    document.getElementById('invoice_images').addEventListener('change', function (event) {
        const files = event.target.files;
        const previewContainer = document.getElementById('image-preview');
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function (e) {
                const previewDiv = document.createElement('div');
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <i class="fas fa-times remove-icon" onclick="this.parentElement.remove()"></i>
                `;
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        // إضافة معالج أحداث لمربعات التصنيف
        const categoryBoxes = document.querySelectorAll('.category-box');
        
        categoryBoxes.forEach(box => {
            box.addEventListener('click', function() {
                // إزالة الكلاس active من جميع المربعات
                categoryBoxes.forEach(b => b.classList.remove('active'));
                
                // إضافة الكلاس active للمربع المحدد
                this.classList.add('active');
                
                const categoryId = this.getAttribute('data-category-id');
                
                // إذا تم تحديد "كل الأصناف"
                if (categoryId === 'all') {
                    // عرض جميع الأصناف
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    // تحميل الأصناف للتصنيف المحدد
                    loadItems(categoryId);
                }
            });
        });
        
        // معالج أحداث البحث في التصنيفات
        document.getElementById('categorySearch').addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            
            document.querySelectorAll('.category-box').forEach(box => {
                const text = box.querySelector('span').textContent.toLowerCase();
                if (text.includes(searchText) || box.getAttribute('data-category-id') === 'all') {
                    box.style.display = '';
                } else {
                    box.style.display = 'none';
                }
            });
        });
        
        // تحديث الإجمالي عند تحميل الصفحة
        updateTotal();
    });
    
    document.getElementById('categorySearch').oninput = function() {
        const filter = this.value.toLowerCase();
        document.querySelectorAll('.category-box').forEach(box => {
            const text = box.querySelector('span').textContent.toLowerCase();
            if (text.includes(filter) || box.getAttribute('data-category-id') === 'all') {
                box.style.display = '';
            } else {
                box.style.display = 'none';
            }
        });
    }

    document.getElementById('itemSearch').oninput = function() {
        const filter = this.value.toLowerCase();
        const itemsList = document.getElementById('itemsList');
        const items = itemsList.getElementsByTagName('tr');
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const text = item.textContent || item.innerText;
            item.style.display = text.toLowerCase().indexOf(filter) > -1 ? '' : 'none';
        }
    }

    document.getElementById('updateInvoiceForm').addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        // Show loading modal
        Swal.fire({
            title: 'جاري تحديث الفاتورة...',
            text: 'يرجى الانتظار حتى يتم الانتهاء.',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch('update_purchase_invoice.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            Swal.close(); // Close the loading modal
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم تحديث الفاتورة بنجاح.',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    window.location.href = 'purchase_invoices.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في تحديث الفاتورة.',
                    text: data.message,
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        })
        .catch(error => {
            Swal.close(); // Close the loading modal
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'فشل في تحديث الفاتورة.',
                text: 'حدث خطأ أثناء تحديث الفاتورة.',
                showConfirmButton: false,
                timer: 3000
            });
        });
    });
    
    // View Item Images Functions
    function viewItemImages(encryptedItemId, evt){
        if(evt){evt.stopPropagation();}
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(r=>r.json())
        .then(d=>{
            if(d.success){
                const container=document.getElementById('modalImagesContainer');
                container.innerHTML='';
                document.getElementById('modalItemName').textContent=d.item.name;
                if(d.item.images&&d.item.images.length>0){
                    d.item.images.forEach(img=>{
                        const el=document.createElement('img');el.src=img.img_path;el.style.maxWidth='150px';el.style.borderRadius='8px';container.appendChild(el);
                    });
                }else{container.innerHTML='<p>لا توجد صور متاحة لهذا الصنف.</p>';}
                document.getElementById('viewImagesModal').classList.add('active');
            }else{Swal.fire({icon:'error',title:'خطأ',text:'فشل في جلب الصور'});}
        }).catch(err=>{console.error(err);Swal.fire({icon:'error',title:'خطأ',text:'تعذر الاتصال بالخادم'});});
    }
    function closeViewImagesModal(){document.getElementById('viewImagesModal').classList.remove('active');}
    window.onclick=function(e){const m=document.getElementById('viewImagesModal');if(e.target===m){m.classList.remove('active');}};

    // ميزة قارئ الباركود
    let globalBarcode = "";
    let barcodeTimer = null;
    let lastBarcodeTime = 0;
    let isProcessing = false;

    document.addEventListener('keypress', function(e) {
        // تجاهل إذا كان التركيز على حقل إدخال أو textarea أو إذا كان المف��اح Enter
        const activeElement = document.activeElement;
        if (activeElement.tagName.toLowerCase() === "input" || 
            activeElement.tagName.toLowerCase() === "textarea" || 
            activeElement.isContentEditable ||
            e.key === "Enter" ||
            isProcessing) {
            return;
        }

        const currentTime = Date.now();
        
        // إذا مر أكثر من 500ms منذ آخر ضغطة مفتاح، ابدأ باركود جديد
        if (currentTime - lastBarcodeTime > 500) {
            globalBarcode = "";
        }
        
        lastBarcodeTime = currentTime;
        globalBarcode += e.key;

        // إلغاء المؤقت السابق
        if (barcodeTimer) {
            clearTimeout(barcodeTimer);
        }

        barcodeTimer = setTimeout(() => {
            if (globalBarcode.length > 0 && !isProcessing) {
                isProcessing = true;
                processBarcode(globalBarcode.trim());
                // إعادة تعيين كل شيء
                globalBarcode = "";
                lastBarcodeTime = 0;
                setTimeout(() => {
                    isProcessing = false;
                }, 100);
            }
        }, 100);
    });

    // إضافة معالج لإعادة تعيين الباركود عند النقر في أي مكان
    document.addEventListener('click', function() {
        globalBarcode = "";
        lastBarcodeTime = 0;
        if (barcodeTimer) {
            clearTimeout(barcodeTimer);
            barcodeTimer = null;
        }
    });

    // إضافة معالج لمفتاح Escape لإعادة تعيين الباركود
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            globalBarcode = "";
            lastBarcodeTime = 0;
            isProcessing = false;
            if (barcodeTimer) {
                clearTimeout(barcodeTimer);
                barcodeTimer = null;
            }
            console.log('Barcode reset manually');
        }
    });

    function processBarcode(barcode) {
        console.log('Processing barcode:', barcode); // للتتبع
        
        // التأكد من أن الباركود ليس فارغاً أو قصيراً جداً
        if (!barcode || barcode.length < 3) {
            console.log('Barcode too short, ignoring');
            return;
        }
        
        // البحث عن الصنف بناءً على الباركود من جميع الأصناف (حتى المخفية)
        const itemRows = document.querySelectorAll('#itemsList tr');
        let found = null;

        itemRows.forEach(row => {
            const barcodeCell = row.querySelector('.barcode-col');
            if (barcodeCell && barcodeCell.textContent.trim() === barcode.trim()) {
                const itemData = row.getAttribute('data-item');
                if (itemData) {
                    try {
                        found = JSON.parse(itemData);
                    } catch (e) {
                        console.error('Error parsing item data:', e);
                    }
                }
            }
        });

        if (found) {
            addItemToInvoice(found, true);
        } else {
            // إظهار رسالة خطأ
            Swal.fire({
                icon: 'error',
                title: 'صنف غير موجود',
                text: "لم يتم العثور على صنف بهذا الباركود: " + barcode,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
        
        // إعادة تعيين نهائية للمتغيرات
        setTimeout(() => {
            globalBarcode = "";
            lastBarcodeTime = 0;
            isProcessing = false;
        }, 50);
    }
    
</script>
<script src="js/theme.js"></script>

</body>
</html>
