<?php
ob_start(); // يبدأ تخزين أي إخراج مؤقت
include 'db_connection.php';
include 'encryption_functions.php';
require 'vendor/autoload.php'; // Ensure you have PHPExcel library installed

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$store_id = decrypt($encrypted_store_id, $key);

// Fetch the store name
$store_stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_stmt->bind_result($store_name);
$store_stmt->fetch();
$store_stmt->close();

$sql = "SELECT pi.invoice_id, pi.total_amount, pi.status, DATE_FORMAT(pi.created_at, '%Y-%m-%d') AS created_at, 
               a.name AS account_name, i.name, p.quantity, i.cost
        FROM purchase_invoices pi
        JOIN purchases p ON pi.invoice_id = p.invoice_id
        JOIN items i ON p.item_id = i.item_id
        LEFT JOIN accounts a ON pi.account_id = a.account_id
        WHERE pi.store_id = ?
        ORDER BY pi.created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('فواتير الشراء');

// Define styles
$headerStyle = [
    'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF007BFF']]
];
$invoiceHeaderStyle = [
    'font' => ['bold' => true, 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFE3F2FD']]
];
$detailRowStyle = [
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT, 'vertical' => Alignment::VERTICAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
    'font' => ['name' => 'Calibri', 'size' => 11]
];

$statusStyles = [
    'confirmed' => ['fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFD4EDDA']]],
    'pending' => ['fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFFFF3CD']]]
];

// Add logo and title
$sheet->mergeCells('A1:H1');
$sheet->setCellValue('A1', 'فواتير الشراء - ' . $store_name);
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(20);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getRowDimension('1')->setRowHeight(40);

// Set header row
$headers = ['رقم الفاتورة', 'الحالة', 'إجمال�� السعر', 'توقيت الفاتورة', 'اسم الحساب', 'اسم الصنف', 'الكمية', 'السعر'];
$columnIndex = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($columnIndex . '2', $header);
    $sheet->getStyle($columnIndex . '2')->applyFromArray($headerStyle);
    $columnIndex++;
}

$rowNumber = 3;
$currentInvoiceId = null;

function addInvoiceHeaderRow($sheet, $row, $rowNumber, $invoiceHeaderStyle, $statusStyles) {
    $sheet->setCellValue('A' . $rowNumber, $row['invoice_id']);
    
    // تطبيق لون حسب الحالة
    $status = strtolower($row['status']);
    $statusText = $status === 'confirmed' ? 'مؤكد' : 'معلق';
    $sheet->setCellValue('B' . $rowNumber, $statusText);
    if (isset($statusStyles[$status])) {
        $sheet->getStyle('B' . $rowNumber)->applyFromArray($statusStyles[$status]);
    }
    
    $sheet->setCellValue('C' . $rowNumber, $row['total_amount']);
    $sheet->setCellValue('D' . $rowNumber, $row['created_at']);
    $sheet->setCellValue('E' . $rowNumber, $row['account_name'] ?? 'غير محدد');
    $sheet->mergeCells('F' . $rowNumber . ':H' . $rowNumber);
    $sheet->setCellValue('F' . $rowNumber, 'تفاصيل الفاتورة');
    $sheet->getStyle('A' . $rowNumber . ':H' . $rowNumber)->applyFromArray($invoiceHeaderStyle);
}

function addDetailRow($sheet, $row, $rowNumber, $detailRowStyle) {
    $sheet->setCellValue('F' . $rowNumber, $row['name']);
    $sheet->setCellValue('G' . $rowNumber, $row['quantity']);
    $sheet->setCellValue('H' . $rowNumber, $row['cost']);
    $sheet->getStyle('F' . $rowNumber . ':H' . $rowNumber)->applyFromArray($detailRowStyle);
    $sheet->getStyle('F' . $rowNumber . ':H' . $rowNumber)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB($rowNumber % 2 == 0 ? 'FFF7F7F7' : 'FFFFFFFF');
    $sheet->getRowDimension($rowNumber)->setOutlineLevel(1)->setVisible(false)->setCollapsed(true);
}

while ($row = $result->fetch_assoc()) {
    if ($currentInvoiceId !== $row['invoice_id']) {
        addInvoiceHeaderRow($sheet, $row, $rowNumber, $invoiceHeaderStyle, $statusStyles);
        $currentInvoiceId = $row['invoice_id'];
        $rowNumber++;
    }
    addDetailRow($sheet, $row, $rowNumber, $detailRowStyle);
    $rowNumber++;
}

// Auto size columns
foreach (range('A', 'H') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

// Apply currency format to price columns
$sheet->getStyle('C3:C' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');
$sheet->getStyle('H3:H' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');

// Apply date format to date columns
$sheet->getStyle('D3:D' . $rowNumber)->getNumberFormat()->setFormatCode('yyyy-mm-dd');

// Set auto filter
$sheet->setAutoFilter($sheet->calculateWorksheetDimension());

// Enable grouping
$sheet->setShowSummaryBelow(false);

// Freeze panes
$sheet->freezePane('A3');

// Set right-to-left direction
$sheet->setRightToLeft(true);

// Log the purchase invoices export action
session_start();
$account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
            VALUES (?, 'export', 'purchase_invoices', ?)";
$description = "تم إصدار تقرير فواتير الشراء للفرع $store_name";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("is", $account_id, $description);
$log_stmt->execute();
$log_stmt->close();

// 1) نظّف أي إخراج سابق
ob_end_clean();

// 2) ارسل الهيدرز
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

// اسم الملف
$filename = 'purchase_invoices_' . $store_name . '_' . date('Ymd_His') . '.xlsx';
$asciiFilename = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $filename);

// fallback + UTF-8
header('Content-Disposition: attachment; filename="' . $asciiFilename . '";');
header("Content-Disposition: attachment; filename*=UTF-8''" . rawurlencode($filename));
header('Cache-Control: max-age=0');

// 3) احفظ الإخراج
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit();
?>
