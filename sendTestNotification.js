const admin = require('firebase-admin');
const serviceAccount = require('./test-c60d6-firebase-adminsdk-fbsvc-78904eea16.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

// Function to send test notification
async function sendTestNotification(token) {
    const message = {
        notification: {
            title: 'اختبار الإشعار',
            body: 'هذا إشعار اختبار. اضغط هنا للتفاعل.'
        },
        token: token
    };

    try {
        const response = await admin.messaging().send(message);
        console.log('Successfully sent message:', response);
        return response;
    } catch (error) {
        console.log('Error sending message:', error);
        throw error;
    }
}

// Example usage
const userToken = 'fJJm9hzvk8eJfaDGUACIo8:APA91bGQNRu5YYJxL0fuKfiY38lG7cfj-19B_cMdDybNygLur-1fD6b2DSDbQSRRaz5NTO2or9LrDuAlZ20iVNnbPT8V7qKURgAyNkyG3muVcPh3b7MMHeA'; // Replace with the actual user token
sendTestNotification(userToken);