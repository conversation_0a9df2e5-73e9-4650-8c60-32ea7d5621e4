<?php
/**
 * Get OAuth 2.0 access token for Firebase Cloud Messaging API
 * 
 * This file provides a function to get an OAuth 2.0 access token
 * using the service account credentials.
 */

/**
 * Get OAuth 2.0 access token
 * 
 * @param string $serviceAccountPath Path to the service account JSON file
 * @return array Array containing the access token and expiry time
 * @throws Exception If there's an error getting the access token
 */
function getAccessToken($serviceAccountPath) {
    // Check if service account file exists
    if (!file_exists($serviceAccountPath)) {
        throw new Exception('Service account key file not found at: ' . $serviceAccountPath);
    }
    
    // Load service account credentials
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    
    // Check if service account file is valid
    if (!isset($serviceAccount['private_key']) || !isset($serviceAccount['client_email'])) {
        throw new Exception('Invalid service account file. Missing required fields.');
    }
    
    // Create JWT claim
    $now = time();
    $jwt = [
        'iss' => $serviceAccount['client_email'],
        'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
        'aud' => 'https://oauth2.googleapis.com/token',
        'exp' => $now + 3600,
        'iat' => $now
    ];
    
    // Create JWT
    $jwt = createJWT($jwt, $serviceAccount['private_key']);
    
    // Exchange JWT for access token
    $ch = curl_init('https://oauth2.googleapis.com/token');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    // Enable verbose debugging
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    
    // Get verbose information
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    
    // Get HTTP status code
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if ($response === false) {
        throw new Exception('cURL error: ' . curl_error($ch) . "\nVerbose log: " . $verboseLog);
    }
    
    curl_close($ch);
    
    $data = json_decode($response, true);
    
    if (!isset($data['access_token'])) {
        throw new Exception('Failed to get access token: ' . $response . "\nVerbose log: " . $verboseLog);
    }
    
    return [
        'token' => $data['access_token'],
        'expires_in' => $data['expires_in'],
        'expires_at' => time() + $data['expires_in']
    ];
}

/**
 * Create JWT token
 * 
 * @param array $payload JWT payload
 * @param string $privateKey Private key
 * @return string JWT token
 */
function createJWT($payload, $privateKey) {
    // Create JWT header
    $header = [
        'alg' => 'RS256',
        'typ' => 'JWT'
    ];
    
    // Ensure private key is properly formatted
    $privateKey = str_replace("\\n", "\n", $privateKey);
    
    // Encode header and payload
    $base64UrlHeader = rtrim(strtr(base64_encode(json_encode($header)), '+/', '-_'), '=');
    $base64UrlPayload = rtrim(strtr(base64_encode(json_encode($payload)), '+/', '-_'), '=');
    
    // Create signature
    $signatureInput = $base64UrlHeader . '.' . $base64UrlPayload;
    $signature = '';
    
    // Check if openssl_sign is successful
    if (!openssl_sign($signatureInput, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
        throw new Exception('Failed to create signature: ' . openssl_error_string());
    }
    
    $base64UrlSignature = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');
    
    // Create JWT
    return $base64UrlHeader . '.' . $base64UrlPayload . '.' . $base64UrlSignature;
}
?>
