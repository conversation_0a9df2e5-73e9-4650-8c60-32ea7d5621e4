-- Table to store FCM tokens
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    store_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    last_updated <PERSON><PERSON><PERSON><PERSON>E NOT NULL,
    FOR<PERSON><PERSON><PERSON> KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (store_id) REFERENCES stores(store_id) ON DELETE CASCADE,
    UNIQUE KEY (account_id, token)
);

-- Add created_by column to notifications table if it doesn't exist
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS created_by INT NULL,
ADD FOREIGN KEY IF NOT EXISTS (created_by) REFERENCES accounts(account_id) ON DELETE SET NULL;
