<?php
// تحميل نظام الحماية والصلاحيات
require_once 'security.php';

// التحقق من صلاحية الوصول لإعدادات الحساب
checkPagePermission('cashier_account', 'access');

// فحص الصلاحيات الفرعية
$canView = hasPermission('cashier_account', 'view');
$canEditProfile = hasPermission('cashier_account', 'edit_profile');
$canSwitchStore = hasPermission('cashier_account', 'switch_store');

$encrypted_account_id = $_GET['account_id'] ?? null;
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

if (!$account_id) {
    die("Account ID not found. Please log in again.");
}

// Fetch user account details
$query = "SELECT username, password, name, phone, img_path, store_id, theme FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    die("User not found.");
}

$username = htmlspecialchars($user['username']);
$password = $user['password']; // Keep the encrypted password as is
$name = htmlspecialchars($user['name']);
$phone = htmlspecialchars($user['phone']);
$imgPath = $user['img_path'] ?? null;

// Fetch branch names from the stores table
$branches_query = "SELECT name FROM stores";
$branches_result = $conn->query($branches_query);
$branches = [];
if ($branches_result->num_rows > 0) {
    while ($row = $branches_result->fetch_assoc()) {
        $branches[] = $row['name'];
    }
}

// Fetch user's branch
$user_branch_query = "SELECT name FROM stores WHERE store_id = ?";
$user_branch_stmt = $conn->prepare($user_branch_query);
$user_branch_stmt->bind_param("i", $user['store_id']);
$user_branch_stmt->execute();
$user_branch_result = $user_branch_stmt->get_result();
$user_branch = $user_branch_result->fetch_assoc()['name'] ?? null;
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>إعدادات الحساب</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --primary-light: #4dabf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --light-bg: #f0f2f5;
            
            /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
            --dark-bg: #0f1419;
            --dark-surface: #1a2332;
            --dark-surface-light: #242b3d;
            --dark-surface-hover: #2a3441;
            --dark-text: #e1e8f0;
            --dark-text-secondary: #b8c5d1;
            --dark-text-muted: #8a9ba8;
            --border-color: #dee2e6;
            --dark-border: #2d3748;
            --dark-border-light: #3a4553;
            
            /* ألوان زرقاء ناعمة ومريحة */
            --blue-gradient-start: #1e3a8a;
            --blue-gradient-end: #3b82f6;
            --blue-accent: #5b9bd5;
            --blue-hover: #4a90c2;
            --blue-soft: #6ba3d6;
            --blue-muted: #4a7ba7;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: var(--light-bg);
            color: #333;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            direction: rtl;
            text-align: right;
        }

        .dark-mode {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 40%, var(--dark-surface-light) 100%);
            color: var(--dark-text);
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 10px 20px;
            background-color: transparent;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logout-icon {
            color: #ff0000;
            font-size: 24px;
            text-decoration: none;
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .logout-icon:hover {
            color: #ff6666;
            transform: scale(1.1);
        }

        .dark-mode .logout-icon {
            color: #ff8a80;
        }

        .dark-mode .logout-icon:hover {
            color: #ffcdd2;
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            padding: 30px 20px 100px;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Form Container */
        .form-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            margin-top: 20px;
            border: 1px solid rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            padding: 30px;
        }

        .form-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .dark-mode .form-container {
            background: var(--dark-surface-light);
            box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4);
            border: 1px solid var(--dark-border-light);
        }

        .dark-mode .form-container:hover {
            box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5);
            border-color: var(--blue-soft);
        }

        /* Profile Image */
        .account-profile-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 30px;
            display: block;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
            border: 4px solid var(--primary-color);
            transition: all 0.3s ease;
        }

        .account-profile-img:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.3);
        }

        .dark-mode .account-profile-img {
            border-color: var(--blue-soft);
            box-shadow: 0 8px 25px rgba(91, 155, 213, 0.2);
        }

        .dark-mode .account-profile-img:hover {
            box-shadow: 0 12px 35px rgba(91, 155, 213, 0.3);
        }

        .account-default-profile {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            font-size: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
            transition: all 0.3s ease;
        }

        .account-default-profile:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.3);
        }

        .dark-mode .account-default-profile {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 8px 25px rgba(91, 155, 213, 0.2);
        }

        .dark-mode .account-default-profile:hover {
            box-shadow: 0 12px 35px rgba(91, 155, 213, 0.3);
        }

        /* Form Labels */
        .form-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
            display: block;
            font-size: 1.1rem;
        }

        .dark-mode .form-label {
            color: var(--blue-soft);
        }

        /* Input Groups */
        .icon-input {
            position: relative;
            margin-bottom: 25px;
        }

        .icon-input i {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            color: var(--primary-color);
            font-size: 18px;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .dark-mode .icon-input i {
            color: var(--blue-soft);
        }

        .icon-input input,
        .icon-input select {
            width: 100%;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px 50px 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--white);
            color: #333;
            outline: none;
        }

        .icon-input input:focus,
        .icon-input select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .icon-input input:focus + i,
        .icon-input select:focus + i {
            color: var(--primary-dark);
            transform: translateY(-50%) scale(1.1);
        }

        .dark-mode .icon-input input,
        .dark-mode .icon-input select {
            background-color: var(--dark-surface-light);
            color: var(--dark-text);
            border-color: var(--dark-border-light);
        }

        .dark-mode .icon-input input:focus,
        .dark-mode .icon-input select:focus {
            border-color: var(--blue-soft);
            box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2);
            background-color: var(--dark-surface-hover);
        }

        .dark-mode .icon-input input:focus + i,
        .dark-mode .icon-input select:focus + i {
            color: var(--blue-hover);
        }

        .icon-input input::placeholder {
            color: #999;
            transition: all 0.3s ease;
        }

        .dark-mode .icon-input input::placeholder {
            color: var(--dark-text-muted);
        }

        /* Custom File Upload */
        .custom-file-input {
            display: none;
        }

        .custom-file-label {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            width: 100%;
            margin-bottom: 20px;
        }

        .custom-file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }

        .dark-mode .custom-file-label {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .custom-file-label:hover {
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
            background: linear-gradient(135deg, var(--blue-hover) 0%, var(--blue-soft) 100%);
        }

        .file-name {
            margin-top: 5px;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
            text-align: center;
            padding: 8px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            display: none;
        }

        .file-name.show {
            display: block;
        }

        .dark-mode .file-name {
            color: var(--blue-soft);
            background: rgba(91, 155, 213, 0.15);
        }

        /* Submit Button */
        .btn-primary {
            display: block;
            width: 100%;
            padding: 16px;
            border-radius: 12px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            color: var(--white);
            text-align: center;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            margin-top: 10px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }

        .dark-mode .btn-primary {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .btn-primary:hover {
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
            background: linear-gradient(135deg, var(--blue-hover) 0%, var(--blue-soft) 100%);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 10px 15px;
            }

            .page-title {
                font-size: 1.2rem;
            }

            .main-content {
                padding: 20px 15px 100px;
            }

            .form-container {
                padding: 20px;
            }

            .account-profile-img,
            .account-default-profile {
                width: 120px;
                height: 120px;
            }

            .account-default-profile {
                font-size: 48px;
            }
        }

        @media (max-width: 480px) {
            .icon-input input,
            .icon-input select {
                padding: 12px 45px 12px 15px;
                font-size: 14px;
            }

            .btn-primary {
                padding: 14px;
                font-size: 16px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="<?= $user['theme'] === 'Dark' ? 'dark-mode' : '' ?>">
    <header class="header">
        <a href="logout.php" class="logout-icon" title="تسجيل الخروج">
            <i class="fas fa-sign-out-alt"></i>
        </a>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-container fade-in">
        <?php if ($canView && !$canEditProfile): ?>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; text-align: center; color: #1976d2;">
            <i class="fas fa-info-circle"></i>
            <strong>وضع العرض فقط</strong> - يمكنك عرض بيانات الحساب بدون إمكانية التعديل
        </div>
        <?php endif; ?>
        <?php if ($imgPath): ?>
            <img src="<?= htmlspecialchars($imgPath) ?>" alt="Profile" class="account-profile-img">
        <?php else: ?>
            <div class="account-default-profile">
                <i class="fas fa-user"></i>
            </div>
        <?php endif; ?>
        <form id="update-account-form" action="update_account.php" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="account_id" value="<?= htmlspecialchars($encrypted_account_id) ?>">
            <label for="username" class="form-label">اسم المستخدم</label>
            <div class="icon-input">
                <i class="fas fa-user"></i>
                <input type="text" class="form-control" id="username" name="username" value="<?= $username ?>" <?= !$canEditProfile ? 'readonly' : 'required' ?>>
            </div>

            <label for="password" class="form-label">كلمة المرور</label>
            <div class="icon-input">
                <i class="fas fa-lock"></i>
                <input type="password" class="form-control" id="password" name="password" value="" placeholder="<?= !$canEditProfile ? 'كلمة المرور محمية' : 'أدخل كلمة المرور الجديدة إذا رغبت بتغييرها' ?>" <?= !$canEditProfile ? 'readonly' : '' ?>>
            </div>

            <label for="name" class="form-label">الاسم</label>
            <div class="icon-input">
                <i class="fas fa-id-card"></i>
                <input type="text" class="form-control" id="name" name="name" value="<?= $name ?>" <?= !$canEditProfile ? 'readonly' : 'required' ?>>
            </div>

            <label for="phone" class="form-label">رقم الهاتف</label>
            <div class="icon-input">
                <i class="fas fa-phone"></i>
                <input type="text" class="form-control" id="phone" name="phone" value="<?= $phone ?>" <?= !$canEditProfile ? 'readonly' : 'required' ?>>
            </div>

            <label for="branch" class="form-label">الفرع</label>
            <div class="icon-input">
                <i class="fas fa-store"></i>
                <select class="form-control" id="branch" name="branch" <?= !$canSwitchStore ? 'disabled' : 'required' ?>>
                    <option value="" disabled>اختر الفرع</option>
                    <?php foreach ($branches as $branch): ?>
                        <option value="<?= htmlspecialchars($branch) ?>" <?= $branch === $user_branch ? 'selected' : '' ?>>
                            <?= htmlspecialchars($branch) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <label for="theme" class="form-label">اختيار الثيم</label>
            <div class="icon-input">
                <i class="fas fa-palette"></i>
                <select class="form-control" id="theme" name="theme" <?= !$canEditProfile ? 'disabled' : 'required' ?>>
                    <option value="" disabled>اختر الثيم</option>
                    <option value="Light" <?= $user['theme'] === 'Light' ? 'selected' : '' ?>>فاتح</option>
                    <option value="Dark" <?= $user['theme'] === 'Dark' ? 'selected' : '' ?>>داكن</option>
                </select>
            </div>

            <?php if ($canEditProfile): ?>
            <label for="profile-img" class="custom-file-label">
                <i class="fas fa-upload"></i> تغيير الصورة الشخصية
            </label>
            <input type="file" id="profile-img" name="profile_img" accept="image/*" class="custom-file-input">
            <div id="file-name" class="file-name"></div>
            <button type="submit" class="btn-primary" style="font-family: 'Cairo', sans-serif;">حفظ التغييرات</button>
            <?php else: ?>
            <div style="text-align: center; padding: 15px; color: #666;">
                <i class="fas fa-eye"></i>
                وضع العرض فقط - لا يمكن حفظ التغييرات
            </div>
            <?php endif; ?>
        </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <?php include 'bottom_nav.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Check if user has edit permission
        const canEditProfile = <?= $canEditProfile ? 'true' : 'false' ?>;
        const canSwitchStore = <?= $canSwitchStore ? 'true' : 'false' ?>;
        
        if (canEditProfile) {
            document.getElementById('update-account-form').addEventListener('submit', function (e) {
                e.preventDefault();
                const formData = new FormData(this);

                fetch('update_account.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    // Check if response is JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        // If not JSON, treat as text and show error
                        return response.text().then(text => {
                            throw new Error('Response is not JSON: ' + text);
                        });
                    }
                })
                .then(data => {
                    if (data.success) {
                        // Update localStorage for theme
                        const selectedTheme = document.getElementById('theme').value;
                        localStorage.setItem('dark-mode', selectedTheme === 'Dark');

                        Swal.fire({
                            icon: 'success',
                            title: 'تم التعديل بنجاح',
                            text: 'تم حفظ التغييرات بنجاح',
                            showConfirmButton: false,
                            timer: 2000
                        }).then(() => {
                            location.reload(); // Refresh the page after success
                        });
                    } else if (data.message === 'لم يتم تعديل أي بيانات.') {
                        Swal.fire({
                            icon: 'info',
                            title: 'لم يتم تعديل أي بيانات',
                            text: 'لم يتم إجراء أي تغييرات على البيانات',
                            showConfirmButton: false,
                            timer: 2000
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message || 'حدث خطأ أثناء التعديل',
                            showConfirmButton: true
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء إرسال الطلب.',
                        showConfirmButton: true
                    });
                });
            });
        }

        // Display uploaded file name (only if user can edit)
        if (canEditProfile) {
            const profileImgInput = document.getElementById('profile-img');
            const fileNameDisplay = document.getElementById('file-name');

            if (profileImgInput && fileNameDisplay) {
                profileImgInput.addEventListener('change', function () {
                    const file = this.files[0];
                    if (file) {
                        fileNameDisplay.textContent = file.name;
                        fileNameDisplay.classList.add('show');
                    } else {
                        fileNameDisplay.textContent = '';
                        fileNameDisplay.classList.remove('show');
                    }
                });
            }
        }

        // Apply theme based on user preference from database
        const body = document.body;
        const userTheme = '<?= $user['theme'] ?>';
        const themeSelect = document.getElementById('theme');

        // Update localStorage to match database value
        localStorage.setItem('dark-mode', userTheme === 'Dark');

        // Add event listener for theme change (only if user can edit)
        if (canEditProfile) {
            themeSelect.addEventListener('change', function() {
                const selectedTheme = this.value;
                if (selectedTheme === 'Dark') {
                    body.classList.add('dark-mode');
                    localStorage.setItem('dark-mode', true);
                } else {
                    body.classList.remove('dark-mode');
                    localStorage.setItem('dark-mode', false);
                }
            });
        }
    </script>
</body>
</html>
