<?php
ob_start(); // يبدأ تخزين أي إخراج مؤقت
/**
 * ملف تصدير فواتير البيع بالجملة إلى Excel
 * يقوم بتصدير جميع فواتير البيع بالجملة مع تفاصيل الأصناف والفروع والأشخاص
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
require 'vendor/autoload.php'; // Ensure you have PHPExcel library installed

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

// فحص صلاحية التصدير
if (!hasPermission('wholesale_invoices', 'export_excel')) {
    die('ليس لديك صلاحية لتصدير البيانات');
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$store_id = decrypt($encrypted_store_id, $key);

// Fetch the store name
$store_stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_stmt->bind_result($store_name);
$store_stmt->fetch();
$store_stmt->close();

// استعلام شامل لجلب فواتير البيع بالجملة مع تفاصيل الأصناف والفروع والأشخاص
$sql = "SELECT 
            wi.invoice_id, 
            wi.total_amount, 
            wi.status,
            DATE_FORMAT(wi.created_at, '%Y-%m-%d %H:%i') AS created_at,
            a.name AS seller_name,
            CASE 
                WHEN wi.buyer REGEXP '^[0-9]+$' THEN s.name 
                ELSE wi.buyer 
            END AS buyer_name,
            ab.name AS buyer_person_name,
            i.name AS item_name, 
            ws.quantity, 
            i.cost,
            (ws.quantity * i.cost) AS item_total
        FROM wholesale_invoices wi
        LEFT JOIN accounts a ON wi.account_id = a.account_id
        LEFT JOIN accounts ab ON wi.account_id_buyer = ab.account_id
        LEFT JOIN stores s ON (wi.buyer REGEXP '^[0-9]+$' AND wi.buyer = s.store_id)
        LEFT JOIN whosales ws ON wi.invoice_id = ws.invoice_id
        LEFT JOIN items i ON ws.item_id = i.item_id
        WHERE wi.store_id = ?
        ORDER BY wi.created_at DESC, wi.invoice_id, i.name";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('فواتير البيع بالجملة');

// Define styles
$headerStyle = [
    'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF28a745']]
];

$invoiceHeaderStyle = [
    'font' => ['bold' => true, 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFE8F5E8']]
];

$detailRowStyle = [
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT, 'vertical' => Alignment::VERTICAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
    'font' => ['name' => 'Calibri', 'size' => 11]
];

$statusStyles = [
    'confirmed' => ['fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFD4EDDA']]],
    'pending' => ['fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFFFF3CD']]]
];

// Add logo and title
$sheet->mergeCells('A1:I1');
$sheet->setCellValue('A1', 'فواتير البيع بالجملة - ' . $store_name);
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(20);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getRowDimension('1')->setRowHeight(40);

// Add export date and time
$sheet->mergeCells('A2:I2');
$sheet->setCellValue('A2', 'تاريخ التصدير: ' . date('Y-m-d H:i:s'));
$sheet->getStyle('A2')->getFont()->setSize(12);
$sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Set header row
$headers = [
    'رقم الفاتورة', 
    'الحالة',
    'إجمالي السعر', 
    'توقيت الفاتورة', 
    'البائع',
    'الفرع/الشخص المشتري',
    'الشخص المشتري',
    'اسم الصنف', 
    'الكمية', 
    'سعر الوحدة',
    'إجمالي الصنف'
];

$columnIndex = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($columnIndex . '3', $header);
    $sheet->getStyle($columnIndex . '3')->applyFromArray($headerStyle);
    $columnIndex++;
}

$rowNumber = 4;
$currentInvoiceId = null;
$totalInvoices = 0;
$totalAmount = 0;

function addInvoiceHeaderRow($sheet, $row, $rowNumber, $invoiceHeaderStyle, $statusStyles) {
    global $totalInvoices, $totalAmount;
    
    $sheet->setCellValue('A' . $rowNumber, $row['invoice_id']);
    
    // تطبيق لون حسب الحالة
    $status = strtolower($row['status']);
    $statusText = $status === 'confirmed' ? 'مؤكد' : 'معلق';
    $sheet->setCellValue('B' . $rowNumber, $statusText);
    if (isset($statusStyles[$status])) {
        $sheet->getStyle('B' . $rowNumber)->applyFromArray($statusStyles[$status]);
    }
    
    $sheet->setCellValue('C' . $rowNumber, $row['total_amount']);
    $sheet->setCellValue('D' . $rowNumber, $row['created_at']);
    $sheet->setCellValue('E' . $rowNumber, $row['seller_name'] ?? 'غير محدد');
    $sheet->setCellValue('F' . $rowNumber, $row['buyer_name'] ?? 'غير محدد');
    $sheet->setCellValue('G' . $rowNumber, $row['buyer_person_name'] ?? 'غير محدد');
    
    $sheet->mergeCells('H' . $rowNumber . ':K' . $rowNumber);
    $sheet->setCellValue('H' . $rowNumber, 'تفاصيل الأصناف');
    
    $sheet->getStyle('A' . $rowNumber . ':K' . $rowNumber)->applyFromArray($invoiceHeaderStyle);
    
    $totalInvoices++;
    $totalAmount += $row['total_amount'];
}

function addDetailRow($sheet, $row, $rowNumber, $detailRowStyle) {
    $sheet->setCellValue('H' . $rowNumber, $row['item_name'] ?? 'غير محدد');
    $sheet->setCellValue('I' . $rowNumber, $row['quantity'] ?? 0);
    $sheet->setCellValue('J' . $rowNumber, $row['cost'] ?? 0);
    $sheet->setCellValue('K' . $rowNumber, $row['item_total'] ?? 0);
    
    $sheet->getStyle('H' . $rowNumber . ':K' . $rowNumber)->applyFromArray($detailRowStyle);
    $sheet->getStyle('H' . $rowNumber . ':K' . $rowNumber)->getFill()
          ->setFillType(Fill::FILL_SOLID)
          ->getStartColor()
          ->setARGB($rowNumber % 2 == 0 ? 'FFF7F7F7' : 'FFFFFFFF');
    
    // إضافة مستوى التجميع للتفاصيل
    $sheet->getRowDimension($rowNumber)->setOutlineLevel(1)->setVisible(false)->setCollapsed(true);
}

// معالجة البيانات
while ($row = $result->fetch_assoc()) {
    if ($currentInvoiceId !== $row['invoice_id']) {
        addInvoiceHeaderRow($sheet, $row, $rowNumber, $invoiceHeaderStyle, $statusStyles);
        $currentInvoiceId = $row['invoice_id'];
        $rowNumber++;
    }
    
    // إضافة تفاصيل الصنف إذا كانت موجودة
    if (!empty($row['item_name'])) {
        addDetailRow($sheet, $row, $rowNumber, $detailRowStyle);
        $rowNumber++;
    }
}

// إضافة صف الإجمالي
$rowNumber += 2;
$sheet->mergeCells('A' . $rowNumber . ':F' . $rowNumber);
$sheet->setCellValue('A' . $rowNumber, 'الإجمالي العام');
$sheet->setCellValue('G' . $rowNumber, 'عدد الفواتير: ' . $totalInvoices);
$sheet->setCellValue('H' . $rowNumber, 'إجمالي المبلغ:');
$sheet->setCellValue('I' . $rowNumber, $totalAmount);

$totalRowStyle = [
    'font' => ['bold' => true, 'size' => 14],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFFFE0B2']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THICK]]
];
$sheet->getStyle('A' . $rowNumber . ':K' . $rowNumber)->applyFromArray($totalRowStyle);

// Auto size columns
foreach (range('A', 'K') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

// Apply currency format to price columns
$sheet->getStyle('C4:C' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');
$sheet->getStyle('J4:K' . $rowNumber)->getNumberFormat()->setFormatCode('#,##0.00 [$جنيه-ARA]');

// Apply date format to date columns
$sheet->getStyle('D4:D' . $rowNumber)->getNumberFormat()->setFormatCode('yyyy-mm-dd hh:mm');

// Set auto filter
$sheet->setAutoFilter('A3:K3');

// Enable grouping
$sheet->setShowSummaryBelow(false);

// Freeze panes
$sheet->freezePane('A4');

// Set right-to-left direction
$sheet->setRightToLeft(true);

// تسجيل عملية التصدير في السجل
session_start();
$account_id = decrypt($_SESSION['account_id'], $key);
$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
            VALUES (?, 'export', 'wholesale_invoices', ?)";
$description = "تم إصدار تقرير فواتير البيع بالجملة للفرع $store_name";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("is", $account_id, $description);
$log_stmt->execute();
$log_stmt->close();

// 1) نظّف أي إخراج سابق
ob_end_clean();

// 2) ارسل الهيدرز
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

// اسم الملف
$filename = 'wholesale_invoices_' . $store_name . '_' . date('Ymd_His') . '.xlsx';
$asciiFilename = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $filename);

// fallback + UTF-8
header('Content-Disposition: attachment; filename="' . $asciiFilename . '";');
header("Content-Disposition: attachment; filename*=UTF-8''" . rawurlencode($filename));
header('Cache-Control: max-age=0');

// 3) احفظ الإخراج
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit();
?>