<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_invoice_id = $_POST['invoice_id'];
$invoice_id = decrypt($encrypted_invoice_id, $key);
$total_amount = $_POST['total_amount'];
$items = $_POST['items'];

// Decrypt seller store_id and, if applicable, buyer store_id
$store_id = decrypt($_POST['store_id'], $key);
$buyer_store_id = null;
if ($_POST['buyer'] !== 'person') {
    $buyer_store_id = decrypt($_POST['buyer'], $key);
}

// Fetch the status of the invoice
$sql_status = "SELECT status FROM wholesale_invoices WHERE invoice_id = ?";
$stmt_status = $conn->prepare($sql_status);
$stmt_status->bind_param("i", $invoice_id);
$stmt_status->execute();
$result_status = $stmt_status->get_result();
$invoice_status = $result_status->fetch_assoc()['status'];
$stmt_status->close();

// Update only the total_amount in wholesale_invoices
$sql_update = "UPDATE wholesale_invoices SET total_amount = ? WHERE invoice_id = ?";
$stmt = $conn->prepare($sql_update);
$stmt->bind_param("di", $total_amount, $invoice_id);
$stmt->execute();
$stmt->close();

$sql_fetch_purchase_invoice = "SELECT purchase_invoice_id FROM wholesale_invoices WHERE invoice_id = ?";
$stmt_fetch_purchase_invoice = $conn->prepare($sql_fetch_purchase_invoice);
$stmt_fetch_purchase_invoice->bind_param("i", $invoice_id);
$stmt_fetch_purchase_invoice->execute();
$result_purchase_invoice = $stmt_fetch_purchase_invoice->get_result();
$purchase_invoice_id = $result_purchase_invoice->fetch_assoc()['purchase_invoice_id'];
$stmt_fetch_purchase_invoice->close();

// Fetch existing items in the invoice
$sql_existing_items = "SELECT item_id, quantity FROM whosales WHERE invoice_id = ?";
$stmt_existing_items = $conn->prepare($sql_existing_items);
$stmt_existing_items->bind_param("i", $invoice_id);
$stmt_existing_items->execute();
$result_existing_items = $stmt_existing_items->get_result();
$existing_items = [];
while ($row = $result_existing_items->fetch_assoc()) {
    $existing_items[$row['item_id']] = $row['quantity'];
}
$stmt_existing_items->close();

$conn->begin_transaction();
try {
    // Handle items in the updated invoice
    if (strtolower(trim($invoice_status)) === 'confirmed') {
        // Full update logic for confirmed invoices
        foreach ($items as $item_id => $itemData) {
            $barcode = $itemData['barcode'];
            $newQty = $itemData['quantity'];
            $item_total = $itemData['total'];

            // Fetch the item_id for the seller's store using barcode and store_id
            $sql_fetch_seller_item = "SELECT item_id FROM items WHERE barcode = ? AND store_id = ?";
            $stmt_fetch_seller_item = $conn->prepare($sql_fetch_seller_item);
            $stmt_fetch_seller_item->bind_param("si", $barcode, $store_id);
            $stmt_fetch_seller_item->execute();
            $seller_item_id = $stmt_fetch_seller_item->get_result()->fetch_assoc()['item_id'];
            $stmt_fetch_seller_item->close();

            if (isset($existing_items[$item_id])) {
                // Item exists in the original invoice
                $oldQty = $existing_items[$item_id];
                $diff = $newQty - $oldQty;
                $abs = abs($diff);

                if ($diff !== 0) {
                    // Adjust seller's inventory
                    $sql_adj = $diff > 0
                        ? "UPDATE items SET quantity = quantity - ? WHERE item_id = ? AND store_id = ?"
                        : "UPDATE items SET quantity = quantity + ? WHERE item_id = ? AND store_id = ?";
                    $stmt_adj = $conn->prepare($sql_adj);
                    $stmt_adj->bind_param("iii", $abs, $seller_item_id, $store_id);
                    $stmt_adj->execute();
                    $stmt_adj->close();

                    // Adjust buyer's inventory if applicable
                    if ($buyer_store_id) {
                        // Fetch the item_id for the buyer's store using barcode and store_id
                        $sql_fetch_buyer_item = "SELECT item_id FROM items WHERE barcode = ? AND store_id = ?";
                        $stmt_fetch_buyer_item = $conn->prepare($sql_fetch_buyer_item);
                        $stmt_fetch_buyer_item->bind_param("si", $barcode, $buyer_store_id);
                        $stmt_fetch_buyer_item->execute();
                        $buyer_item_id = $stmt_fetch_buyer_item->get_result()->fetch_assoc()['item_id'];
                        $stmt_fetch_buyer_item->close();

                        $sql_buy = $diff > 0
                            ? "UPDATE items SET quantity = quantity + ? WHERE item_id = ? AND store_id = ?"
                            : "UPDATE items SET quantity = quantity - ? WHERE item_id = ? AND store_id = ?";
                        $stmt_buy = $conn->prepare($sql_buy);
                        $stmt_buy->bind_param("iii", $abs, $buyer_item_id, $buyer_store_id);
                        $stmt_buy->execute();
                        $stmt_buy->close();
                    }
                }

                // Update the item in whosales
                $sql_update_whosales = "UPDATE whosales SET quantity = ?, total_amount = ? WHERE invoice_id = ? AND item_id = ?";
                $stmt_update_whosales = $conn->prepare($sql_update_whosales);
                $stmt_update_whosales->bind_param("diii", $newQty, $item_total, $invoice_id, $item_id);
                $stmt_update_whosales->execute();
                $stmt_update_whosales->close();

                // Update the transaction for the item in the seller's store
                $sql_update_transaction = "
                    UPDATE itemtransactions 
                    SET quantity = ? 
                    WHERE transaction_type = 'wholesale_sale' 
                      AND item_id = ?";
                $stmt_update_transaction = $conn->prepare($sql_update_transaction);
                $stmt_update_transaction->bind_param("ii", $newQty, $seller_item_id);
                $stmt_update_transaction->execute();
                $stmt_update_transaction->close();

                // Update the item in purchases if applicable
                if ($purchase_invoice_id) {
                    $sql_update_purchase = "UPDATE purchases SET quantity = ?, total_amount = ? WHERE invoice_id = ? AND item_id = ?";
                    $stmt_update_purchase = $conn->prepare($sql_update_purchase);
                    $stmt_update_purchase->bind_param("diii", $newQty, $item_total, $purchase_invoice_id, $item_id);
                    $stmt_update_purchase->execute();
                    $stmt_update_purchase->close();

                    // Update the transaction for the item in the buyer's store
                    if ($buyer_store_id) {
                        $sql_update_buyer_transaction = "
                            UPDATE itemtransactions 
                            SET quantity = ? 
                            WHERE transaction_type = 'wholesale_purchase' 
                              AND transaction_id_ref = ? 
                              AND item_id = ?";
                        $stmt_update_buyer_transaction = $conn->prepare($sql_update_buyer_transaction);
                        $stmt_update_buyer_transaction->bind_param("iii", $newQty, $purchase_invoice_id, $buyer_item_id);
                        $stmt_update_buyer_transaction->execute();
                        $stmt_update_buyer_transaction->close();
                    }
                }

                // Remove the item from the existing items array
                unset($existing_items[$item_id]);
            } else {
                // New item added to the invoice
                // Deduct from seller's inventory
                $sql_deduct = "UPDATE items SET quantity = quantity - ? WHERE barcode = ? AND store_id = ?";
                $stmt_deduct = $conn->prepare($sql_deduct);
                $stmt_deduct->bind_param("isi", $newQty, $barcode, $store_id);
                $stmt_deduct->execute();
                $stmt_deduct->close();

                // Add to buyer's inventory if applicable
                if ($buyer_store_id) {
                    $sql_add = "UPDATE items SET quantity = quantity + ? WHERE barcode = ? AND store_id = ?";
                    $stmt_add = $conn->prepare($sql_add);
                    $stmt_add->bind_param("isi", $newQty, $barcode, $buyer_store_id);
                    $stmt_add->execute();
                    $stmt_add->close();
                }

                $sql_insert_whosales = "INSERT INTO whosales (invoice_id, store_id, item_id, quantity, total_amount) VALUES (?, ?, ?, ?, ?)";
                $stmt_insert_whosales = $conn->prepare($sql_insert_whosales);
                $stmt_insert_whosales->bind_param("iiiii", $invoice_id, $store_id, $item_id, $newQty, $item_total);
                $stmt_insert_whosales->execute();
                $stmt_insert_whosales->close();

                // Insert into purchases if applicable
                if ($purchase_invoice_id) {
                    $sql_insert_purchase = "INSERT INTO purchases (store_id, invoice_id, item_id, quantity, time, total_amount) VALUES (?, ?, ?, ?, NOW(), ?)";
                    $stmt_insert_purchase = $conn->prepare($sql_insert_purchase);
                    $stmt_insert_purchase->bind_param("iiiii", $buyer_store_id, $purchase_invoice_id, $item_id, $newQty, $item_total);
                    $stmt_insert_purchase->execute();
                    $stmt_insert_purchase->close();
                }

                // Insert a new transaction for the item in the seller's store
                $sql_insert_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'wholesale_sale', ?, ?)";
                $stmt_insert_transaction = $conn->prepare($sql_insert_transaction);
                $stmt_insert_transaction->bind_param("iii", $item_id, $invoice_id, $newQty);
                $stmt_insert_transaction->execute();
                $stmt_insert_transaction->close();

                // Insert a new transaction for the item in the buyer's store
                if ($buyer_store_id && $purchase_invoice_id) {
                    $sql_insert_buyer_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'wholesale_purchase', ?, ?)";
                    $stmt_insert_buyer_transaction = $conn->prepare($sql_insert_buyer_transaction);
                    $stmt_insert_buyer_transaction->bind_param("iii", $item_id, $purchase_invoice_id, $newQty);
                    $stmt_insert_buyer_transaction->execute();
                    $stmt_insert_buyer_transaction->close();
                }
            }
        }

        // Handle removed items
        foreach ($existing_items as $item_id => $oldQty) {
            // Fetch barcode for the item
            $sql_fetch_barcode = "SELECT barcode FROM items WHERE item_id = ?";
            $stmt_fetch_barcode = $conn->prepare($sql_fetch_barcode);
            $stmt_fetch_barcode->bind_param("i", $item_id);
            $stmt_fetch_barcode->execute();
            $result_barcode = $stmt_fetch_barcode->get_result();
            $barcode = $result_barcode->fetch_assoc()['barcode'];
            $stmt_fetch_barcode->close();

            // Add back to seller's inventory
            $sql_add_back = "UPDATE items SET quantity = quantity + ? WHERE barcode = ? AND store_id = ?";
            $stmt_add_back = $conn->prepare($sql_add_back);
            $stmt_add_back->bind_param("isi", $oldQty, $barcode, $store_id);
            $stmt_add_back->execute();
            $stmt_add_back->close();

            // Deduct from buyer's inventory if applicable
            if ($buyer_store_id) {
                $sql_deduct_buyer = "UPDATE items SET quantity = quantity - ? WHERE barcode = ? AND store_id = ?";
                $stmt_deduct_buyer = $conn->prepare($sql_deduct_buyer);
                $stmt_deduct_buyer->bind_param("isi", $oldQty, $barcode, $buyer_store_id);
                $stmt_deduct_buyer->execute();
                $stmt_deduct_buyer->close();
            }

            // Remove from whosales
            $sql_delete_whosales = "DELETE FROM whosales WHERE invoice_id = ? AND item_id = ?";
            $stmt_delete_whosales = $conn->prepare($sql_delete_whosales);
            $stmt_delete_whosales->bind_param("ii", $invoice_id, $item_id);
            $stmt_delete_whosales->execute();
            $stmt_delete_whosales->close();

            // Remove from purchases if applicable
            if ($purchase_invoice_id) {
                $sql_delete_purchase = "DELETE FROM purchases WHERE invoice_id = ? AND item_id = ?";
                $stmt_delete_purchase = $conn->prepare($sql_delete_purchase);
                $stmt_delete_purchase->bind_param("ii", $purchase_invoice_id, $item_id);
                $stmt_delete_purchase->execute();
                $stmt_delete_purchase->close();
            }

            // Remove the transaction for the item in the seller's store
            $sql_delete_transaction = "DELETE FROM itemtransactions WHERE transaction_type = 'wholesale_sale' AND transaction_id_ref = ? AND item_id = ?";
            $stmt_delete_transaction = $conn->prepare($sql_delete_transaction);
            $stmt_delete_transaction->bind_param("ii", $invoice_id, $item_id);
            $stmt_delete_transaction->execute();
            $stmt_delete_transaction->close();

            // Remove the transaction for the item in the buyer's store
            if ($buyer_store_id && $purchase_invoice_id) {
                $sql_delete_buyer_transaction = "DELETE FROM itemtransactions WHERE transaction_type = 'wholesale_purchase' AND transaction_id_ref = ? AND item_id = ?";
                $stmt_delete_buyer_transaction = $conn->prepare($sql_delete_buyer_transaction);
                $stmt_delete_buyer_transaction->bind_param("ii", $purchase_invoice_id, $item_id);
                $stmt_delete_buyer_transaction->execute();
                $stmt_delete_buyer_transaction->close();
            }
        }

        // Update the total amount in the purchase invoice
        if ($purchase_invoice_id) {
            $sql_update_purchase_invoice = "UPDATE purchase_invoices SET total_amount = ? WHERE invoice_id = ?";
            $stmt_update_purchase_invoice = $conn->prepare($sql_update_purchase_invoice);
            $stmt_update_purchase_invoice->bind_param("di", $total_amount, $purchase_invoice_id);
            $stmt_update_purchase_invoice->execute();
            $stmt_update_purchase_invoice->close();
        }
    } else {
        // Restricted update logic for pending invoices
        foreach ($items as $item_id => $itemData) {
            $newQty = $itemData['quantity'];
            $item_total = $itemData['total'];

            // Update the item in whosales
            $sql_update_whosales = "UPDATE whosales SET quantity = ?, total_amount = ? WHERE invoice_id = ? AND item_id = ?";
            $stmt_update_whosales = $conn->prepare($sql_update_whosales);
            $stmt_update_whosales->bind_param("diii", $newQty, $item_total, $invoice_id, $item_id);
            $stmt_update_whosales->execute();
            $stmt_update_whosales->close();
        }
    }

    // Handle removed images
    if (!empty($_POST['removed_images'])) {
        $removedImages = explode(',', rtrim($_POST['removed_images'], ','));
        foreach ($removedImages as $imgPath) {
            $stmt = $conn->prepare("DELETE FROM wholesale_invoice_images WHERE wholesale_invoice_id = ? AND img_path = ?");
            $stmt->bind_param("is", $invoice_id, $imgPath);
            $stmt->execute();
            $stmt->close();
            if (file_exists($imgPath)) {
                unlink($imgPath); // Delete the image file from the server
            }
        }
    }

    // Handle new images
    if (!empty($_FILES['invoice_images']['name'][0])) {
        $targetDir = "uploads/wholesale_invoices/";

        // Ensure the directory exists and is writable
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true); // Create the directory with proper permissions
        }

        foreach ($_FILES['invoice_images']['tmp_name'] as $index => $tmpName) {
            $fileName = basename($_FILES['invoice_images']['name'][$index]);
            $targetFile = $targetDir . uniqid() . "_" . $fileName;

            if (move_uploaded_file($tmpName, $targetFile)) {
                $stmt = $conn->prepare("INSERT INTO wholesale_invoice_images (wholesale_invoice_id, img_path) VALUES (?, ?)");
                $stmt->bind_param("is", $invoice_id, $targetFile);
                $stmt->execute();
                $stmt->close();
            } else {
                error_log("Failed to move uploaded file: $tmpName to $targetFile");
            }
        }
    }

    // Log the update operation in the system_logs table
    $account_id = decrypt($_SESSION['account_id'], $key); // Decrypt account ID from session
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'update', 'wholesale_invoices', ?, ?)";
    $description = "تم تعديل فاتورة بيع بالجملة برقم $invoice_id بقيمة $total_amount";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    $conn->commit();
} catch (Exception $e) {
    $conn->rollback();
    throw $e;
}

$conn->close();

// Set success message in session
session_start();
$_SESSION['message'] = 'تم تحديث الفاتورة بنجاح.';
$_SESSION['message_type'] = 'success';

// Redirect to the edit page to display the success message
header("Location: edit_wholesale_invoice.php?invoice_id=" . urlencode($encrypted_invoice_id) . "&store_id=" . urlencode(encrypt($store_id, $key)));

// After a delay, redirect to the wholesale invoices page
echo "<script>
    setTimeout(function() {
        window.location.href = 'wholesale_invoices.php?store_id=" . urlencode(encrypt($store_id, $key)) . "';
    }, 3000);
</script>";
exit();
?>
