<?php

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';
$key = getenv('ENCRYPTION_KEY');

// فحص صلاحية الوصول لوحدة التصنيفات
checkPagePermission('categories', 'view');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_category_id'])) {
    // فحص صلاحية حذف التصنيف
    if (!hasPermission('categories', 'delete_category')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف التصنيفات']);
        exit();
    }

    $delete_category_id = $_POST['delete_category_id'];

    // Check if the category has items
    $stmt = $conn->prepare("SELECT COUNT(*) FROM items WHERE category_id = ?");
    $stmt->bind_param("i", $delete_category_id);
    $stmt->execute();
    $stmt->bind_result($item_count);
    $stmt->fetch();
    $stmt->close();

    if ($item_count == 0) {
        $stmt = $conn->prepare("DELETE FROM categories WHERE category_id = ?");
        $stmt->bind_param("i", $delete_category_id);
        $stmt->execute();
        $stmt->close();
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'لا يمكن حذف التصنيف لأنه يحتوي على أصناف.']);
    }
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_category_id'])) {
    // فحص صلاحية تعديل التصنيف
    if (!hasPermission('categories', 'edit_category')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل التصنيفات']);
        exit();
    }

    $edit_category_id = $_POST['edit_category_id'];
    $new_category_name = $_POST['new_category_name'];

    $stmt = $conn->prepare("UPDATE categories SET name = ? WHERE category_id = ?");
    $stmt->bind_param("si", $new_category_name, $edit_category_id);
    $stmt->execute();
    $stmt->close();

    echo json_encode(['success' => true]);
    exit();
}

$result = null;

if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);
    $_SESSION['store_id'] = $encrypted_store_id;
    // Fetch store name
    $store_sql = "SELECT name FROM stores WHERE store_id = ?";
    $store_stmt = $conn->prepare($store_sql);
    $store_stmt->bind_param("i", $store_id);
    $store_stmt->execute();
    $store_stmt->bind_result($store_name);
    $store_stmt->fetch();
    $store_stmt->close();

    // Fetch categories again to ensure data is up-to-date
    $sql = "SELECT * FROM categories WHERE store_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التصنيفات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">

</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container">

    <h2>إدارة التصنيفات</h2>
    <h3>الفرع: <?php echo htmlspecialchars($store_name); ?></h3>

    <?php if (hasPermission('categories', 'add_category')): ?>
        <button class="add-btn" id="addCategoryBtn" title="إضافة تصنيف جديد">+</button>
    <?php endif; ?>
 
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>اسم التصنيف</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($result && $result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $encrypted_category_id = encrypt($row['category_id'], $key);
                        echo "<tr id='category-{$row['category_id']}'>
                                <td>{$row['name']}</td>
                                <td>
                                    <div class='action-buttons' style='display: inline-flex; gap: 5px;'>";
                        
                        // زر إضافة صنف إلى التصنيف
                        if (hasPermission('categories', 'add_item_to_category')) {
                            echo "<button class='add-btn' onclick='openAddItemModal({$row['category_id']})' title='إضافة صنف'>
                                    <i class='fas fa-plus'></i>
                                  </button>";
                        }
                        
                        // زر عرض أصناف التصنيف
                        if (hasPermission('categories', 'view_category_items')) {
                            echo "<button class='add-btn' onclick='openItemsList(\"{$encrypted_category_id}\")' title='عرض أصناف التصنيف'>
                                    <i class='fas fa-list'></i>
                                  </button>";
                        }
                        
                        // زر حذف التصنيف
                        if (hasPermission('categories', 'delete_category')) {
                            echo "<button class='add-btn' onclick='deleteCategory({$row['category_id']})' title='حذف التصنيف'>
                                    <i class='fas fa-trash-alt'></i>
                                  </button>";
                        }
                        
                        // زر تعديل التصنيف
                        if (hasPermission('categories', 'edit_category')) {
                            echo "<button class='add-btn' onclick='openEditCategoryModal({$row['category_id']}, \"{$row['name']}\")' title='تعديل التصنيف'>
                                    <i class='fas fa-edit'></i>
                                  </button>";
                        }
                        
                        echo "    </div>
                                </td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='2'>لا توجد تصنيفات حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>

    <div id="categoryModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>إضافة تصنيف جديد</h2>
            <form method="POST" action="add_category.php" enctype="multipart/form-data">
                <input type="hidden" name="store_id" value="<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>">
                <input type="text" name="category_name" class="input-field" placeholder="اسم التصنيف" required>
                <button type="submit" class="add-btn">إضافة التصنيف</button>
            </form>
        </div>
    </div>

    <div id="itemModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>إضافة صنف جديد</h2>
        <form method="POST" action="add_item.php" enctype="multipart/form-data" style="width: 100%; max-width: 500px; margin: 0 auto;">
            <input type="hidden" name="category_id" id="category_id">
            <input type="hidden" name="store_id" value="<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>">

            <label for="item_type">نوع الصنف:</label>
            <select name="item_type" id="item_type" class="input-field" required onchange="toggleItemFields()">
                <option value="">اختر نوع الصنف</option>
                <option value="piece">قطعة</option>
                <option value="box">كرتونة</option>
                <option value="fridge">تلاجة</option>
                <option value="other">أخرى</option>
            </select>

            <div id="pieceFields" class="item-fields">
                <label for="item_name_piece">اسم الصنف:</label>
                <input type="text" name="item_name_piece" id="item_name_piece" class="input-field" placeholder="اسم الصنف">

                <label for="cost_piece">تكلفة المنتج:</label>
                <input type="number" step="0.01" name="cost_piece" id="cost_piece" class="input-field" placeholder="جملة الصنف">

                <label for="price1_piece">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_piece" id="price1_piece" class="input-field" placeholder="سعر القطاعي">

                <label for="quantity_piece">الكمية:</label>
                <input type="number" step="0.01" name="quantity_piece" id="quantity_piece" class="input-field" placeholder="الكمية">
            </div>

            <div id="boxFields" class="item-fields">
                <label for="item_name_box">اسم الكرتونة:</label>
                <input type="text" name="item_name_box" id="item_name_box" class="input-field" placeholder="اسم الكرتونة">

                <label for="cost_box">تكلفة الكرتونة:</label>
                <input type="number" step="0.01" name="cost_box" id="cost_box" class="input-field" placeholder="جملة الكرتونة">

                <label for="price1_box">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_box" id="price1_box" class="input-field" placeholder="سعر القطاعي">

                <label for="pieces_per_box">عدد القطع داخل الكرتونة:</label>
                <input type="number" step="0.01" name="pieces_per_box" id="pieces_per_box" class="input-field" placeholder="عدد القطع">

                <label for="quantity_box">كمية الكراتين:</label>
                <input type="number" step="0.01" name="quantity_box" id="quantity_box" class="input-field" placeholder="كمية الكراتين">
            </div>

            <div id="fridgeFields" class="item-fields">
                <label for="item_name_fridge">اسم الصنف:</label>
                <input type="text" name="item_name_fridge" id="item_name_fridge" class="input-field" placeholder="اسم الصنف">

                <label for="cost_fridge">سعر الكيلو جملة:</label>
                <input type="number" step="0.01" name="cost_fridge" id="cost_fridge" class="input-field" placeholder="سعر الكيلو جملة">

                <label for="price1_fridge">سعر الكيلو قطاعي:</label>
                <input type="number" step="0.01" name="price1_fridge" id="price1_fridge" class="input-field" placeholder="سعر الكيلو قطاعي">

                <label for="quantity_fridge">الوزن الموجود:</label>
                <input type="number" step="0.01" name="quantity_fridge" id="quantity_fridge" class="input-field" placeholder="الوزن الموجود">
            </div>

            <label for="barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="barcode" class="input-field" placeholder="باركود المنتج" required>

            <label>
                <input type="checkbox" name="auto_generate_barcode" id="auto_generate_barcode" onclick="toggleBarcode()" checked> تحديد الباركود تلقائي
            </label>
            <div id="barcode_error" class="error-message"></div>

            <label for="item_images">صور الصنف:</label>
            <input type="file" name="item_images[]" id="item_images" class="input-field" multiple accept="image/*">
            <small>يمكنك اختيار عدة صور للصنف</small>

            <button type="submit" class="add-btn">إضافة الصنف</button>
        </form>
    </div>
</div>

    <div id="editCategoryModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تعديل اسم التصنيف</h2>
            <form id="editCategoryForm" method="POST">
                <input type="hidden" name="edit_category_id" id="edit_category_id">
                <input type="text" name="new_category_name" id="new_category_name" class="input-field" placeholder="اسم التصنيف الجديد" required>
                <button type="submit" class="add-btn">تعديل التصنيف</button>
            </form>
        </div>
    </div>

    <div class="popup-message" id="popupMessage"></div>
</div>

<form id="storeForm" method="GET" action="categories.php" style="display:none;">
    <input type="hidden" name="store_id" id="store_id">
</form>

<script>
    // متغيرات الصلاحيات
    const permissions = {
        add_category: <?php echo hasPermission('categories', 'add_category') ? 'true' : 'false'; ?>,
        edit_category: <?php echo hasPermission('categories', 'edit_category') ? 'true' : 'false'; ?>,
        delete_category: <?php echo hasPermission('categories', 'delete_category') ? 'true' : 'false'; ?>,
        add_item_to_category: <?php echo hasPermission('categories', 'add_item_to_category') ? 'true' : 'false'; ?>,
        view_category_items: <?php echo hasPermission('categories', 'view_category_items') ? 'true' : 'false'; ?>
    };

    var categoryModal = document.getElementById("categoryModal");
    var itemModal = document.getElementById("itemModal");
    var editCategoryModal = document.getElementById("editCategoryModal");
    var addCategoryBtn = document.getElementById("addCategoryBtn");
    var categorySpan = document.getElementsByClassName("close")[0];
    var itemSpan = document.getElementsByClassName("close")[1];
    var editCategorySpan = document.getElementsByClassName("close")[2];

    if (addCategoryBtn) {
        addCategoryBtn.onclick = function() {
            if (!permissions.add_category) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لإضافة التصنيفات'
                });
                return;
            }
            categoryModal.classList.add("active");
        }
    }

    categorySpan.onclick = function() {
        categoryModal.classList.remove("active");
    }

    itemSpan.onclick = function() {
        itemModal.classList.remove("active");
    }

    editCategorySpan.onclick = function() {
        editCategoryModal.classList.remove("active");
    }

    window.onclick = function(event) {
        if (event.target == categoryModal) {
            categoryModal.classList.remove("active");
        }
        if (event.target == itemModal) {
            itemModal.classList.remove("active");
        }
        if (event.target == editCategoryModal) {
            editCategoryModal.classList.remove("active");
        }
    }

    function openAddItemModal(categoryId) {
        if (!permissions.add_item_to_category) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لإضافة أصناف إلى التصنيفات'
            });
            return;
        }
        document.getElementById("category_id").value = categoryId;
        itemModal.classList.add("active");
    }

    function openItemsList(encryptedCategoryId) {
        if (!permissions.view_category_items) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لعرض أصناف التصنيفات'
            });
            return;
        }
        window.location.href = `items.php?category_id=${encodeURIComponent(encryptedCategoryId)}`;
    }

    function openEditCategoryModal(categoryId, categoryName) {
        if (!permissions.edit_category) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتعديل التصنيفات'
            });
            return;
        }
        document.getElementById("edit_category_id").value = categoryId;
        document.getElementById("new_category_name").value = categoryName;
        editCategoryModal.classList.add("active");
    }

    function toggleBarcode() {
        var barcodeField = document.getElementById("barcode");
        var autoGenerateCheckbox = document.getElementById("auto_generate_barcode");
        if (autoGenerateCheckbox.checked) {
            barcodeField.disabled = true;
        } else {
            barcodeField.disabled = false;
        }
    }

    // Ensure the barcode field is disabled by default
    document.addEventListener('DOMContentLoaded', function() {
        toggleBarcode();
    });

    function showPopupMessage(message, type, duration = 3000) {
        Swal.fire({
            icon: type,
            title: message,
            showConfirmButton: false,
            timer: duration
        });
    }

    <?php if (isset($_SESSION['message'])): ?>
        showPopupMessage('<?php echo $_SESSION['message']; ?>', '<?php echo $_SESSION['message_type']; ?>');
        <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
    <?php endif; ?>

    function deleteCategory(categoryId) {
        if (!permissions.delete_category) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لحذف التصنيفات'
            });
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفه!'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('delete_category_id', categoryId);

                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`category-${categoryId}`).remove();
                        Swal.fire(
                            'تم الحذف!',
                            'تم حذف التصنيف بنجاح.',
                            'success'
                        );
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message
                        });
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    }

    function toggleItemFields() {
        var itemType = document.getElementById("item_type").value;
        var pieceFields = document.getElementById("pieceFields");
        var boxFields = document.getElementById("boxFields");
        var fridgeFields = document.getElementById("fridgeFields");

        pieceFields.style.display = "none";
        boxFields.style.display = "none";
        fridgeFields.style.display = "none";

        if (itemType === "piece" || itemType === "other") {
            pieceFields.style.display = "block";
        } else if (itemType === "box") {
            boxFields.style.display = "block";
        } else if (itemType === "fridge") {
            fridgeFields.style.display = "block";
        }
    }

    document.querySelector('form[action="add_item.php"]').addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('add_item.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPopupMessage(data.message, 'success');
                itemModal.classList.remove("active");
                this.reset(); // Clear the form inputs
                toggleItemFields(); // Reset the item fields display
            } else {
                showPopupMessage(data.message, 'error');
            }
        })
        .catch(error => console.error('Error:', error));
    });

    document.querySelector('form[action="add_category.php"]').addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('add_category.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPopupMessage(data.message, 'success');
                categoryModal.classList.remove("active");
                setTimeout(() => {
                    location.reload(); // Reload the page to reflect changes
                }, 3000); // Reload after 3000ms
            } else {
                showPopupMessage(data.message, 'error');
            }
        })
        .catch(error => console.error('Error:', error));
    });

    document.getElementById("editCategoryForm").addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPopupMessage('تم تعديل التصنيف بنجاح', 'success', 5000); // Extend duration to 5000ms
                editCategoryModal.classList.remove("active");
                setTimeout(() => {
                    location.reload(); // Reload the page to reflect changes
                }, 3000); // Reload after 5000ms
            } else {
                showPopupMessage('حدث خطأ أثناء تعديل التصنيف', 'error');
            }
        })
        .catch(error => console.error('Error:', error));
    });

    // WebSocket integration
    const socket = io('http://localhost:3000');

    socket.on('categoryAdded', function(category) {
        const encryptedCategoryId = '<?php echo encrypt("CATEGORY_ID_PLACEHOLDER", $key); ?>'.replace('CATEGORY_ID_PLACEHOLDER', category.category_id);
        const newRow = `<tr id='category-${category.category_id}'>
                            <td>${category.name}</td>
                            <td>
                                <div class='action-buttons' style='display: inline-flex; gap: 5px;'>
                                    <button class='add-btn' onclick='openAddItemModal(${category.category_id})'>
                                        <i class='fas fa-plus'></i>
                                    </button>
                                    <button class='add-btn' onclick='openItemsList("${encryptedCategoryId}")'>
                                        <i class='fas fa-list'></i>
                                    </button>
                                    <button class='add-btn' onclick='deleteCategory(${category.category_id})'>
                                        <i class='fas fa-trash-alt'></i>
                                    </button>
                                    <button class='add-btn' onclick='openEditCategoryModal(${category.category_id}, "${category.name}")'>
                                        <i class='fas fa-edit'></i>
                                    </button>
                                </div>
                            </td>
                        </tr>`;
        document.querySelector('tbody').insertAdjacentHTML('beforeend', newRow);
        showPopupMessage('تم إضافة التصنيف بنجاح', 'success');
    });

    socket.on('categoryDeleted', function(categoryId) {
        document.getElementById(`category-${categoryId}`).remove();
    });

    function submitForm(storeId) {
        document.getElementById("store_id").value = storeId;
        document.getElementById("storeForm").submit();
    }
</script>

<?php include 'notifications.php'; ?>
</body>
</html>

<?php

?>