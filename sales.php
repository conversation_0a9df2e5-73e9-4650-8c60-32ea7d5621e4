<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

$store_id = null;
if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);
}

$sql = "SELECT DATE(s.time) AS order_date, s.account_id, a.username, a.phone, 
               CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
               END AS status, 
               COUNT(s.item_id) AS item_count, 
               SUM(s.quantity) AS total_quantity, 
               SUM(s.price * s.quantity) AS total_amount, 
               COALESCE(SUM(s.collected), 0) AS collected
        FROM sales s
        JOIN accounts a ON s.account_id = a.account_id";

if ($store_id) {
    $sql .= " WHERE s.store_id = ?";
}

$sql .= " GROUP BY DATE(s.time), s.account_id, a.username, a.phone
          ORDER BY DATE(s.time) DESC"; // ترتيب الطلبات حسب التاريخ من الأحدث إلى الأقدم

$stmt = $conn->prepare($sql);
if ($store_id) {
    $stmt->bind_param("i", $store_id);
}
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <link href="uploads\img\logo.png" rel="icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات التجار</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/sales.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

   

</head>
<?php include 'sidebar.php'; ?>

<div class="container">
    <!-- Header Section with Enhanced Design -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-line"></i>
            إدارة المبيعات والمرتجعات
        </h1>
        <p class="page-subtitle">تتبع وإدارة جميع عمليات البيع والإرجاع بسهولة</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-content">
                <h3 id="total-orders">0</h3>
                <p>إجمالي الطلبات</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-content">
                <h3 id="total-amount">0 ج.م</h3>
                <p>إجمالي المبيعات</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3 id="pending-orders">0</h3>
                <p>طلبات معلقة</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3 id="confirmed-orders">0</h3>
                <p>طلبات مؤكدة</p>
            </div>
        </div>
    </div>

    <!-- Enhanced Search Container -->
    <div class="search-container">
        <div class="search-header">
            <h3><i class="fas fa-filter"></i> فلترة البيانات</h3>
            <button type="button" class="toggle-search-btn" onclick="toggleSearchForm()">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
    <form id="searchForm" class="search-form">
            <input type="hidden" id="store-id" name="store_id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">
            
            <!-- Search Fields Row 1 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="search-name">
                        <i class="fas fa-user"></i>
                        اسم الشخص
                    </label>
                    <input type="text" id="search-name" placeholder="ابحث عن اسم الشخص">
                </div>
                <div class="search-field">
                    <label for="search-phone">
                        <i class="fas fa-phone"></i>
                        رقم الهاتف
                    </label>
                    <input type="text" id="search-phone" placeholder="ابحث عن رقم الهاتف">
                </div>
                <div class="search-field">
                    <label for="search-barcode">
                        <i class="fas fa-barcode"></i>
                        البـاركود
                    </label>
                    <input type="text" id="search-barcode" placeholder="ابحث بالباركود">
                </div>
            </div>

            <!-- Search Fields Row 2 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="start-date">
                        <i class="fas fa-calendar-alt"></i>
                        من تاريخ
                    </label>
                    <input type="date" id="start-date" value="<?php echo date('Y-m-01'); ?>">
                </div>
                <div class="search-field">
                    <label for="end-date">
                        <i class="fas fa-calendar-alt"></i>
                        إلى تاريخ
                    </label>
                    <input type="date" id="end-date">
                </div>
                <div class="search-field">
                    <label for="status-filter">
                        <i class="fas fa-flag"></i>
                        حالة الطلب
                    </label>
                    <select id="status-filter">
                        <option value="">كل الحالات</option>
                        <option value="pending">انتظار</option>
                        <option value="delayed">مؤجل</option>
                        <option value="confirmed">مؤكد</option>
                    </select>
                </div>
            </div>

            <!-- Search Fields Row 3 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="display-mode">
                        <i class="fas fa-eye"></i>
                        طريقة العرض
                    </label>
                    <select id="display-mode" onchange="fetchOrders()">
                        <option value="by_date">عرض حسب التاريخ</option>
                        <option value="by_account">عرض حسب الحساب</option>
                    </select>
                </div>
                <div class="search-actions">
                    <button type="button" class="search-btn" onclick="searchOrders()">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <button type="button" class="clear-btn" onclick="clearDates()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="button" class="refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Tabs Container -->
    <div class="tabs-container">
        <div class="tabs">
            <button class="tab-btn active" data-tab="sales">
                <i class="fas fa-shopping-bag"></i>
                المبيعات
                <span class="tab-count" id="sales-count">0</span>
            </button>
            <button class="tab-btn" data-tab="returns">
                <i class="fas fa-undo"></i>
                المرتجعات
                <span class="tab-count" id="returns-count">0</span>
            </button>
        </div>
    </div>

    
    <!-- Enhanced Table Section -->
    <div class="table-section">
        <div class="table-header">
            <div class="table-title">
                <h3 id="table-title">قائمة المبيعات</h3>
                <span class="table-subtitle" id="table-subtitle">عرض جميع المعاملات</span>
            </div>
            <div class="table-controls">
                <button class="control-btn" onclick="selectAllOrders()" title="تحديد الكل">
                    <i class="fas fa-check-square"></i>
                </button>
                <button class="control-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </button>
                <button class="control-btn" onclick="printTable()" title="طباعة الجدول">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
        
        <div class="table-wrapper">
            <table class="enhanced-table">
                <thead>
                    <tr>
                        <th class="checkbox-column">
                            <input type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()">
                        </th>
                        <th class="action-column">
                            <i class="fas fa-print"></i>
                            فاتورة
                        </th>
                        <th class="sortable" onclick="sortTable('username')">
                            <i class="fas fa-user"></i>
                            اسم الشخص
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th>
                            <i class="fas fa-user-tag"></i>
                            العميل
                        </th>
                        <th class="sortable" onclick="sortTable('phone')">
                            <i class="fas fa-phone"></i>
                            رقم الهاتف
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('status')">
                            <i class="fas fa-flag"></i>
                            حالة الفاتورة
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('quantity')">
                            <i class="fas fa-boxes"></i>
                            كمية الأصناف
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('amount')">
                            <i class="fas fa-money-bill-wave"></i>
                            مجموع الفاتورة
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="profit-column sortable" onclick="sortTable('profit')">
                            <i class="fas fa-chart-line"></i>
                            مكسب الفاتورة
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('date')">
                            <i class="fas fa-calendar-alt"></i>
                            التاريخ
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="action-column">
                            <i class="fas fa-cogs"></i>
                            إجراءات
                        </th>
                    </tr>
                </thead>
                <tbody id="orders-table-body">
                    <!-- سيتم ملء القائمة باستخدام JavaScript -->
                </tbody>
            </table>
            
            <!-- Enhanced Loading Spinner -->
            <div id="loading-spinner" class="loading-spinner">
                <div class="spinner-container">
                    <div class="spinner"></div>
                    <div class="loading-text">
                        <p>جاري تحميل البيانات...</p>
                        <span class="loading-dots">...</span>
                    </div>
                </div>
            </div>
            
            <!-- Empty State -->
            <div id="empty-state" class="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم العثور على أي طلبات تطابق معايير البحث</p>
                <button class="refresh-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث البيانات
                </button>
            </div>
        </div>
    </div>
    
    <!-- Enhanced Fixed Navbar -->
    <div class="fixed-navbar">
        <div class="navbar-content">
            <div class="selection-info">
                <span id="selected-count">0</span>
                <span>عنصر محدد</span>
            </div>
            <div class="navbar-buttons">
                <button onclick="printSelectedReceipts()" class="action-btn print-btn" disabled>
                    <i class="fas fa-print"></i>
                    <span class="btn-text">طباعة الفواتير</span>
                </button>
                <button onclick="viewSelectedOrders()" class="action-btn view-btn" disabled>
                    <i class="fas fa-eye"></i>
                    <span class="btn-text">عرض التفاصيل</span>
                </button>
                <button onclick="deleteSelectedOrders()" class="action-btn delete-btn" disabled>
                    <i class="fas fa-trash-alt"></i>
                    <span class="btn-text">حذف المحدد</span>
                </button>
                <button onclick="exportSelectedToExcel()" class="action-btn export-btn" disabled>
                    <i class="fas fa-file-excel"></i>
                    <span class="btn-text">تصدير المحدد</span>
                </button>
            </div>
            <div class="quick-actions">
                <button onclick="refreshData()" class="quick-btn" title="تحديث البيانات">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button onclick="toggleFullscreen()" class="quick-btn" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div id="orderDetailsModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2>تفاصيل الفاتورة</h2><br>
        <table>
            <thead>
                <tr>
                    <th>اختيار</th>
                    <th>معرف البيع</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>تكلفة القطعة</th>
                    <th>سعر القطعة</th>
                    <th>مجموع السعر</th>
                    <th>مكسب الصنف</th>
                    <th>الحالة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="order-details-body">
                <!-- سيتم ملء هذه القائمة باستخدام JavaScript -->
            </tbody>
        </table>
        <button class="action-btn" style="background-color:#dc3545;color:white;" onclick="deleteSelectedItems()">حذف الأصناف المحددة</button>
    </div>
</div>

<!-- Orders Modal -->
<div id="userOrdersModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeUserOrdersModal()">&times;</span>
        <h2>طلبات المستخدم</h2>
        <div id="userOrdersSummary" class="summary-bar">
            <div class="summary-item frame">
                <span id="userTotalQuantity">إجمالي الكميات: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalAmount">إجمالي مجموع سعر الأصناف: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalPaid">إجمالي المدفوع: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalRemaining">الباقي: 0</span>
            </div>
        </div>
        <table id="userOrdersTable" class="display">
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>اسم الصنف</th>
                    <th>حالة الطلب</th>
                    <th>عدد الأصناف</th>
                    <th>المجموع</th>
                    <th>تاريخ الطلب</th>
                </tr>
            </thead>
            <tbody id="userOrdersTableBody">
                <!-- Orders will be populated here -->
            </tbody>
        </table>
    </div>
</div>

<audio id="newOrderSound" src="Ques.wav" preload="auto"></audio>
<input type="hidden" id="store-id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">

<script>
    let initialLoad = true;
    let previousOrderCounts = {};
    let isFiltering = false;
    let newOrderPlayed = false;
    let suppressSound = true; // Changed to true by default to suppress sound initially
    let firstSSE = true;
    let audioEnabled = false;
    let modalOpen = false;
    let lastUpdate = 0;
    let sseReconnectTimer;
    let currentTab = 'sales'; // Default tab is sales
    let sortDirection = 'asc';
    let currentSortColumn = '';
    let searchFormCollapsed = false;

    // دالة لتبديل عرض نموذج البحث
    function toggleSearchForm() {
        const searchForm = document.querySelector('.search-form');
        const toggleBtn = document.querySelector('.toggle-search-btn i');
        
        searchFormCollapsed = !searchFormCollapsed;
        
        if (searchFormCollapsed) {
            searchForm.style.display = 'none';
            toggleBtn.className = 'fas fa-chevron-up';
        } else {
            searchForm.style.display = 'block';
            toggleBtn.className = 'fas fa-chevron-down';
        }
    }

    // دالة لتحديث عداد العناصر المحددة
    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
        const count = selectedCheckboxes.length;
        const selectedCountElement = document.getElementById('selected-count');
        const actionButtons = document.querySelectorAll('.fixed-navbar .action-btn');
        
        selectedCountElement.textContent = count;
        
        // تفعيل/إلغاء تفعيل الأزرار بناءً على عدد العناصر المحددة
        actionButtons.forEach(btn => {
            btn.disabled = count === 0;
        });
    }

    // دالة لتحديد/إلغاء تحديد جميع العناصر
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');
        
        orderCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        updateSelectedCount();
    }

    // دالة لتحديد جميع الطلبات
    function selectAllOrders() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        selectAllCheckbox.checked = true;
        toggleSelectAll();
    }

    // دالة لتحديث الإحصائيات
    function updateStatistics(data) {
        let totalOrders = data.length;
        let totalAmount = 0;
        let pendingOrders = 0;
        let confirmedOrders = 0;
        
        data.forEach(order => {
            totalAmount += parseFloat(order.total_amount || 0);
            if (order.status === 'pending') pendingOrders++;
            else if (order.status === 'confirmed') confirmedOrders++;
        });
        
        document.getElementById('total-orders').textContent = totalOrders;
        document.getElementById('total-amount').textContent = totalAmount.toFixed(2) + ' ج.م';
        document.getElementById('pending-orders').textContent = pendingOrders;
        document.getElementById('confirmed-orders').textContent = confirmedOrders;
        
        // تحديث عدادات التبويبات
        if (currentTab === 'sales') {
            document.getElementById('sales-count').textContent = totalOrders;
        } else {
            document.getElementById('returns-count').textContent = totalOrders;
        }
    }

    // دالة لتحديث عنوان الجدول
    function updateTableTitle() {
        const tableTitle = document.getElementById('table-title');
        const tableSubtitle = document.getElementById('table-subtitle');
        
        if (currentTab === 'sales') {
            tableTitle.textContent = 'قائمة المبيعات';
            tableSubtitle.textContent = 'عرض جميع عمليات البيع';
        } else {
            tableTitle.textContent = 'قائمة المرتجعات';
            tableSubtitle.textContent = 'عرض جميع عمليات الإرجاع';
        }
    }

    // دالة لتحديث البيانات
    function refreshData() {
        const refreshBtn = document.querySelector('.refresh-btn i');
        if (refreshBtn) {
            refreshBtn.classList.add('fa-spin');
            setTimeout(() => {
                refreshBtn.classList.remove('fa-spin');
            }, 1000);
        }
        fetchOrders(false);
    }

    // دالة لتحديث عناوين الأعمدة بناءً على نوع التبويب
    function updateTableHeaders() {
        // إضافة/إزالة كلاس للجدول بناءً على النوع
        const tableWrapper = document.querySelector('.table-wrapper');
        if (currentTab === 'returns') {
            tableWrapper.classList.add('returns-mode');
        } else {
            tableWrapper.classList.remove('returns-mode');
        }
        
        // تحديث عرض أعمدة المكسب
        const profitHeaders = document.querySelectorAll('.profit-column');
        profitHeaders.forEach(header => {
            if (currentTab === 'sales') {
                header.style.display = 'table-cell';
            } else {
                header.style.display = 'none';
            }
        });
        
        // تحديث عناوين الأعمدة في نافذة تفاصيل الفاتورة
        const modalCostHeader = document.querySelector('#orderDetailsModal thead th:nth-child(5)'); // عمود التكلفة
        const modalProfitHeader = document.querySelector('#orderDetailsModal thead th:nth-child(8)'); // عمود المكسب
        
        if (modalCostHeader && modalProfitHeader) {
            if (currentTab === 'sales') {
                modalCostHeader.style.display = 'table-cell';
                modalProfitHeader.style.display = 'table-cell';
                modalCostHeader.textContent = 'تكلفة القطعة';
                modalProfitHeader.textContent = 'مكسب الصنف';
            } else {
                modalCostHeader.style.display = 'none';
                modalProfitHeader.style.display = 'none';
            }
        }
    }

    // تبديل التبويبات والبيانات المعروضة
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث عناوين الأعمدة عند تحميل الصفحة
        updateTableHeaders();
        
        // تحديث عنوان الجدول
        updateTableTitle();
        
        // تحديث عداد العناصر المحددة
        updateSelectedCount();
        
        // تحميل البيانات عند تحميل ا��صفحة
        fetchOrders(false);
        
        const tabButtons = document.querySelectorAll('.tab-btn');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // إزالة الكلاس active من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // إضافة الكلاس active للزر المضغوط
                this.classList.add('active');
                
                // تغيير التبويب الحالي
                currentTab = this.getAttribute('data-tab');
                
                // إعادة تعيين القيم وجلب البيانات الجديدة
                document.getElementById('search-name').value = '';
                document.getElementById('search-phone').value = '';
                document.getElementById('search-barcode').value = '';
                document.getElementById('status-filter').value = '';
                
                // تعيين تاريخ البداية للشهر الحالي
                const now = new Date();
                const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                document.getElementById('start-date').value = firstDay.toISOString().split('T')[0];
                document.getElementById('end-date').value = '';
                
                // تحديث عرض عناوين الأعمدة بناءً على نوع التبويب
                updateTableHeaders();
                
                // تحديث عنوان الجدول
                updateTableTitle();
                
                // إعادة تعيين عداد العناصر المحددة
                updateSelectedCount();
                
                // جلب البيانات الجديدة بناء على التبويب الحالي
                suppressSound = true;
                fetchOrders(false);
            });
        });
    });

    document.getElementById('status-filter').addEventListener('change', () => {
        const statusValue = document.getElementById('status-filter').value;
        if (statusValue === "") {
            // When switching back to "All statuses", set the flag to ignore sound
            suppressSound = true;
            isFiltering = false;
            fetchOrders(false);
        } else {
            isFiltering = true;
            fetchOrders(false);
        }
    });

    document.getElementById('start-date').addEventListener('change', () => {
        suppressSound = true;
        fetchOrders(false);
    });

    document.getElementById('end-date').addEventListener('change', () => {
        suppressSound = true;
        fetchOrders(false);
    });

    document.getElementById('search-name').addEventListener('input', () => {
        suppressSound = true;
    });

    document.getElementById('search-phone').addEventListener('input', () => {
        suppressSound = true;
        fetchOrders(false);
    });

    function searchOrders() {
        suppressSound = true;
        fetchOrders(false);
    }

    function clearDates() {
        document.getElementById('start-date').value = '';
        document.getElementById('end-date').value = '';
        suppressSound = true;
        fetchOrders(false);
    }

    function fetchOrders(playSound = true) {
        // إظهار مؤشر التحميل
        const loadingSpinner = document.getElementById('loading-spinner');
        const ordersTableBody = document.getElementById('orders-table-body');
        
        loadingSpinner.classList.add('active');
        
        // حفظ حالة الاختيار الحالية قبل مسح الجدول
        let selectedOrders = {};
        document.querySelectorAll('.order-checkbox:checked').forEach(chk => {
            const key = chk.getAttribute('data-plain-account-id') + '_' + chk.getAttribute('data-order-date');
            selectedOrders[key] = true;
        });
        
        const now = Date.now();
        if (now - lastUpdate < 2000) {
            loadingSpinner.classList.remove('active');
            return; // Prevent updates faster than 2 sec
        }
        lastUpdate = now;
        
        const searchName = document.getElementById('search-name').value;
        const searchPhone = document.getElementById('search-phone').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        const statusFilter = document.getElementById('status-filter').value;
        const displayMode = document.getElementById('display-mode').value;
        const searchBarcode = document.getElementById('search-barcode').value;
        const storeId = '<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>';

        const params = new URLSearchParams();
        if (searchName) params.append('search_name', searchName);
        if (searchPhone) params.append('search_phone', searchPhone);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (statusFilter) params.append('status', statusFilter);
        if (searchBarcode) params.append('search_barcode', searchBarcode);
        if (storeId) params.append('store_id', storeId);
        if (displayMode) params.append('display_mode', displayMode);
        // إضافة معلمة لتحديد نوع البيانات (مبيعات أو مرتجعات)
        params.append('data_type', currentTab);

        fetch(`get_orders.php?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                const ordersTableBody = document.getElementById('orders-table-body');
                const previousOrderCount = ordersTableBody.children.length;
                ordersTableBody.innerHTML = '';

                const isFilterActive = statusFilter !== "" || startDate !== "" || endDate !== "" || searchName !== "" || searchPhone !== "";

                let hasRealChanges = false;
                data.forEach(order => {
                    const encryptedAccountId = order.encrypted_account_id;
                    const plainAccountId = order.account_id;
                    const key = plainAccountId + '_' + order.order_date;
                    const isChecked = selectedOrders[key] ? 'checked' : '';
                    const status = order.status === 'pending' 
                        ? '<span class="status-frame pending">انتظار</span>' 
                        : order.status === 'delayed' 
                            ? '<span class="status-frame delayed">مؤجل</span>' 
                            : '<span class="status-frame confirmed">مؤكد</span>';
                    const customerName = order.customer_name || '-';
                    const profit = currentTab === 'sales' ? (order.total_profit || 0) : '';
                    const profitCell = currentTab === 'sales' ? `<td class="profit-column">${profit}</td>` : '<td class="profit-column" style="display: none;"></td>';
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <input type="checkbox" 
                                class="order-checkbox" 
                                data-plain-account-id="${plainAccountId}" 
                                data-encrypted-account-id="${encryptedAccountId}" 
                                data-order-date="${order.order_date}" ${isChecked}
                                onchange="updateSelectedCount()">
                        </td>
                        <td>
                            <i class="fas fa-print printer-icon" style="cursor:pointer; font-size: 18px; color: #444;" onclick='showInvoiceOptions("${encryptedAccountId}", "${order.order_date}")'></i>
                        </td>
                        <td class="username" data-account-id="${plainAccountId}" onclick='showUserOrders("${plainAccountId}")'>${order.username}</td>
                        <td>${customerName}</td>
                        <td>${order.phone}</td>
                        <td>${status}</td>
                        <td>${order.total_quantity}</td>
                        <td>${order.total_amount}</td>
                        ${profitCell}
                        <td>${order.order_date || '-'}</td>
                        <td>
                            <i class="fas fa-eye eye-icon" style="cursor:pointer; font-size: 18px; color: #007bff;" onclick='showOrderDetails("${encryptedAccountId}", "${order.order_date}", false)'></i>
                            <i class="fas fa-trash-alt delete-icon icon-spacing" style="cursor:pointer; font-size: 18px; color: #dc3545; margin-right: 8px;" onclick='deleteOrder("${encryptedAccountId}", "${order.order_date}", this)'></i>
                        </td>
                    `;
                    ordersTableBody.appendChild(row);

                    const previousCount = previousOrderCounts[order.order_date] || 0;
                    if (order.total_quantity > previousCount) {
                        hasRealChanges = true;
                    }
                    previousOrderCounts[order.order_date] = order.total_quantity;
                });

                if (hasRealChanges && !isFilterActive && !suppressSound && !modalOpen && playSound) {
                    window.playNewOrderSound();
                }

                initialLoad = false;
                isFiltering = false;
                suppressSound = false;
                
                // تحديث عرض الأعمدة بعد تحديث البيانات
                updateTableHeaders();
                
                // تحديث الإحصائيات
                updateStatistics(data);
                
                // تحديث عنوان الجدول
                updateTableTitle();
                
                // إخفاء مؤشر التحميل
                loadingSpinner.classList.remove('active');
                
                // إظهار/إخفاء حالة فارغة
                const emptyState = document.getElementById('empty-state');
                if (data.length === 0) {
                    emptyState.style.display = 'block';
                    ordersTableBody.style.display = 'none';
                } else {
                    emptyState.style.display = 'none';
                    ordersTableBody.style.display = 'table-row-group';
                }
            })
            .catch(error => {
                console.error('Error fetching orders:', error);
                // إخفاء مؤشر التحميل في حالة الخطأ أيضاً
                loadingSpinner.classList.remove('active');
            });
    }

    function showOrderDetails(encryptedAccountId, orderDate = null, playSound = true) {
        modalOpen = true;

        const url = orderDate && orderDate !== 'undefined'
            ? `get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}&data_type=${currentTab}`
            : `get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}&data_type=${currentTab}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (!data || data.length === 0) {
                    console.error('Error fetching order details: No data received');
                    return;
                }

                const orderDetailsBody = document.getElementById('order-details-body');
                orderDetailsBody.innerHTML = '';
                orderDetailsBody.dataset.accountId = encryptedAccountId;
                orderDetailsBody.dataset.orderDate = orderDate || '';
                orderDetailsBody.dataset.dataType = currentTab; // تخزين نوع البيانات للاستخدام لاحقًا

                data.forEach(item => {
                    const status = item.status === 'pending' 
                        ? '<span class="status-frame pending">انتظار</span>' 
                        : item.status === 'delayed' 
                            ? '<span class="status-frame delayed">مؤجل</span>' 
                            : '<span class="status-frame confirmed">مؤكد</span>';
                    
                    // إظهار التكلفة والمكسب فقط في حالة المبيعات
                    const cost = currentTab === 'sales' ? (item.cost || 0) : '';
                    const profit = currentTab === 'sales' ? (item.profit || 0) : '';
                    const costCell = currentTab === 'sales' ? `<td class="cost">${cost}</td>` : '<td class="cost" style="display: none;"></td>';
                    const profitCell = currentTab === 'sales' ? `<td class="profit">${profit}</td>` : '<td class="profit" style="display: none;"></td>';
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><input type="checkbox" class="order-detail-checkbox" data-sale-id="${item.sale_id}"></td>
                        <td class="sale_id">${item.sale_id}</td>
                        <td class="name">${item.name}</td>
                        <td class="quantity" contenteditable="true" onkeypress="handleQuantityKeyPress(event, this, ${item.sale_id}, '${item.status}', ${item.item_id})">${item.quantity}</td>
                        ${costCell}
                        <td class="price">${item.price}</td>
                        <td class="total_amount">${item.total_amount}</td>
                        ${profitCell}
                        <td>${status}</td>
                        <td>
                            <i class="fas fa-trash-alt" style="cursor:pointer; font-size: 18px; color: #dc3545;" onclick='deleteOrderItem(${item.sale_id}, this)'></i>
                        </td>
                    `;
                    orderDetailsBody.appendChild(row);
                });
                
                // حساب إجمالي المكسب وإضافته للنافذة
                if (currentTab === 'sales') {
                    let totalProfit = 0;
                    data.forEach(item => {
                        totalProfit += parseFloat(item.profit || 0);
                    });
                    
                    // إضافة أو تحديث عنصر إجمالي المكسب
                    let profitSummary = document.getElementById('profit-summary');
                    if (!profitSummary) {
                        profitSummary = document.createElement('div');
                        profitSummary.id = 'profit-summary';
                        profitSummary.style.cssText = 'margin: 15px 0; padding: 10px; background-color: #e8f5e8; border-radius: 5px; text-align: center; font-weight: bold; color: #2e7d32;';
                        
                        const modalContent = document.querySelector('#orderDetailsModal .modal-content');
                        const table = modalContent.querySelector('table');
                        modalContent.insertBefore(profitSummary, table.nextSibling);
                    }
                    profitSummary.innerHTML = `إجمالي المكسب: ${totalProfit.toFixed(2)}`;
                    profitSummary.style.display = 'block';
                } else {
                    // إخفاء عنصر إجمالي المكسب في حالة المرتجعات
                    const profitSummary = document.getElementById('profit-summary');
                    if (profitSummary) {
                        profitSummary.style.display = 'none';
                    }
                }

                const modal = document.getElementById('orderDetailsModal');
                modal.classList.add('active');
            })
            .catch(error => console.error('Error fetching order details:', error));
    }

    function markAsPaid(button, saleId, encryptedAccountId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، ادفعها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const row = button.closest('tr');
                const quantity = row.querySelector('.quantity').textContent.trim();
                const price = row.querySelector('.price').textContent.trim();
                const totalAmount = row.querySelector('.total_amount').textContent.trim();
                const collected = row.querySelector('.collected').textContent.trim();
                const remaining = totalAmount - collected;
                const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

                if (remaining <= 0) {
                    console.error('This item has already been paid for.');
                    return;
                }

                fetch('mark_as_paid.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ quantity: quantity, price: price, total_amount: totalAmount, sale_id: saleId })
                }).then(response => response.json())
                  .then(data => {
                      if (data.status === 'success') {
                          showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                          fetchOrders(); // Refresh orders table
                      }
                  })
                  .catch(error => console.error('Error marking item as paid:', error));
            }
        });
    }

    function markAsDelayed(element, saleId, encryptedAccountId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، أجله!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

                fetch(`mark_as_delayed.php?sale_id=${saleId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                            fetchOrders(); // Refresh orders table
                        }
                    })
                    .catch(error => console.error('Error marking item as delayed:', error));
            }
        });
    }

    function closeModal() {
        modalOpen = false;
        document.getElementById('orderDetailsModal').classList.remove('active');
    }

    window.onclick = function(event) {
        const modal = document.getElementById('orderDetailsModal');
        const userOrdersModal = document.getElementById('userOrdersModal');
        if (event.target === modal) {
            closeModal();
        } else if (event.target === userOrdersModal) {
            closeUserOrdersModal();
        }
    }

    // Fetch orders on page load
    document.addEventListener('DOMContentLoaded', () => {
        fetchOrders(false);
        initialLoad = false; // Set initialLoad to false after the first fetch
    });

    // Use Server-Sent Events (SSE) to update orders continuously
    if (typeof(EventSource) !== "undefined") {
        const storeId = '<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>';
        const setupSSE = () => {
            const source = new EventSource(`sse_orders.php?store_id=${storeId}`);
            source.onmessage = (e) => {
                if (firstSSE) {
                    firstSSE = false;
                    return;
                }
                // Disable sound by passing false
                fetchOrders(false);
            };
            source.onerror = () => {
                source.close();
                clearTimeout(sseReconnectTimer);
                sseReconnectTimer = setTimeout(setupSSE, 5000);
            };
        };
        setupSSE();
    } else {
        console.error("Your browser does not support Server-Sent Events.");
    }

    function deleteOrderFromModal() {
        const saleId = document.querySelector('#order-details-body .sale_id').textContent.trim();
        const encryptedAccountId = '<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>';
        deleteOrder(saleId, document.querySelector(`#orders-table-body tr[data-sale-id="${saleId}"]`), true);
        setTimeout(() => showOrderDetails(encryptedAccountId, saleId, false), 500); // Refresh order details after a short delay
    }

    function deleteOrder(accountId, orderDate, element, fromModal = false) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                // استخدام نقطة النهاية المناسبة بناءً على التبويب الحالي
                const endpoint = currentTab === 'returns' ? 'delete_full_return.php' : 'delete_full_order.php';
                
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ account_id: accountId, order_date: orderDate })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إزالة الصف الخاص بالطلب المحذوف من واجهة المستخدم
                        if (element) {
                            element.closest('tr').remove();
                        }
                        // إذا كان الحذف من داخل المودال، تحقق من بقاء أصناف أخرى
                        if (fromModal) {
                            const orderDetailsBody = document.getElementById('order-details-body');
                            if (orderDetailsBody.children.length === 0) {
                                closeModal(); // إغلاق المودال إذا لم يتبقَ أي صنف
                            }
                        }
                        // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                        fetchOrders();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    }

    function payAllItems() {
        const orderDetailsBody = document.getElementById('order-details-body');
        const rows = orderDetailsBody.querySelectorAll('tr');
        const saleIds = [];
        const orderDate = orderDetailsBody.dataset.orderDate; // Get the order date
        const encryptedAccountId = orderDetailsBody.dataset.accountId; // Get the account ID of the order

        rows.forEach(row => {
            const saleId = row.querySelector('.sale_id').textContent.trim();
            const collected = row.querySelector('.collected').textContent.trim();

            if (collected == 0) {
                saleIds.push(saleId);
            }
        });

        if (saleIds.length > 0) {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "لن تتمكن من التراجع عن هذا!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'نعم، ادفع الكل!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('pay_all_items.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ sale_ids: saleIds, account_id: encryptedAccountId, order_date: orderDate }) // Send account ID and order date
                    }).then(response => response.json())
                      .then(data => {
                          if (data.status === 'success') {
                              // Update item statuses in the modal
                              rows.forEach(row => {
                                  const collectedCell = row.querySelector('.collected');
                                  const statusCell = row.querySelector('.status-frame');
                                  if (collectedCell.textContent.trim() == 0) {
                                      collectedCell.textContent = row.querySelector('.total_amount').textContent.trim();
                                      statusCell.className = 'status-frame confirmed';
                                      statusCell.textContent = 'مؤكد';
                                  }
                              });
                              fetchOrders(); // Refresh orders table
                              showOrderDetails(encryptedAccountId, orderDate, false); // Fetch order details with account ID and order date
                          } else {
                              alert(data.message);
                          }
                      })
                      .catch(error => console.error('Error marking all items as paid:', error));
                }
            });
        }
    }

    function deleteOrderItem(saleId, element) {
        const dataType = document.getElementById('order-details-body').dataset.dataType || 'sales';
        const endpoint = dataType === 'returns' ? 'delete_return.php' : 'delete_order.php';

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ order_id: saleId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إزالة الصف الخاص بالصنف المحذوف من واجهة المستخدم
                        if (element) {
                            element.closest('tr').remove();
                        }
                        // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                        fetchOrders();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    }

    function deleteSelectedItems() {
        const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
        if (checkboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يرجى اختيار عنصر واحد على الأقل للحذف'
            });
            return;
        }

        const dataType = document.getElementById('order-details-body').dataset.dataType || 'sales';
        const saleIds = Array.from(checkboxes).map(checkbox => checkbox.getAttribute('data-sale-id'));

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const endpoint = dataType === 'returns' ? 'delete_multiple_returns.php' : 'delete_multiple_orders.php';
                
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ids: saleIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // حذف الصفوف من الجدول
                        saleIds.forEach(id => {
                            const checkbox = document.querySelector(`.order-detail-checkbox[data-sale-id="${id}"]`);
                            if (checkbox) {
                                const row = checkbox.closest('tr');
                                if (row) row.remove();
                            }
                        });
                        
                        // تحديث القوائم الرئيسية
                        fetchOrders();
                        
                        Swal.fire(
                            'تم الحذف!',
                            'تم حذف العناصر المحددة بنجاح.',
                            'success'
                        );
                    } else {
                        Swal.fire(
                            'خطأ!',
                            data.message || 'حدث خطأ أثناء الحذف.',
                            'error'
                        );
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire(
                        'خطأ!',
                        'حدث خطأ أثناء الاتصال بالخادم.',
                        'error'
                    );
                });
            }
        });
    }

    // New function to pay only selected items
    function paySelectedItems() {
        const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
        if (checkboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'اختر صنف واحد على الأقل للدفع'
            });
            return;
        }
        let saleIds = [];
        // Changed index from 8 to 9 for status cell
        for (const chk of checkboxes) {
            const row = chk.closest('tr');
            const statusText = row.cells[9].innerText.trim();
            if (statusText !== 'انتظار' && statusText !== 'مؤجل') {
                Swal.fire({
                    icon: 'warning',
                    title: 'تنبيه',
                    text: 'يمكن دفع الأصناف التي حالتها انتظار أو مؤجل فقط'
                });
                return;
            }
            saleIds.push(chk.getAttribute('data-sale-id'));
        }
        const orderDetailsBody = document.getElementById('order-details-body');
        const orderDate = orderDetailsBody.dataset.orderDate;
        const encryptedAccountId = orderDetailsBody.dataset.accountId;
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، ادفعها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('pay_selected_items.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        sale_ids: saleIds,
                        account_id: encryptedAccountId,
                        order_date: orderDate
                    })
                }).then(response => response.json())
                  .then(data => {
                      if (data.status === 'success') {
                          Swal.fire({
                              icon: 'success',
                              title: 'نجاح',
                              text: 'تم الدفع للأصناف المحددة'
                          });
                          showOrderDetails(encryptedAccountId, orderDate, false);
                          fetchOrders();
                      } else {
                          Swal.fire({
                              icon: 'error',
                              title: 'خطأ',
                              text: data.message
                          });
                      }
                  })
                  .catch(error => console.error('Error:', error));
            }
        });
    }

    function handleReturnedKeyPress(event, element, saleId) {
        if (event.key === 'Enter') {
            event.preventDefault();
            updateReturned(element, saleId);
        }
    }

    function updateReturned(element, saleId) {
        const newVal = parseFloat(element.textContent.trim());
        const oldVal = parseFloat(element.dataset.oldReturned);
        if (isNaN(newVal)) {
            alert('القيمة غير صالحة');
            element.textContent = oldVal;
            return;
        }
        fetch('update_returned.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                sale_id: saleId,
                new_returned: newVal
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update the oldReturned data attribute to newVal on success
                element.dataset.oldReturned = newVal;
                // Optionally, refresh order details
                const orderDate = document.querySelector('#order-details-body').dataset.orderDate;
                const encryptedAccountId = document.querySelector('#order-details-body').dataset.accountId;
                showOrderDetails(encryptedAccountId, orderDate, false);
                fetchOrders();
            } else {
                alert(data.message);
                element.textContent = oldVal;
            }
        })
        .catch(error => {
            console.error('Error updating returned:', error);
            element.textContent = oldVal;
        });
    }

    // Function to show invoice printing options
    function showInvoiceOptions(encryptedAccountId, orderDate) {
        Swal.fire({
            title: 'خيارات الطباعة',
            html: `
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <button class="swal2-confirm swal2-styled" onclick="printInvoice('${encryptedAccountId}', '${orderDate}', 'regular')">
                        طباعة فاتورة عادية
                    </button>
                    <button class="swal2-confirm swal2-styled" onclick="printInvoice('${encryptedAccountId}', '${orderDate}', 'thermal')">
                        طباعة فاتورة حرارية
                    </button>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'إلغاء'
        });
    }

    // Function to handle invoice printing
    function printInvoice(encryptedAccountId, orderDate, type) {
        let url = '';
        if (type === 'regular') {
            url = `print_invoice.php?account_id=${encryptedAccountId}&order_date=${orderDate}`;
        } else if (type === 'thermal') {
            url = `print_thermal_invoice.php?account_id=${encryptedAccountId}&order_date=${orderDate}`;
        }
        
        if (url) {
            window.open(url, '_blank');
            Swal.close();
        }
    }
</script>
<script src="view_selected_orders.js" defer></script>

<?php
$conn->close();
?>
</body>
</html>
