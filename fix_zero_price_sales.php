<?php
require_once 'db_connection.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><title>إصلاح أسعار المبيعات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Cairo', sans-serif; padding: 20px; }</style>";
echo "</head><body>";
echo "<div class='container'>";
echo "<h1><i class='fas fa-wrench'></i> أداة إصلاح أسعار المبيعات الصفرية</h1>";

// بدء المعاملة لضمان سلامة البيانات
$conn->begin_transaction();

try {
    // 1. البحث عن جميع سجلات المبيعات التي سعرها صفر
    $stmt_select = $conn->prepare("SELECT sale_id, item_id, quantity FROM sales WHERE price = 0");
    $stmt_select->execute();
    $result_select = $stmt_select->get_result();
    $sales_to_fix = $result_select->fetch_all(MYSQLI_ASSOC);
    $stmt_select->close();

    if (empty($sales_to_fix)) {
        echo "<div class='alert alert-success'>لم يتم العثور على سجلات مبيعات بأسعار صفرية. كل شيء على ما يرام!</div>";
        $conn->rollback(); // لا توجد تغييرات، لذا يمكن التراجع
        exit;
    }

    echo "<p class='alert alert-info'>تم العثور على " . count($sales_to_fix) . " سجل لإصلاحه.</p>";
    echo "<ul class='list-group'>";

    // 2. تحضير الاستعلامات اللازمة
    $stmt_get_price = $conn->prepare("SELECT price FROM items WHERE item_id = ?");
    $stmt_update_sale = $conn->prepare("UPDATE sales SET price = ?, total_amount = ? WHERE sale_id = ?");

    $updated_count = 0;

    // 3. المرور على كل سجل لإصلاحه
    foreach ($sales_to_fix as $sale) {
        $sale_id = $sale['sale_id'];
        $item_id = $sale['item_id'];
        $quantity = $sale['quantity'];

        echo "<li class='list-group-item'>معالجة سجل البيع رقم: $sale_id (الصنف: $item_id)... ";

        // 4. جلب السعر الصحيح من جدول الأصناف
        $stmt_get_price->bind_param("i", $item_id);
        $stmt_get_price->execute();
        $result_price = $stmt_get_price->get_result();
        $item_data = $result_price->fetch_assoc();

        if ($item_data && $item_data['price'] > 0) {
            $correct_price = $item_data['price'];
            // إعادة حساب الإجمالي بناءً على السعر الصحيح
            $new_total_amount = $correct_price * $quantity;

            // 5. تحديث سجل البيع
            $stmt_update_sale->bind_param("ddi", $correct_price, $new_total_amount, $sale_id);
            $stmt_update_sale->execute();

            if ($stmt_update_sale->affected_rows > 0) {
                echo "<span class='badge bg-success'>تم التحديث بنجاح. السعر الجديد: $correct_price</span></li>";
                $updated_count++;
            } else {
                echo "<span class='badge bg-warning'>فشل التحديث أو لا حاجة للتغيير.</span></li>";
            }
        } else {
            echo "<span class='badge bg-danger'>لم يتم العثور على سعر صالح للصنف رقم: $item_id. تم التخطي.</span></li>";
        }
    }

    $stmt_get_price->close();
    $stmt_update_sale->close();

    // 6. تأكيد المعاملة إذا تمت التحديثات بنجاح
    $conn->commit();

    echo "</ul>";
    echo "<h2 class='mt-4'>ملخص</h2>";
    echo "<p class='alert alert-success'>تم تحديث $updated_count سجل بنجاح من أصل " . count($sales_to_fix) . " سجل.</p>";

} catch (Exception $e) {
    // التراجع عن المعاملة في حالة حدوث أي خطأ
    $conn->rollback();
    echo "<h2>حدث خطأ!</h2>";
    echo "<p class='alert alert-danger'>تم التراجع عن العملية. الخطأ: " . $e->getMessage() . "</p>";
}

echo "</div></body></html>";

// إغلاق الاتصال
$conn->close();
?>
