// offline_sync.js - Manages offline invoice functionality
// تحديث محسن مع دعم الوضع المظلم والتصميم المتجاوب
// الميزات الجديدة:
// - دعم كامل للوضع المظلم مع ألوان متناسقة
// - مراقبة تغيير الثيم ديناميكياً
// - تحسين موقع رمز الاتصال (تم نقله إلى الأسفل)
// - تأثيرات بصرية محسنة وانتقالات أكثر سلاسة
// - تصميم متجاوب للشاشات المختلفة

// Initialize IndexedDB database for offline storage
let db;
const DB_NAME = 'invoiceOfflineDB';
const DB_VERSION = 1;
const PENDING_STORE = 'pendingInvoices';
const STATUS_STORE = 'syncStatus';

// Initialize database
function initDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, DB_VERSION);
        
        request.onerror = event => {
            console.error("IndexedDB error:", event.target.error);
            reject("Error opening IndexedDB");
        };
        
        request.onsuccess = event => {
            db = event.target.result;
            console.log("IndexedDB initialized successfully");
            resolve(db);
        };
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            // Create object store for pending invoices
            if (!db.objectStoreNames.contains(PENDING_STORE)) {
                const store = db.createObjectStore(PENDING_STORE, { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
            
            // Create object store for sync status
            if (!db.objectStoreNames.contains(STATUS_STORE)) {
                db.createObjectStore(STATUS_STORE, { keyPath: 'key' });
            }
        };
    });
}

// Save offline status
function saveOfflineStatus(isOffline) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STATUS_STORE], 'readwrite');
        const store = transaction.objectStore(STATUS_STORE);
        
        const status = { key: 'offlineStatus', isOffline, lastUpdated: new Date().toISOString() };
        const request = store.put(status);
        
        request.onsuccess = () => resolve(true);
        request.onerror = event => reject(event.target.error);
    });
}

// Check if device is offline
function isOffline() {
    return !navigator.onLine;
}

// Add invoice to offline queue
function addInvoiceToQueue(invoiceData) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);
        
        // Add timestamp to track when it was stored
        invoiceData.timestamp = new Date().toISOString();
        const request = store.add(invoiceData);
        
        request.onsuccess = () => {
            updatePendingCount();
            resolve(true);
        };
        
        request.onerror = event => reject(event.target.error);
    });
}

// Count pending invoices
function countPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);
        
        const countRequest = store.count();
        countRequest.onsuccess = () => resolve(countRequest.result);
        countRequest.onerror = event => reject(event.target.error);
    });
}

// Get all pending invoices
function getPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);
        
        const request = store.getAll();
        
        request.onsuccess = () => resolve(request.result);
        request.onerror = event => reject(event.target.error);
    });
}

// Remove invoice from queue after successful sync
function removeInvoiceFromQueue(id) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);
        
        const request = store.delete(id);
        
        request.onsuccess = () => {
            updatePendingCount();
            resolve(true);
        };
        
        request.onerror = event => reject(event.target.error);
    });
}

// Update UI to show pending invoice count
function updatePendingCount() {
    countPendingInvoices().then(count => {
        const offlineStatus = document.getElementById('offline-status');
        if (offlineStatus) {
            const pendingBadge = offlineStatus.querySelector('.pending-badge');
            if (pendingBadge) {
                pendingBadge.textContent = count;
                pendingBadge.style.display = count > 0 ? 'inline-block' : 'none';
            }
        }
    });
}

// Synchronize one invoice
async function syncOneInvoice(invoiceData) {
    try {
        // Choose the appropriate endpoint based on invoice type
        let endpoint;
        if (invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') {
            endpoint = 'save_sale_invoice.php';
        } else if (invoiceData.type === 'sale') {
            endpoint = 'confirm_sale_invoice.php';
        } else {
            endpoint = 'confirm_invoice.php';
        }
        
        // Create form data
        const formData = new FormData();
        formData.append('store_id', invoiceData.store_id);
        formData.append('account_id', invoiceData.account_id);
        
        if (invoiceData.branch_id) {
            formData.append('branch_id', invoiceData.branch_id);
        }
        
        if (invoiceData.account_buyer_id) {
            formData.append('account_buyer_id', invoiceData.account_buyer_id);
        }
        
        formData.append('items', JSON.stringify(invoiceData.items));
        formData.append('invoice_type', invoiceData.invoice_type || invoiceData.type);
        
        // Handle images
        if (invoiceData.images && invoiceData.images.length) {
            formData.append('images', JSON.stringify(invoiceData.images));
        } else {
            formData.append('images', JSON.stringify([]));
        }
        
        // Send the invoice to the server
        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            return true;
        } else {
            throw new Error(result.message || 'Unknown error during sync');
        }
    } catch (error) {
        console.error("Sync error:", error);
        return false;
    }
}

// Try to synchronize all pending invoices
async function syncPendingInvoices() {
    if (isOffline()) {
        console.log('Still offline, cannot sync');
        return false;
    }
    
    try {
        const pendingInvoices = await getPendingInvoices();
        if (pendingInvoices.length === 0) {
            console.log('No pending invoices to sync');
            return true;
        }
        
        let successCount = 0;
        
        // Show sync in progress toast notification
        showSyncToast('جاري مزامنة ' + pendingInvoices.length + ' فواتير...', 'info');
        
        for (const invoice of pendingInvoices) {
            const success = await syncOneInvoice(invoice);
            
            if (success) {
                await removeInvoiceFromQueue(invoice.id);
                successCount++;
                showSyncToast('تم مزامنة ' + successCount + ' من ' + pendingInvoices.length + ' فواتير', 'info');
            }
        }
        
        // Show final status
        if (successCount === pendingInvoices.length) {
            showSyncToast('تمت مزامنة جميع الفواتير بنجاح', 'success');
            return true;
        } else {
            showSyncToast('تمت مزامنة ' + successCount + ' من ' + pendingInvoices.length + ' فواتير', 'warning');
            return false;
        }
    } catch (error) {
        console.error('Error syncing invoices:', error);
        showSyncToast('حدث خطأ أثناء المزامنة', 'error');
        return false;
    }
}

// Show toast notification for sync status
function showSyncToast(message, type = 'info') {
    if (typeof toastr !== 'undefined') {
        switch (type) {
            case 'success':
                toastr.success(message);
                break;
            case 'error':
                toastr.error(message);
                break;
            case 'warning':
                toastr.warning(message);
                break;
            default:
                toastr.info(message);
        }
    } else {
        console.log(message);
    }
}

// Save an invoice (works online or offline)
async function saveInvoiceWithOfflineSupport(invoiceData) {
    if (isOffline()) {
        // Offline: save to IndexedDB
        try {
            await addInvoiceToQueue(invoiceData);
            showSyncToast('تم حفظ الفاتورة محلياً. ستتم المزامنة عند عودة الاتصال', 'info');
            return { 
                success: true, 
                offline: true, 
                message: 'تم حفظ الفاتورة بشكل مؤقت وسيتم مزامنتها عند عودة الاتصال'
            };
        } catch (error) {
            console.error('Error saving offline:', error);
            return { 
                success: false, 
                offline: true, 
                message: 'حدث خطأ أثناء حفظ الفاتورة محلياً'
            };
        }
    } else {
        // Online: use regular sync
        try {
            const success = await syncOneInvoice(invoiceData);
            if (success) {
                return { 
                    success: true, 
                    offline: false, 
                    message: 'تم حفظ الفاتورة بنجاح'
                };
            } else {
                throw new Error('فشل في حفظ الفاتورة على السيرفر');
            }
        } catch (error) {
            console.error('Error saving online:', error);
            
            // If online save fails, try saving offline as fallback
            try {
                await addInvoiceToQueue(invoiceData);
                return { 
                    success: true, 
                    offline: true, 
                    message: 'تعذر الاتصال بالسيرفر. تم حفظ الفاتورة محلياً وستتم المزامنة لاحقاً'
                };
            } catch (offlineError) {
                return { 
                    success: false, 
                    offline: true, 
                    message: 'فشل حفظ الفاتورة عبر الإنترنت وفشل الحفظ المحلي'
                };
            }
        }
    }
}

// Initialize the offline functionality
async function initOfflineSystem() {
    try {
        await initDB();
        
        // Set up online/offline event listeners
        window.addEventListener('online', handleOnlineStatus);
        window.addEventListener('offline', handleOfflineStatus);
        
        // Monitor theme changes for dynamic styling updates
        setupThemeObserver();
        
        // Initial status check
        if (isOffline()) {
            handleOfflineStatus();
        } else {
            handleOnlineStatus();
        }
        
        // Update UI with pending count
        updatePendingCount();
        
        return true;
    } catch (error) {
        console.error('Error initializing offline system:', error);
        return false;
    }
}

// Setup theme observer to handle dynamic theme changes
function setupThemeObserver() {
    // Watch for changes in body class to detect theme changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                // Theme changed, update offline status styling if visible
                updateOfflineStatusStyling();
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });
}

// Update offline status styling based on current theme
function updateOfflineStatusStyling() {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    const isCurrentlyOffline = offlineStatus.classList.contains('offline');
    
    // Apply appropriate styling based on current state and theme
    if (isCurrentlyOffline) {
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else if (offlineStatus.querySelector('.status-text').textContent === 'متصل') {
        // Only style if showing "connected" text
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }
    }
}

// Handle online event
function handleOnlineStatus() {
    console.log('Connection is back online');
    updateOfflineStatusUI(false);
    saveOfflineStatus(false);
    
    // Try to sync pending invoices automatically
    setTimeout(() => syncPendingInvoices(), 2000);
}

// Handle offline event
function handleOfflineStatus() {
    console.log('Connection is offline');
    updateOfflineStatusUI(true);
    saveOfflineStatus(true);
}

// Update UI to show offline status
function updateOfflineStatusUI(isOfflineNow) {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;
    
    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    if (isOfflineNow) {
        offlineStatus.classList.add('offline');
        offlineStatus.querySelector('.status-text').textContent = 'أنت غير متصل';
        
        // Apply dark mode friendly colors
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else {
        offlineStatus.classList.remove('offline');
        offlineStatus.querySelector('.status-text').textContent = 'متصل';
        
        // Apply dark mode friendly colors for online status
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }
        
        // Only show for a short time when coming back online
        setTimeout(() => {
            // Reset to default styles based on theme
            if (isDarkMode) {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            } else {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            }
            offlineStatus.querySelector('.status-text').textContent = '';
        }, 5000);
    }
    
    // Update position to be lower (to fix height issue)
    offlineStatus.style.top = '80px'; // Moved down from 15px
} 