/**
 * نظام إدارة الثيم المحسن مع منع الكاش
 * Enhanced Theme Management System with Cache Prevention
 */
document.addEventListener("DOMContentLoaded", () => {
    console.log("🎨 Theme system loading...");

    // دالة لجلب الثيم مع منع الكاش
    function fetchThemeWithCacheBuster() {
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 11);
        const url = `getAccountTheme.php?_t=${timestamp}&_cb=${randomId}`;

        return fetch(url, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Requested-With': 'XMLHttpRequest',
                'X-Cache-Buster': timestamp
            }
        });
    }

    // دالة لتطبيق الثيم
    function applyTheme(theme) {
        if (!theme || !['light', 'dark'].includes(theme.toLowerCase())) {
            theme = 'light'; // ثيم افتراضي
        }

        theme = theme.toLowerCase();
        document.documentElement.setAttribute("data-theme", theme);
        localStorage.setItem("theme", theme);

        // تحديث عنصر التبديل
        const toggle = document.getElementById("theme-toggle-link");
        if (toggle) {
            toggle.setAttribute("data-theme", theme);
        }

        // استدعاء دالة تحديث الواجهة إذا كانت متاحة
        if (typeof updateThemeLink === "function") {
            updateThemeLink();
        }

        console.log(`🎨 Theme applied: ${theme}`);
        return theme;
    }

    // جلب الثيم من الخادم
    fetchThemeWithCacheBuster()
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(accountTheme => {
            accountTheme = accountTheme.trim().toLowerCase();
            console.log(`🎨 Theme received from server: ${accountTheme}`);

            if (accountTheme && ['light', 'dark'].includes(accountTheme)) {
                applyTheme(accountTheme);
            } else {
                console.warn("🎨 Invalid theme received, using default");
                applyTheme('light');
            }
        })
        .catch(error => {
            console.error("🎨 Error fetching theme:", error);
            // في حالة الخطأ، استخدم الثيم المحفوظ محلياً أو الافتراضي
            const savedTheme = localStorage.getItem("theme");
            if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
                applyTheme(savedTheme);
                console.log("🎨 Using saved theme from localStorage");
            } else {
                applyTheme('light');
                console.log("🎨 Using default theme");
            }
        });

    // إعداد مستمع تبديل الثيم
    const toggle = document.getElementById("theme-toggle-link");
    if (toggle) {
        toggle.addEventListener("click", (e) => {
            e.preventDefault();

            const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
            const newTheme = (currentTheme === "dark") ? "light" : "dark";

            console.log(`🎨 Theme toggle: ${currentTheme} → ${newTheme}`);

            // تطبيق الثيم الجديد فوراً
            applyTheme(newTheme);

            // إرسال التحديث للخادم (اختياري - يمكن إضافته لاحقاً)
            // updateThemeOnServer(newTheme);
        });
    }

    // دالة لتحديث الثيم على الخادم (يمكن استخدامها لاحقاً)
    function updateThemeOnServer(theme) {
        const timestamp = Date.now();
        const formData = new FormData();
        formData.append('theme', theme);

        fetch(`updateTheme.php?_t=${timestamp}`, {
            method: 'POST',
            body: formData,
            cache: 'no-cache',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-Cache-Buster': timestamp
            }
        }).catch(error => {
            console.error("🎨 Error updating theme on server:", error);
        });
    }

    console.log("🎨 Theme system initialized");
});
