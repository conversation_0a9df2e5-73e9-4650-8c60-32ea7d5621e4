<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $store_id_encrypted = $_POST['store_id'];
    $buyer_encrypted = $_POST['buyer'] === 'person' ? $_POST['person_name'] : $_POST['buyer'];
    $total_amount = $_POST['total_amount'];
    $items = $_POST['items'];
    $invoice_status = $_POST['invoice_status'];
    $account_id_buyer = isset($_POST['account_buyer']) ? $_POST['account_buyer'] : null;

    $store_id = decrypt($store_id_encrypted, $key);
    $buyer = $_POST['buyer'] === 'person' ? $_POST['person_name'] : decrypt($buyer_encrypted, $key);

    $account_id = decrypt($_POST['account_id'], $key); // Decrypt the account ID from the form

    // Insert into wholesale_invoices
    $sql_invoice = "INSERT INTO wholesale_invoices (store_id, buyer, total_amount, status, account_id_buyer, account_id) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql_invoice);
    $stmt->bind_param("isssii", $store_id, $buyer, $total_amount, $invoice_status, $account_id_buyer, $account_id);
    if ($stmt->execute()) {
        $invoice_id = $stmt->insert_id;

        // Log the wholesale invoice addition action
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'add', 'wholesale_invoices', ?, ?)";
        $description = "تم إضافة فاتورة بيع بالجملة برقم $invoice_id بقيمة $total_amount للمشتري $buyer";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
        $log_stmt->execute();
        $log_stmt->close();
    } else {
        echo "Error: " . $stmt->error;
        exit();
    }
    $stmt->close();

    $success = true; // Ensure $success is initialized before any loops

    // Insert items into whosales
    $sql_whosale = "INSERT INTO whosales (invoice_id, store_id, item_id, quantity, total_amount) VALUES (?, ?, ?, ?, ?)";
    $stmt_whosale = $conn->prepare($sql_whosale);
    if (!$stmt_whosale) {
        die("Prepare whosales failed: " . $conn->error);
    }

    foreach ($items as $item_id => $item) {
        $quantity = isset($item['quantity']) ? $item['quantity'] : 0;
        $item_total_amount = isset($item['total']) ? $item['total'] : 0;

        if ($quantity <= 0 || $item_total_amount <= 0) {
            continue; // Skip invalid items
        }

        $stmt_whosale->bind_param("iiiii", $invoice_id, $store_id, $item_id, $quantity, $item_total_amount);
        $stmt_whosale->execute();
    }
    $stmt_whosale->close(); // Close the statement after the loop

    // If the invoice status is "pending", stop further processing
    if ($invoice_status === 'pending') {
        $conn->close();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة الفاتورة بنجاح.'
        ]);
        exit();
    }

    // Insert item transactions
    $sql_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'wholesale_sale', ?, ?)";
    $stmt_transaction = $conn->prepare($sql_transaction);
    if (!$stmt_transaction) {
        die("Prepare itemtransactions failed: " . $conn->error);
    }

    foreach ($items as $item_id => $item) {
        $quantity = $item['quantity'];

        $stmt_transaction->bind_param("iii", $item_id, $invoice_id, $quantity);
        $stmt_transaction->execute();

        // Deduct quantity from the original store
        $sql_deduct = "UPDATE items SET quantity = quantity - ? WHERE item_id = ?";
        $stmt_deduct = $conn->prepare($sql_deduct);
        if (!$stmt_deduct) {
            die("Prepare deduct failed: " . $conn->error);
        }
        $stmt_deduct->bind_param("ii", $quantity, $item_id);
        $stmt_deduct->execute();
        $stmt_deduct->close();
    }
    $stmt_transaction->close(); // Close the statement after the loop

    // Add quantity to the buyer's store if the buyer is a store
    if ($_POST['buyer'] !== 'person') {
        $buyer_store_id = decrypt($buyer_encrypted, $key);

        // Insert into purchase_invoices with default status 'confirmed'
        $sql_purchase_invoice = "INSERT INTO purchase_invoices (store_id, total_amount, account_id, status) VALUES (?, ?, ?, 'confirmed')";
        $stmt_purchase_invoice = $conn->prepare($sql_purchase_invoice);
        $stmt_purchase_invoice->bind_param("idi", $buyer_store_id, $total_amount, $account_id);
        if ($stmt_purchase_invoice->execute()) {
            $purchase_invoice_id = $stmt_purchase_invoice->insert_id;

            // Update the wholesale_invoices table with the purchase_invoice_id
            $sql_update_wholesale = "UPDATE wholesale_invoices SET purchase_invoice_id = ? WHERE invoice_id = ?";
            $stmt_update_wholesale = $conn->prepare($sql_update_wholesale);
            $stmt_update_wholesale->bind_param("ii", $purchase_invoice_id, $invoice_id);
            $stmt_update_wholesale->execute();
            $stmt_update_wholesale->close();
        } else {
            echo "Error: " . $stmt_purchase_invoice->error;
            exit();
        }
        $stmt_purchase_invoice->close();

        // Insert all items into purchases for the single purchase invoice
        $sql_purchase = "INSERT INTO purchases (store_id, invoice_id, item_id, quantity, time, total_amount) VALUES (?, ?, ?, ?, NOW(), ?)";
        $stmt_purchase = $conn->prepare($sql_purchase);

        // Insert item transactions for the buyer's store
        $sql_buyer_transaction = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'wholesale_purchase', ?, ?)";
        $stmt_buyer_transaction = $conn->prepare($sql_buyer_transaction);

        foreach ($items as $item_id => $item) {
            $quantity = isset($item['quantity']) ? $item['quantity'] : 0;
            $barcode = isset($item['barcode']) ? $item['barcode'] : '';
            $item_name = isset($item['name']) ? $item['name'] : '';

            if ($quantity <= 0 || empty($barcode) || empty($item_name)) {
                continue; // Skip invalid items
            }

            $stmt_purchase->bind_param("iiiii", $buyer_store_id, $purchase_invoice_id, $item_id, $quantity, $item_total_amount);
            $stmt_purchase->execute();

            // Check if the item exists in the buyer's store with the same name and barcode
            $sql_check_item = "SELECT item_id FROM items WHERE barcode = ? AND name = ? AND store_id = ?";
            $stmt_check_item = $conn->prepare($sql_check_item);
            $stmt_check_item->bind_param("ssi", $barcode, $item_name, $buyer_store_id);
            $stmt_check_item->execute();
            $stmt_check_item->store_result();

            if ($stmt_check_item->num_rows > 0) {
                $stmt_check_item->bind_result($buyer_item_id);
                $stmt_check_item->fetch();

                // Update quantity in buyer's inventory
                $sql_add_quantity = "UPDATE items SET quantity = quantity + ? WHERE item_id = ?";
                $stmt_add_quantity = $conn->prepare($sql_add_quantity);
                $stmt_add_quantity->bind_param("ii", $quantity, $buyer_item_id);
                $stmt_add_quantity->execute();
                $stmt_add_quantity->close();

                // Record the transaction for the buyer's store
                $stmt_buyer_transaction->bind_param("iii", $buyer_item_id, $purchase_invoice_id, $quantity);
                $stmt_buyer_transaction->execute();

                // Update the purchases table with the correct buyer's item_id
                $sql_update_purchase = "UPDATE purchases SET item_id = ? WHERE invoice_id = ? AND item_id = ?";
                $stmt_update_purchase = $conn->prepare($sql_update_purchase);
                $stmt_update_purchase->bind_param("iii", $buyer_item_id, $purchase_invoice_id, $item_id);
                $stmt_update_purchase->execute();
                $stmt_update_purchase->close();
            } else {
                $success = false;
                $_SESSION['message'] = 'فشل في إضافة الكمية إلى مخزن المشتري. تأكد من أن الاسم والباركود متطابقان.';
                $_SESSION['message_type'] = 'error';
            }
            $stmt_check_item->close();
        }
        $stmt_purchase->close();
        $stmt_buyer_transaction->close();
    }

    // Handle uploaded image paths: save them into wholesale_invoice_images table
    if (!empty($_POST['uploaded_images'])) {
        foreach ($_POST['uploaded_images'] as $relativePath) {
            $stmt = $conn->prepare("INSERT INTO wholesale_invoice_images (wholesale_invoice_id, img_path) VALUES (?, ?)");
            $stmt->bind_param("is", $invoice_id, $relativePath);
            $stmt->execute();
            $stmt->close();
        }
    }

    // Before sending the final response, delete the temporary JSON file
    $tempInvoicesDir = __DIR__ . '/temp_wholesale_invoices';
    $jsonFilePath = $tempInvoicesDir . "/account_{$account_id}.json";
    if (file_exists($jsonFilePath)) {
        unlink($jsonFilePath);
    }

    $conn->close();

    header('Content-Type: application/json');
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة الفاتورة بنجاح.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء إضافة الفاتورة.'
        ]);
    }
    exit();
} else {
    // Handle invalid request method
    header("Location: add_wholesale_invoice.php");
    exit();
}
?>
