/* New font imports */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

/* Light Theme (Default) */
:root {
    --color-bg: linear-gradient(to right, #f5f7fa, #c3cfe2);
    --color-fg: #333;
    --color-primary: #3f51b5;
    --color-secondary: #ffffff;
    --color-hover: rgba(63, 81, 181, 0.1);
    --color-header-text: #ffffff; /* New variable for header text color */
    --color-button-text: #ffffff; /* New variable for button text color */
}
[data-theme="dark"] {
    --color-bg: #0d1117; /* أغمق وراقي أكتر */
    --color-fg: #c9d1d9; /* لون كتابة مريح */
    --color-primary: #58a6ff; /* أزرق فاتح بيريح العين ومميز */
    --color-secondary: #161b22; /* رمادي غامق ناعم */
    --color-hover: rgba(88, 166, 255, 0.08); /* لون هوفر ناعم */
    --color-header-text: #ffffff;
    --color-button-text: #ffffff;
}

/* SweetAlert2 Dark Mode Support */
[data-theme="dark"] .swal2-popup {
    background-color: #161b22; /* Match dark mode background */
    color: #c9d1d9; /* Match dark mode text color */
    border: 1px solid #3f51b5; /* Add a subtle border */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5); /* Add shadow for depth */
}

[data-theme="dark"] .swal2-title {
    color: #e6edf3; /* Softer text color for better readability */
}

[data-theme="dark"] .swal2-html-container {
    color: #c9d1d9; /* Match dark mode text color */
}

[data-theme="dark"] .swal2-confirm {
    background-color: #3f51b5; /* Primary button color */
    color: #ffffff; /* Button text color */
    border: none;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

[data-theme="dark"] .swal2-confirm:hover {
    background-color: #303f9f; /* Darker hover effect */
}

[data-theme="dark"] .swal2-cancel {
    background-color: #e74c3c; /* Cancel button color */
    color: #ffffff; /* Button text color */
    border: none;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

[data-theme="dark"] .swal2-cancel:hover {
    background-color: #c0392b; /* Darker hover effect */
}

[data-theme="dark"] .swal2-icon {
    color: #58a6ff; /* Match dark mode primary color */
}

[data-theme="dark"] .swal2-icon.swal2-warning {
    color: #fdb813; /* Warning icon color */
}

[data-theme="dark"] .swal2-icon.swal2-error {
    color: #e74c3c; /* Error icon color */
}

[data-theme="dark"] .swal2-icon.swal2-success {
    color: #4caf50; /* Success icon color */
}

[data-theme="dark"] .swal2-icon.swal2-info {
    color: #1f6feb; /* Info icon color */
}

/* Apply Theme Variables */
body {
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
    background-color: var(--color-bg);
    color: var(--color-fg);
    
}

a, button {
    color: var(--color-fg);
}

.container {
    margin: 30px auto;
    max-width: 1200px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    padding: 30px;
    background-color: var(--color-bg);
    padding-top: 80px; /* لو ما عندك wrapper معين */

}

h2 {
    font-size: 2.8em;
    font-weight: 700;
    margin-bottom: 30px;
    margin-top: 80px; /* جديد: رفع العنوان لأسفل لضمان عدم تغطيته من الهيدير */
    color: var(--color-primary);
    text-align: center;
}

/* Dark mode for h2 */
[data-theme="dark"] h2 {
    color: var(--color-primary); /* لون فاتح للوضع الداكن */
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: none;
    margin-bottom: 30px;
    background-color: var(--color-secondary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

th {
    background-color: var(--color-primary);
    color: var(--color-header-text); /* Use header text color */
    text-transform: uppercase;
    font-size: 16px;
    padding: 15px;
    border-bottom: 2px solid #fdb813;
    font-weight: bold;
}

td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
}

tr:hover {
    background-color: #e3f2fd;
    color: #06162b;
}

.action-btn,
.add-btn,
.quantity-btn {
    background-color: var(--color-primary);
    color: var(--color-button-text); /* Use button text color */
    border: none;
    padding: 12px 25px;
    cursor: pointer;
    border-radius: 50px;
    font-size: 16px;
    margin: 20px auto;
    transition: background 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: block;
}

.action-btn:hover,
.add-btn:hover,
.quantity-btn:hover {
    background-color: #303f9f;
    transform: translateY(-2px);
}

.action-btn:active,
.add-btn:active,
.quantity-btn:active {
    transform: translateY(2px);
    box-shadow: none;
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1500; /* increased from 1050 so modal appears on top */
    inset: 0; /* replaces left, top, right, bottom */
    background-color: rgba(0, 0, 0, 0.7); /* Darker background overlay */
    backdrop-filter: blur(5px); /* Add blur effect to the background */
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease-in-out;
}

/* Use .active to display modal via flex */
.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--color-secondary);
    border: 2px solid var(--color-primary); /* Add a colored border */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Add shadow for depth */
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    max-width: 600px;
    width: 90%;
    position: relative;
    animation: slideIn 0.4s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-height: 90vh; /* allow vertical scroll */
    overflow-y: auto;
    border: 3px solid var(--color-primary); /* Add a colored border */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Add shadow for depth */
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}
.modal-content h3 {
    font-size: 1.8rem;
    color: var(--color-primary);
    margin-bottom: 20px;
}

.modal-content p {
    font-size: 1rem;
    color: var(--color-fg);
    margin-bottom: 20px;
}

.modal-content .close {
    position: absolute;
    top: 15px;
    left: 15px; /* Changed from right: 15px; */
    background-color: var(--color-primary);
    color: var(--color-header-text);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background-color 0.3s ease;
    float: none; /* override generic float */
}

.modal-content .close:hover {
    background-color: #9a67ea;
}

.modal-content .action-btn {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 1rem;
    border-radius: 8px;
    background-color: var(--color-primary);
    color: var(--color-button-text);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.modal-content .action-btn:hover {
    background-color: #9a67ea;
    transform: translateY(-2px);
}

.modal-content .action-btn:active {
    transform: translateY(2px);
    box-shadow: none;
}

#invoiceItemsModal .modal-content {
    width: 90%;
    padding: 15px;
    max-height: 80vh; /* Limit height for better usability */
    overflow-y: auto; /* Enable scrolling for long content */
    position: relative; /* Ensure proper positioning for the close icon */
}

#invoiceItemsModal table {
    width: 100%;
    border-collapse: collapse; /* Remove white borders between cells */
    margin-top: 15px;
}

#invoiceItemsModal th, #invoiceItemsModal td {
    padding: 10px;
    text-align: center;
    font-size: 14px;
}

#invoiceItemsModal img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin: 5px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

#invoiceItemsModal img:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 15px;
    }

    #invoiceItemsModal .modal-content {
        width: 95%;
        padding: 10px;
    }

    #invoiceItemsModal th, #invoiceItemsModal td {
        font-size: 12px;
        padding: 8px;
    }

    #invoiceItemsModal img {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 100%;
        padding: 10px;
    }
}

/* Responsive Modal Adjustments */
@media (max-width: 768px) {
    .modal-content h3 {
        font-size: 1.5rem; /* Reduce title font size */
    }

    .modal-content p {
        font-size: 0.9rem; /* Reduce paragraph font size */
    }

    .modal-content .action-btn {
        font-size: 0.9rem; /* Reduce button font size */
        padding: 8px 16px; /* Adjust button padding */
    }

    .modal-content .close {
        width: 25px; /* Reduce close button size */
        height: 25px;
        font-size: 1rem; /* Adjust font size */
    }
}

@media (max-width: 480px) {
    .modal-content h3 {
        font-size: 1.2rem; /* Further reduce title font size */
    }

    .modal-content p {
        font-size: 0.8rem; /* Further reduce paragraph font size */
    }

    .modal-content .action-btn {
        font-size: 0.8rem; /* Further reduce button font size */
        padding: 6px 12px; /* Further adjust button padding */
    }

    .modal-content .close {
        width: 20px; /* Further reduce close button size */
        height: 20px;
        font-size: 0.9rem; /* Further adjust font size */
    }
}

/* Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close {
    color: #888;
    float: left;
    font-size: 28px;
    font-weight: bold;
    height: 40px;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.input-field {
    width: calc(100% - 40px);
    height: 40px;
    padding: 12px 20px;
    margin: 15px auto;
    border-radius: 8px;
    border: 1px solid #ccc;
    transition: border-color 0.3s ease;
    font-size: 16px;
    box-sizing: border-box;
    display: block;
}

.input-field:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 5px rgba(63, 81, 181, 0.4);
}

/* Enhanced Search Bar Styles */
.search-bar {
    width: 70%;
    max-width: 600px;
    margin: 28px auto;
    padding: 18px 28px;
    border-radius: 25px;
    border: 2px solid #e0e0e0;
    font-size: 18px;
    font-family: 'Cairo', Arial, sans-serif;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: sticky;
    top: 190px;
    z-index: 700;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    color: #333;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Light Mode Focus State */
.search-bar:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(63, 81, 181, 0.15), 
                0 12px 35px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
    background: linear-gradient(145deg, #ffffff, #ffffff);
    transform: translateY(-2px);
}

/* Light Mode Hover State */
.search-bar:hover {
    border-color: #c0c0c0;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}

/* Light Mode Placeholder */
.search-bar::placeholder {
    color: #888;
    font-style: italic;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Dark Mode Search Bar Styles */
[data-theme="dark"] .search-bar {
    background: linear-gradient(145deg, #21262d, #161b22);
    border-color: #30363d;
    color: #c9d1d9;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark Mode Focus State */
[data-theme="dark"] .search-bar:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 
                0 12px 35px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    background: linear-gradient(145deg, #262c36, #1c2128);
    transform: translateY(-2px);
}

/* Dark Mode Hover State */
[data-theme="dark"] .search-bar:hover {
    border-color: #484f58;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.35),
                inset 0 1px 0 rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
    background: linear-gradient(145deg, #252b35, #1a1f26);
}

/* Dark Mode Placeholder */
[data-theme="dark"] .search-bar::placeholder {
    color: #7d8590;
    opacity: 0.9;
}

/* Search Bar Icon Support */
.search-container {
    position: relative;
    width: 70%;
    max-width: 600px;
    margin: 28px auto;
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    font-size: 20px;
    pointer-events: none;
    z-index: 701;
    transition: color 0.3s ease;
}

[data-theme="dark"] .search-icon {
    color: #7d8590;
}

.search-container:focus-within .search-icon {
    color: var(--color-primary);
}

/* Responsive Search Bar */
@media (max-width: 768px) {
    .search-bar {
        width: 85%;
        padding: 16px 22px;
        font-size: 16px;
        border-radius: 20px;
        top: 160px;
    }
    
    .search-container {
        width: 85%;
    }
    
    .search-icon {
        right: 18px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .search-bar {
        width: 90%;
        padding: 14px 20px;
        font-size: 15px;
        border-radius: 18px;
        top: 140px;
    }
    
    .search-container {
        width: 90%;
    }
    
    .search-icon {
        right: 16px;
        font-size: 16px;
    }
}

/* Search Bar Animation */
@keyframes searchPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(63, 81, 181, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(63, 81, 181, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(63, 81, 181, 0);
    }
}

@keyframes searchPulseDark {
    0% {
        box-shadow: 0 0 0 0 rgba(88, 166, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(88, 166, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(88, 166, 255, 0);
    }
}

.search-bar.searching {
    animation: searchPulse 1.5s infinite;
}

[data-theme="dark"] .search-bar.searching {
    animation: searchPulseDark 1.5s infinite;
}

.store-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
}

/* Store Card Styles */
.store-card {
    width: 300px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--color-secondary);
    border: 1px solid var(--color-primary); /* Add border for better visibility in dark mode */
    color: var(--color-fg);
    border-radius: 15px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2); /* Enhance shadow for better contrast */
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    text-align: center;
}

.store-card:hover {
    transform: scale(1.05);
    background-color: var(--color-hover); /* Subtle hover effect in dark mode */
    border-color: var(--color-primary); /* Maintain border color on hover */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); /* Increase shadow intensity on hover */
}

.store-card .store-icon {
    font-size: 28px;
    color: var(--color-primary);
    margin-bottom: 8px;
    transition: transform 0.3s ease, color 0.3s ease;
}

.store-card:hover .store-icon {
    transform: scale(1.1);
    color: var(--color-primary);
}

.store-card h3 {
    margin: 0;
    font-size: 20px;
    color: var(--color-primary);
}

.error-message {
    color: red;
    font-size: 14px;
}

.toggle-btn {
    background-color: #009688;
    color: white;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    border: none;
    border-radius: 50px;
    transition: background 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.toggle-btn.inactive {
    background-color: #e53935;
}

.toggle-btn:hover {
    background-color: #00796b;
}

.toggle-btn:active {
    opacity: 0.8;
}

.image-preview {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    position: relative;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.image-preview .delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: red;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.search-field {
    width: calc(100% - 40px);
    padding: 12px 20px;
    margin: 15px auto;
    border-radius: 8px;
    border: 1px solid #ccc;
    transition: border-color 0.3s ease;
    font-size: 16px;
    display: block;
}

.search-field:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 5px rgba(63, 81, 181, 0.4);
}

.checkbox-cell {
    cursor: pointer;
}

.device-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}

.device-status.red {
    background-color: #ff4d4d;
    color: white;
}

.device-status.green {
    background-color: #4caf50;
    color: white;
}

.device-status.orange {
    background-color: #ff9800;
    color: white;
}

.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}

.status.orange {
    background-color: #ff9800;
    color: white;
}

.popup-message {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    font-size: 16px;
    font-weight: bold;
}

.popup-message.success {
    background-color: #4CAF50;
    color: white;
}

.popup-message.error {
    background-color: #f44336;
    color: white;
}

.notification-icon {
    font-size: 30px;
    color: #fdb813;
    cursor: pointer;
    position: absolute;
    top: 40px;
    left: 40px;
}

.notification-icon .notification-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: red;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
}

.notification-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.notification-item {
    background-color: #f1f1f1;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-message {
    font-size: 16px;
    margin: 0;
}

.notification-time {
    font-size: 12px;
    color: #888;
}

.mark-read-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.mark-read-btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.mark-read-btn:active {
    background-color: #3e8e41;
    transform: translateY(0);
}

#sidebar.styled-sidebar {
    background: var(--color-secondary);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.2);
}

#sidebar ul li a:hover,
#sidebar ul li span:hover {
    background-color: var(--color-hover);
}

#sidebar ul li a.active {
    background-color: var(--color-primary);
    color: var(--color-fg);
}

@media (max-width: 800px) {
    body {
        font-size: 14px;
    }
    h2 {
        font-size: 2em;
    }
    .container {
        margin: 10px;
    }
    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    .input-field,
    .search-field {
        padding: 8px;
    }
    .store-card {
        width: 150px;
        padding: 15px;
    }
    .store-card h3 {
        font-size: 16px;
    }
    .image-preview img {
        width: 80px;
        height: 80px;
    }
    table {
        font-size: 12px;
    }
    th,
    td {
        padding: 8px;
    }
    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 8px 15px;
        font-size: 14px;
    }
    .input-field,
    .search-field {
        padding: 8px;
        font-size: 14px;
    }
    .store-card {
        width: 100%;
        padding: 10px;
    }
    .store-card h3 {
        font-size: 16px;
    }
    .image-preview img {
        width: 80px;
        height: 80px;
    }
    .modal-content {
        width: 90%;
        padding: 10px;
    }
    #sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    #sidebar ul {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    #sidebar ul li {
        width: 100%;
        text-align: center;
    }
    #sidebar ul li a {
        display: block;
        padding: 10px;
    }
    #sidebar ul li.branch {
        padding: 10px;
    }
    .branch-container {
        justify-content: center;
    }
    .dropdown-btn {
        width: 100%;
        text-align: center;
    }
    .sub-menu {
        width: 100%;
    }
    .sub-menu li {
        text-align: center;
    }
}

@media (max-width: 600px) {
    .container {
        margin: 10px;
        padding: 10px;
    }
    h2 {
        font-size: 1.5em;
    }
    table {
        font-size: 12px;
    }
    th,
    td {
        padding: 8px;
    }
    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 8px 15px;
        font-size: 14px;
    }
    .input-field,
    .search-field {
        padding: 8px;
        font-size: 14px;
    }
    .store-card {
        width: 100%;
        padding: 10px;
    }
    .store-card h3 {
        font-size: 16px;
    }
    .image-preview img {
        width: 80px;
        height: 80px;
    }
    .modal-content {
        width: 90%;
        padding: 10px;
    }
    #sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    #sidebar ul {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    #sidebar ul li {
        width: 100%;
        text-align: center;
    }
    #sidebar ul li a {
        display: block;
        padding: 10px;
    }
    #sidebar ul li.branch {
        padding: 10px;
    }
    .branch-container {
        justify-content: center;
    }
    .dropdown-btn {
        width: 100%;
        text-align: center;
    }
    .sub-menu {
        width: 100%;
    }
    .sub-menu li {
        text-align: center;
    }
}

/* New styles for image upload section */
.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
}

.image-upload-label {
    display: inline-block;
    background-color: var(--color-primary);
    color: var(--color-button-text);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease, transform 0.2s ease;
    margin-top: 10px;
}

.image-upload-label:hover {
    background-color: #303f9f;
    transform: translateY(-2px);
}

.image-upload-input {
    display: none; /* Hide the default file input */
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.image-preview div {
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview .remove-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    background: white;
    border-radius: 50%;
    padding: 5px;
    cursor: pointer;
    color: red;
    font-size: 14px;
}

/* Enhanced Filter Container */
.filter-container {
    display: flex;
    flex-wrap: nowrap; /* Prevent wrapping */
    gap: 20px;
    align-items: flex-end; /* Align items to the bottom */
}

.filter-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 160px; /* Minimum width for each filter item */
}

.filter-item label {
    font-size: 14px;
    font-weight: bold;
    color: var(--color-fg);
    margin-bottom: 5px;
}

.filter-item input,
.filter-item select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--color-bg);
    color: var(--color-fg);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.filter-item input:focus,
.filter-item select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 5px rgba(63, 81, 181, 0.4);
    outline: none;
}

.filter-item button {
    padding: 10px 20px;
    background-color: var(--color-primary);
    color: var(--color-button-text);
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.filter-item button:hover {
    background-color: #303f9f;
    transform: translateY(-2px);
}

.filter-item button:active {
    transform: translateY(2px);
    box-shadow: none;
}

/* Responsive Layout */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        gap: 16px;
    }

    .filter-container > * {
        width: 100%;
    }
}

@media (min-width: 769px) {
    .filter-container {
        flex-direction: row;
        align-items: flex-end;
    }

    .filter-container > * {
        flex: 1;
        min-width: 160px;
    }
}

/* Provider Frame Styles */
.provider-frame {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1em;
    text-align: center;
    color: #fff;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: box-shadow 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
    font-family: 'Cairo', sans-serif;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

.provider-frame::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    z-index: -1;
    transition: transform 0.3s ease-in-out, background-color 0.3s ease;
}

/* Default Provider Frame */
.provider-frame.default {
    background-color: #607d8b;
    border: 2px solid #546e7a;
    box-shadow: 0 4px 20px rgba(96, 125, 139, 0.5);
}

.provider-frame.default::before {
    background: linear-gradient(45deg, #546e7a, #b0bec5);
}

/* Fawry Provider Frame */
.provider-frame.fawry {
    background-color: #ffb300;
    border: 2px solid #ffa000;
    box-shadow: 0 4px 20px rgba(255, 179, 0, 0.5);
}

.provider-frame.fawry::before {
    background: linear-gradient(45deg, #ffa000, #ffd54f);
}

/* Basata Provider Frame */
.provider-frame.basata {
    background-color: #d32f2f;
    border: 2px solid #c62828;
    box-shadow: 0 4px 20px rgba(211, 47, 47, 0.5);
}

.provider-frame.basata::before {
    background: linear-gradient(45deg, #c62828, #ef9a9a);
}

/* Damen Provider Frame */
.provider-frame.damen {
    background-color: #1976d2;
    border: 2px solid #1565c0;
    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.5);
}

.provider-frame.damen::before {
    background: linear-gradient(45deg, #1565c0, #90caf9);
}

/* Hover Effect */
.provider-frame:hover::before {
    transform: scale(1.08);
}

.provider-frame:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.35);
    transform: translateY(-3px);
}

/* Focus Effect */
.provider-frame:focus {
    outline: none;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

/* Text Styling */
.provider-frame span {
    font-size: 1.2em;
    font-weight: 700;
    letter-spacing: 1px;
}

/* View Providers Modal Styles */
.providers-modal {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: var(--color-secondary);
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    margin: auto;
    text-align: center;
}

.providers-modal h2 {
    font-size: 1.8rem;
    color: var(--color-primary);
    margin-bottom: 20px;
}

.providers-list {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.providers-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid var(--color-primary);
    border-radius: 8px;
    background-color: var(--color-secondary);
    color: var(--color-fg);
    transition: background-color 0.3s ease, transform 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.providers-list li:hover {
    background-color: var(--color-hover);
    transform: translateY(-2px);
}

.provider-name {
    font-size: 1rem;
    font-weight: bold;
    color: var(--color-primary);
}

.delete-icon {
    color: #e74c3c;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.delete-icon:hover {
    color: #c0392b;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .providers-modal {
        padding: 15px;
    }

    .providers-list li {
        padding: 8px 12px;
    }

    .provider-name {
        font-size: 0.9rem;
    }

    .delete-icon {
        font-size: 1rem;
    }
}

/* Responsive Adjustments for Containers */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 20px;
        margin: 20px auto;
    }
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    h2 {
        font-size: 2em;
    }

    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .input-field,
    .search-field {
        padding: 8px;
        font-size: 14px;
    }

    .store-card {
        width: 150px;
        padding: 15px;
    }

    .store-card h3 {
        font-size: 16px;
    }

    .image-preview img {
        width: 80px;
        height: 80px;
    }

    table {
        font-size: 12px;
    }

    th,
    td {
        padding: 8px;
    }

    .modal-content {
        width: 90%;
        padding: 10px;
    }

    .filter-container {
        flex-direction: column;
        gap: 16px;
    }

    .filter-container > * {
        width: 100%;
    }
}

@media (max-width: 600px) {
    .container {
        margin: 10px;
        padding: 10px;
    }

    h2 {
        font-size: 1.5em;
    }

    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 8px 15px;
        font-size: 14px;
    }

    .input-field,
    .search-field {
        padding: 8px;
        font-size: 14px;
    }

    .store-card {
        width: 100%;
        padding: 10px;
    }

    .store-card h3 {
        font-size: 16px;
    }

    .image-preview img {
        width: 80px;
        height: 80px;
    }

    .modal-content {
        width: 100%;
        padding: 10px;
    }

    #sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    #sidebar ul {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    #sidebar ul li {
        width: 100%;
        text-align: center;
    }

    #sidebar ul li a {
        display: block;
        padding: 10px;
    }

    .filter-container {
        flex-direction: column;
        gap: 16px;
    }

    .filter-container > * {
        width: 100%;
    }
}

/* Responsive Adjustments for Tables */
@media (max-width: 768px) {
    table {
        font-size: 12px;
    }

    th,
    td {
        padding: 8px;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Responsive Adjustments for Buttons */
@media (max-width: 768px) {
    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 8px 15px;
        font-size: 14px;
    }
}

/* Responsive Adjustments for Modals */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 20px;
    }

    .modal-content h3 {
        font-size: 1.5rem;
    }

    .modal-content p {
        font-size: 0.9rem;
    }

    .modal-content .action-btn {
        font-size: 0.9rem;
        padding: 8px 16px;
    }

    .modal-content .close {
        width: 25px;
        height: 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 100%;
        padding: 15px;
    }

    .modal-content h3 {
        font-size: 1.2rem;
    }

    .modal-content p {
        font-size: 0.8rem;
    }

    .modal-content .action-btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .modal-content .close {
        width: 20px;
        height: 20px;
        font-size: 0.9rem;
    }
}

/* Responsive Adjustments for Cards */
@media (max-width: 768px) {
    .store-card {
        width: 100%;
        padding: 10px;
    }

    .store-card h3 {
        font-size: 16px;
    }
}

/* Responsive Adjustments for Sidebar */
@media (max-width: 768px) {
    #sidebar.styled-sidebar {
        width: 70%;
        border-radius: 0;
    }

    .menu-icon {
        right: 15px;
    }

    #sidebar ul li a {
        font-size: 0.8rem;
        padding: 10px 12px;
    }

    .store-name-container {
        font-size: 1em;
        padding: 10px;
    }
}

/* Responsive Adjustments for Images */
@media (max-width: 768px) {
    .image-preview img {
        width: 80px;
        height: 80px;
    }
}

/* Responsive Adjustments for Filter Containers */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        gap: 16px;
    }

    .filter-container > * {
        width: 100%;
    }
}

/* Responsive Adjustments for Provider Frames */
@media (max-width: 768px) {
    .provider-frame {
        font-size: 0.9em;
        padding: 6px 12px;
    }
}

/* Responsive Adjustments for Notifications */
@media (max-width: 768px) {
    .notification-icon {
        font-size: 24px;
    }

    .notification-icon .notification-count {
        font-size: 10px;
        padding: 1px 4px;
    }
}

/* Responsive Adjustments for Popups */
@media (max-width: 768px) {
    .popup-message {
        font-size: 14px;
        padding: 10px;
    }
}

/* إضافة التمرير الأفقي للجداول على الشاشات الصغيرة */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* لتمرير سلس على الأجهزة المحمولة */
    margin: 15px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    table {
        min-width: 600px; /* عرض الجدول الداخلي */
    }
    th, td {
        white-space: nowrap; /* منع التفاف النص في الخلايا */
        padding: 12px 15px;
        font-size: 14px;
    }
    .invoice-number {
        font-size: 14px;
    }
    .action-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .table-responsive {
        border: 1px solid rgba(0, 0, 0, 0.1); /* إضافة حدود للتمييز */
    }
    
    .container {
        padding: 15px;
    }
    
    h2 {
        font-size: 1.8rem;
    }
}

/* لتحديد عرض عمود محدد في حالة وجود محتوى طويل */
th:nth-child(1),
td:nth-child(1) {
    min-width: 120px;
}

/* Style for the status frame */
.status-frame {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
    text-align: center;
    color: white;
}

.status-frame.confirmed {
    background-color: #28a745; /* Green background for confirmed */
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5); /* Green glow */
}

.status-frame.pending {
    background-color: #dc3545; /* Red background for pending */
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5); /* Red glow */
}

/* Style for invoice number */
.invoice-number {
    font-weight: bold;
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
    padding: 5px 10px;
    border: 2px solid #007bff;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.invoice-number:hover {
    background-color: #007bff;
    color: white;
}

/* Modal styles */
#reportModal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

#reportModal .modal-content {
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
    border-radius: 10px;
    text-align: center;
    font-family: 'Cairo', sans-serif;
}

#reportModal .modal-content h2 {
    font-size: 1.8em;
    margin-bottom: 20px;
}

#reportModal .modal-content p {
    font-size: 1.2em;
    margin: 10px 0;
}

#reportModal .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

#reportModal .close:hover,
#reportModal .close:focus {
    color: white;
    text-decoration: none;
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    #reportModal .modal-content {
        width: 90%;
        padding: 15px;
    }
}

@media (max-width: 600px) {
    .modal {
        align-items: flex-start; /* position modal at the top */
        padding-top: 20px; /* add spacing from the top */
    }
    .modal-content {
        max-height: calc(100vh - 40px); /* restrict modal height */
        overflow-y: auto; /* enable scrolling if content is too long */
    }
}

/* Calculator Buttons Grid */
#calcButtons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* هنا بنعرف 5 صفوف: 4 صفوف للأرقام والعمليات، والصف الخامس للـundo و clear */
    grid-template-rows: repeat(5, auto);
    grid-template-areas:
        "seven eight nine div"
        "four five six mul"
        "one two three sub"
        "zero dot eq add"
        "undo clr clr clr";
    gap: 10px;
}

/* نحدد كل زرّ في أي منطقة هينزل */
#calcSevenBtn  { grid-area: seven; }
#calcEightBtn  { grid-area: eight; }
#calcNineBtn   { grid-area: nine; }
#calcDivBtn    { grid-area: div;   }

#calcFourBtn   { grid-area: four;  }
#calcFiveBtn   { grid-area: five;  }
#calcSixBtn    { grid-area: six;   }
#calcMulBtn    { grid-area: mul;   }

#calcOneBtn    { grid-area: one;   }
#calcTwoBtn    { grid-area: two;   }
#calcThreeBtn  { grid-area: three; }
#calcSubBtn    { grid-area: sub;   }

#calcZeroBtn   { grid-area: zero;  }
#calcDotBtn    { grid-area: dot;   }
#calcEqBtn     { grid-area: eq;    }
#calcAddBtn    { grid-area: add;   }

#calcUndoBtn   { grid-area: undo;  }
#calcClearBtn  { grid-area: clr;   }

/* Icon styles */
.fa-plus {
    color: #28a745; /* Green for add */
    transition: color 0.3s ease;
}

.fa-plus:hover {
    color: #218838; /* Darker green on hover */
}

.fa-list {
    color: #007bff; /* Blue for list */
    transition: color 0.3s ease;
}

.fa-list:hover {
    color: #0056b3; /* Darker blue on hover */
}

.fa-trash-alt {
    color: #dc3545; /* Red for delete */
    transition: color 0.3s ease;
}

.fa-trash-alt:hover {
    color: #c82333; /* Darker red on hover */
}

.fa-edit {
    color: #ffc107; /* Yellow for edit */
    transition: color 0.3s ease;
}

.fa-edit:hover {
    color: #e0a800; /* Darker yellow on hover */
}

/* Dark mode styles for "تقرير شامل" and "تفاصيل العمليات" modals */
[data-theme="dark"] .modal {
    background-color: rgba(0, 0, 0, 0.9); /* Darker overlay for dark mode */
}

[data-theme="dark"] .modal-content {
    background-color: var(--color-secondary); /* Keep modal content background light */
    color: var(--color-fg); /* Keep text color consistent */
    border: 1px solid var(--color-primary); /* Subtle border for dark mode */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5); /* Add shadow for depth */
}

[data-theme="dark"] .modal-content h2,
[data-theme="dark"] .modal-content p {
    color: #e6edf3; /* Softer text color for better readability */
}

[data-theme="dark"] .modal-content .close {
    background-color: #3f51b5; /* Close button background */
    color: #ffffff; /* Close button text color */
}

[data-theme="dark"] .modal-content .close:hover {
    background-color: #303f9f; /* Darker hover effect */
}

[data-theme="dark"] .modal-table-container table {
    background-color: #1c2025; /* Darker table background */
    color: #c9d1d9; /* Match dark mode text color */
    border-radius: 8px;
    overflow: hidden;
}

/* Adjust card container to stack cards vertically within the frame */
.card-container {
    display: flex;
    flex-direction: column; /* Stack cards one under the other */
    gap: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Adjust individual card styling to fill container width */
.card {
    width: 100%; /* Make card fill container width */
    max-width: none; /* Remove previous max-width constraint */
    box-sizing: border-box;
}

.card {
    background: #ffffff;
    border-radius: 18px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
    padding: 22px 20px;
    margin: 12px 0;
    min-width: 260px;
    max-width: 360px;
    width: 100%;
    direction: rtl;
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: 1px solid #eee;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
  }
  
  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
  }
  
  .card h3 {
    font-size: 1.4em;
    color: #2c3e50;
    font-weight: bold;
    margin: 0;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
  }
  
  .card p {
    margin: 0;
    font-size: 1.05em;
    color: #444;
    line-height: 1.6;
  }
  
  /* Status badge */
  .card .status-badge {
    position: absolute;
    top: 16px;
    left: 16px;
    background: #28a745;
    color: white;
    padding: 4px 12px;
    font-size: 0.9em;
    border-radius: 50px;
    font-weight: bold;
  }
  
  .card .status-badge.inactive {
    background: #e74c3c;
  }
  
  /* Actions */
  .card .actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: auto;
  }
  
  .card .edit-btn,
  .card .delete-btn {
    padding: 8px 14px;
    font-size: 0.95em;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
  }
  
  
  

/* Dark mode overrides for card styles */
[data-theme="dark"] .card {
    background: #1c1e22; /* خلفية داكنة */
    border: 1px solid #333; /* حدود داكنة */
    color: #e6edf3; /* نص فاتح */
}
[data-theme="dark"] .card h3 {
    color: #58a6ff; /* لون العنوان */
    border-color: #58a6ff;
}
[data-theme="dark"] .card p {
    color: #c9d1d9;
}
[data-theme="dark"] .card .status-badge {
    background: #58a6ff; /* لون يشير إلى الحالة بالوضع الداكن */
    color: #ffffff;
}

/* Hide table and show cards on mobile screens */
@media (max-width: 768px) {
  .table,
  .table thead,
  .table tbody,
  .table tr,
  .table td,
  .table th {
    display: none !important;
  }

  #shiftCardsContainer {
    display: flex !important;
    flex-direction: column;
    gap: 1rem;
  }
}

/* Show table and hide cards on larger screens */
@media (min-width: 769px) {
  #shiftCardsContainer {
    display: none !important;
  }

  .table {
    display: table !important;
  }
}

/* Header styles */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: var(--color-secondary);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--color-primary);
}

.menu-icon, .notification-icon, .account-icon {
    cursor: pointer;
    font-size: 24px;
    color: var(--color-primary);
    position: relative;
}

.notification-icon .notification-count {
    position: absolute;
    top: -5px;
    right: -10px;
    background-color: red;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
}

[data-theme="dark"] .header-container {
    background-color: var(--color-bg);
    border-bottom: 1px solid var(--color-hover);
}

[data-theme="dark"] .menu-icon, 
[data-theme="dark"] .notification-icon, 
[data-theme="dark"] .account-icon {
    color: var(--color-fg);
}

[data-theme="dark"] .notification-icon .notification-count {
    background-color: #e74c3c;
}

/* Profile Page Styles */
.profile-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background-color: var(--color-secondary);
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center; /* Center-align text */
}

.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 5px; /* Reduced from previous value */
}

.account-profile-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 5px; /* Reduced margin to bring the title closer */
    border: 3px solid var(--color-primary);
}

.account-default-profile {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: var(--color-hover);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 50px;
    color: var(--color-primary);
    margin-bottom: 5px; /* Reduced margin to bring the title closer */
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    text-align: center;
}

.success-message {
    color: green;
    font-size: 14px;
    margin-bottom: 15px;
    text-align: center;
}

@media (max-width: 768px) {
    .profile-container {
        padding: 15px;
    }

    .account-profile-img,
    .account-default-profile {
        width: 100px;
        height: 100px;
    }
}

/* SweetAlert2 container styles */
.swal2-container {
    z-index: 2000; /* Increased to ensure it appears above the header */
}
/* Stores Page Messages Styles */
.permissions-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.permission-warning {
    color: #ffc107;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.permission-error {
    color: #dc3545;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.no-stores-message {
    background-color: #fff3cd;
    color: #856404;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #ffeaa7;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-access-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #f5c6cb;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Support for Stores Messages */
[data-theme="dark"] .permissions-info {
    background-color: #21262d;
    border-color: #30363d;
    color: #c9d1d9;
}

[data-theme="dark"] .permission-warning {
    color: #fdb813;
}

[data-theme="dark"] .permission-error {
    color: #f85149;
}

[data-theme="dark"] .no-stores-message {
    background-color: #2d2a1f;
    color: #e8d44a;
    border-color: #3d3a2f;
}

[data-theme="dark"] .no-access-message {
    background-color: #2d1b1f;
    color: #f85149;
    border-color: #3d2b2f;
}