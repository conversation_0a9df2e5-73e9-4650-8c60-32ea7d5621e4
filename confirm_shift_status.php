<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$closure_id = isset($_POST['closure_id']) ? intval($_POST['closure_id']) : null;

if (!$closure_id) {
    echo json_encode(['success' => false, 'error' => 'Invalid closure ID.']);
    exit();
}

$stmt = $conn->prepare("UPDATE shift_closures SET status = 'Active' WHERE closure_id = ?");
$stmt->bind_param("i", $closure_id);

if ($stmt->execute()) {
    // Log the shift confirmation action
    $key = getenv('ENCRYPTION_KEY');
    $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'update', 'shift_closures', ?, ?)";
    $description = "تم تأكيد الوردية رقم $closure_id";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $closure_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'error' => 'Failed to update shift status.']);
}

$stmt->close();
$conn->close();
