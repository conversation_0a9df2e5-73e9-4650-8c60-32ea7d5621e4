<?php
include 'db_connection.php';

$invoice_id = isset($_GET['invoice_id']) ? intval($_GET['invoice_id']) : 0;

if ($invoice_id <= 0) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT it.name AS item_name, ii.quantity, it.cost 
        FROM inventory_invoice_items ii 
        JOIN items it ON ii.item_id = it.item_id 
        WHERE ii.inventory_invoice_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$result = $stmt->get_result();

$items = [];
while ($row = $result->fetch_assoc()) {
    $row['total'] = $row['quantity'] * $row['cost'];
    $items[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($items);
?>
