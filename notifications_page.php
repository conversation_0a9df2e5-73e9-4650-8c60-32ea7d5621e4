<?php
/**
 * صفحة الإشعارات - مدمجة مع نظام الصلاحيات
 * 
 * الصلاحيات المطبقة:
 * - access: الوصول إلى صفحة الإشعارات (مطلوبة للوصول للصفحة)
 * - read_notifications: قراءة الإشعارات (مطلوبة لعرض محتوى الإشعارات)
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

require_once 'db_connection.php';
require_once 'security.php';
require_once 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('notifications', 'access');

// Get store_id from URL parameter
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
$store_id = null;

if (!empty($encrypted_store_id)) {
    $key = getenv('ENCRYPTION_KEY');
    $store_id = decrypt($encrypted_store_id, $key);
    if (!$store_id) {
        die('معرف الفرع غير صحيح');
    }
}

// Get current user ID from encrypted account_id in session
$user_id = null;
if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
    $key = getenv('ENCRYPTION_KEY');
    $user_id = decrypt($_SESSION['account_id'], $key);
}

if (!$user_id) {
    header('Location: logon.php');
    exit;
}

// Get store name if store_id is provided
$store_name = 'جميع الفروع';
if ($store_id) {
    $store_query = "SELECT name FROM stores WHERE store_id = ?";
    $store_stmt = $conn->prepare($store_query);
    $store_stmt->bind_param("i", $store_id);
    $store_stmt->execute();
    $store_result = $store_stmt->get_result();
    if ($store_row = $store_result->fetch_assoc()) {
        $store_name = $store_row['name'];
    }
    $store_stmt->close();
}

// فحص صلاحية قراءة الإشعارات
$can_read_notifications = hasPermission('notifications', 'read_notifications');

// Fetch notifications only if user has permission
if ($can_read_notifications) {
    $query = "
        SELECT n.id,
               SUBSTRING(n.message, 1, 50) as title,
               n.message, n.created_at, n.created_by, n.status,
               CASE WHEN n.status = 'read' THEN 1 ELSE 0 END as is_read,
               nr.viewed_at,
               u.username as sender_name
        FROM notifications n
        LEFT JOIN notification_reads nr ON n.id = nr.notification_id AND nr.account_id = ?
        LEFT JOIN accounts u ON n.created_by = u.account_id
        WHERE n.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
    ";

    $params = [$user_id];

    // Add store filter if store_id is provided
    if ($store_id) {
        $query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
        $params[] = $store_id;
    }

    $query .= " ORDER BY n.created_at DESC LIMIT 50";

    $stmt = $conn->prepare($query);
    $stmt->bind_param(str_repeat('i', count($params)), ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    // Count unread notifications
    $unread_query = "
        SELECT COUNT(*) as unread_count
        FROM notifications n
        WHERE n.status = 'notread'
    ";

    $unread_params = [];

    if ($store_id) {
        $unread_query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
        $unread_params[] = $store_id;
    }

    $unread_stmt = $conn->prepare($unread_query);
    if (count($unread_params) > 0) {
        $unread_stmt->bind_param(str_repeat('i', count($unread_params)), ...$unread_params);
    }
    $unread_stmt->execute();
    $unread_result = $unread_stmt->get_result();
    $unread_row = $unread_result->fetch_assoc();
    $unreadCount = $unread_row['unread_count'] ?? 0;
} else {
    // إذا لم يكن لديه صلاحية قراءة الإشعارات، تعيين قيم افتراضية
    $result = null;
    $unreadCount = 0;
}

// Get current file name for sidebar
$current_file = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - <?= htmlspecialchars($store_name) ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="web_css/style_web.css">

    <style>
        .notifications-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .notification-card {
            background: var(--color-bg);
            border: 1px solid var(--color-border);
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .notification-card.unread {
            border-left: 4px solid var(--color-primary);
            background: linear-gradient(90deg, rgba(63, 81, 181, 0.05) 0%, var(--color-bg) 100%);
        }

        .notification-card.read {
            opacity: 0.7;
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .notification-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-text);
            margin: 0;
        }

        .notification-time {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }

        .notification-message {
            color: var(--color-text);
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .notification-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-sender {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }

        .mark-read-btn {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mark-read-btn:hover {
            background: var(--color-primary-dark);
            transform: translateY(-1px);
        }

        .no-notifications {
            text-align: center;
            padding: 60px 20px;
            color: var(--color-text-secondary);
        }

        .no-notifications i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--color-bg);
            border: 1px solid var(--color-border);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--color-primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--color-text-secondary);
            font-size: 0.9rem;
        }

        .page-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 0 0 20px 20px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
        }

        .page-subtitle {
            opacity: 0.9;
            margin-top: 5px;
        }

        .filter-buttons {
            margin-bottom: 20px;
        }

        .filter-btn {
            background: var(--color-bg);
            border: 1px solid var(--color-border);
            color: var(--color-text);
            padding: 8px 16px;
            margin-left: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-bell"></i> الإشعارات
            </h1>
            <p class="page-subtitle">إدارة ومتابعة جميع الإشعارات - <?= htmlspecialchars($store_name) ?></p>
        </div>
    </div>

    <div class="notifications-container">
        <?php if ($can_read_notifications): ?>
            <!-- Statistics Cards -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-number"><?= $unreadCount ?></div>
                    <div class="stat-label">إشعارات غير مقروءة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $result ? $result->num_rows : 0 ?></div>
                    <div class="stat-label">إجمالي الإشعارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $result ? ($result->num_rows - $unreadCount) : 0 ?></div>
                    <div class="stat-label">إشعارات مقروءة</div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterNotifications('all')">
                    <i class="fas fa-list"></i> جميع الإشعارات
                </button>
                <button class="filter-btn" onclick="filterNotifications('unread')">
                    <i class="fas fa-envelope"></i> غير مقروءة
                </button>
                <button class="filter-btn" onclick="filterNotifications('read')">
                    <i class="fas fa-envelope-open"></i> مقروءة
                </button>
                <button class="filter-btn" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                </button>
            </div>

            <!-- Notifications List -->
            <div id="notifications-list">
                <?php if ($result && $result->num_rows > 0): ?>
                    <?php while ($notification = $result->fetch_assoc()): ?>
                    <div class="notification-card <?= $notification['is_read'] ? 'read' : 'unread' ?>"
                         id="notification-<?= $notification['id'] ?>"
                         data-read="<?= $notification['is_read'] ?>">

                        <div class="notification-header">
                            <h5 class="notification-title">
                                <?php if (!$notification['is_read']): ?>
                                    <i class="fas fa-circle text-primary" style="font-size: 0.5rem; margin-left: 8px;"></i>
                                <?php endif; ?>
                                <?= htmlspecialchars($notification['title'] ?? 'إشعار') ?>
                            </h5>
                            <span class="notification-time">
                                <i class="fas fa-clock"></i>
                                <?= date('Y-m-d H:i', strtotime($notification['created_at'])) ?>
                            </span>
                        </div>

                        <div class="notification-message">
                            <?= nl2br(htmlspecialchars($notification['message'])) ?>
                        </div>

                        <div class="notification-footer">
                            <span class="notification-sender">
                                <i class="fas fa-user"></i>
                                من: <?= htmlspecialchars($notification['sender_name'] ?? 'النظام') ?>
                            </span>

                            <?php if (!$notification['is_read']): ?>
                                <button class="mark-read-btn" onclick="markAsRead(<?= $notification['id'] ?>)">
                                    <i class="fas fa-check"></i> تحديد كمقروء
                                </button>
                            <?php else: ?>
                                <span class="text-success">
                                    <i class="fas fa-check-circle"></i> مقروء
                                    <?php if ($notification['viewed_at']): ?>
                                        في <?= date('Y-m-d H:i', strtotime($notification['viewed_at'])) ?>
                                    <?php endif; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="no-notifications">
                        <i class="fas fa-bell-slash"></i>
                        <h3>لا توجد إشعارات</h3>
                        <p>لم يتم العثور على أي إشعارات في الشهر الماضي.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- رسالة عدم وجود صلاحية قراءة الإشعارات -->
            <div class="no-notifications">
                <i class="fas fa-lock" style="color: #f44336;"></i>
                <h3>غير مسموح</h3>
                <p>ليس لديك صلاحية لقراءة الإشعارات. يرجى التواصل مع المدير لمنحك الصلاحية المطلوبة.</p>
                <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404;">
                    <strong>الصلاحية المطلوبة:</strong> قراءة الإشعارات (read_notifications)
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Firebase Scripts -->
    <?php include_once 'firebase_scripts.php'; ?>

    <script>
        // متغيرات الصلاحيات
        const permissions = {
            read_notifications: <?php echo hasPermission('notifications', 'read_notifications') ? 'true' : 'false'; ?>
        };

        // Mark notification as read
        function markAsRead(notificationId) {
            // فحص الصلاحية قبل تنفيذ العملية
            if (!permissions.read_notifications) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لتحديد الإشعارات كمقروءة'
                });
                return;
            }

            fetch('mark_notification_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const notificationElement = document.getElementById('notification-' + notificationId);
                    notificationElement.classList.remove('unread');
                    notificationElement.classList.add('read');
                    notificationElement.setAttribute('data-read', '1');

                    // Update the footer
                    const footer = notificationElement.querySelector('.notification-footer');
                    const button = footer.querySelector('.mark-read-btn');
                    if (button) {
                        button.outerHTML = '<span class="text-success"><i class="fas fa-check-circle"></i> مقروء</span>';
                    }

                    // Remove the unread indicator
                    const indicator = notificationElement.querySelector('.fa-circle');
                    if (indicator) {
                        indicator.remove();
                    }

                    // Update statistics
                    updateStats();

                    // Update unread count in sidebar multiple times
                    updateUnreadCount();
                    setTimeout(() => {
                        updateUnreadCount();
                    }, 500);
                    setTimeout(() => {
                        updateUnreadCount();
                    }, 1500);

                    // Force update if available
                    if (typeof forceUpdateUnreadCount === 'function') {
                        setTimeout(() => {
                            forceUpdateUnreadCount();
                        }, 2000);
                    }

                    // Refresh the page to show updated notifications
                    setTimeout(() => {
                        location.reload();
                    }, 1000);

                    Swal.fire({
                        icon: 'success',
                        title: 'تم',
                        text: 'تم تحديد الإشعار كمقروء',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء تحديث الإشعار'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ في الاتصال'
                });
            });
        }

        // Mark all notifications as read
        function markAllAsRead() {
            // فحص الصلاحية قبل تنفيذ العملية
            if (!permissions.read_notifications) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لتحديد الإشعارات كمقروءة'
                });
                return;
            }

            Swal.fire({
                title: 'تأكيد',
                text: 'هل تريد تحديد جميع الإشعارات كمقروءة؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('mark_all_notifications_read.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            store_id: <?= $store_id ? $store_id : 'null' ?>
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update unread count multiple times
                            updateUnreadCount();
                            setTimeout(() => {
                                updateUnreadCount();
                            }, 500);
                            setTimeout(() => {
                                updateUnreadCount();
                            }, 1500);

                            // Force update if available
                            if (typeof forceUpdateUnreadCount === 'function') {
                                setTimeout(() => {
                                    forceUpdateUnreadCount();
                                }, 2000);
                            }

                            location.reload();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ',
                                text: data.message || 'حدث خطأ أثناء تحديث الإشعارات'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: 'حدث خطأ في الاتصال'
                        });
                    });
                }
            });
        }

        // Filter notifications
        function filterNotifications(filter) {
            const notifications = document.querySelectorAll('.notification-card');
            const filterButtons = document.querySelectorAll('.filter-btn');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Filter notifications
            notifications.forEach(notification => {
                const isRead = notification.getAttribute('data-read') === '1';

                switch(filter) {
                    case 'all':
                        notification.style.display = 'block';
                        break;
                    case 'unread':
                        notification.style.display = isRead ? 'none' : 'block';
                        break;
                    case 'read':
                        notification.style.display = isRead ? 'block' : 'none';
                        break;
                }
            });
        }

        // Update statistics
        function updateStats() {
            const allNotifications = document.querySelectorAll('.notification-card');
            const unreadNotifications = document.querySelectorAll('.notification-card[data-read="0"]');
            const readNotifications = document.querySelectorAll('.notification-card[data-read="1"]');

            const statCards = document.querySelectorAll('.stat-number');
            if (statCards.length >= 3) {
                statCards[0].textContent = unreadNotifications.length;
                statCards[1].textContent = allNotifications.length;
                statCards[2].textContent = readNotifications.length;
            }
        }

        // Auto-refresh notifications every 30 seconds
        setInterval(() => {
            updateUnreadCount();
            // Also refresh the page content to show new notifications
            location.reload();
        }, 30000);
    </script>
</body>
</html>

<?php
/**
 * ملخص الصلاحيات المطبقة في هذا الملف:
 * 
 * 1. access (وحدة notifications):
 *    - مطلوبة للوصول إلى صفحة الإشعارات
 *    - يتم فحصها في بداية الملف باستخدام checkPagePermission()
 *    - إذا لم تكن متوفرة، يتم إعادة توجيه المستخدم لصفحة غير مصرح
 * 
 * 2. read_notifications (وحدة notifications):
 *    - مطلوبة لقراءة محتوى الإشعارات
 *    - يتم فحصها قبل جلب الإشعارات من قاع��ة البيانات
 *    - يتم فحصها في JavaScript قبل تنفيذ عمليات تحديد الإشعارات كمقروءة
 *    - إذا لم تكن متوفرة، يتم عرض رسالة تفيد بعدم وجود صلاحية
 * 
 * طريقة التطبيق:
 * - تم استخدام دوال نظام الصلاحيات: checkPagePermission() و hasPermission()
 * - تم إضافة فحص الصلاحيات في PHP و JavaScript
 * - تم عرض رسائل واضحة للمستخدم في حالة عدم وجود الصلاحية المطلوبة
 * - تم الحفاظ على تجربة المستخدم بعرض رسائل مفيدة بدلاً من أخطاء فنية
 */
?>
