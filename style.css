@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
:root {
    --base-clr: #1F2937;
    /* Dark Navy */
    --line-clr: #e7e7e7;
    /* Light Gray */
    --hover-clr: #343434;
    /* Dark Gray */
    --text-clr: #ffffff;
    /* White */
    --accent-clr: #fdb813;
    /* Golden Yellow */
    --secondary-text-clr: #b0b3c1;
}

* {
    margin: 0;
    padding: 0;
}

html {
    font-family: Poppins, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5rem;
}

body {
    min-height: 100vh;
    min-height: 100dvh;
    background-color: #1F2937;
    display: grid;
    grid-template-columns: auto 1fr;
    /* اجعل العمود الأول للسايدبار */
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
    background-color: #1F2937;
}

#sidebar {
    box-sizing: border-box;
    height: 100vh;
    width: 250px;
    padding: 5px 1em;
    background-color: var(--base-clr);
    border-right: 1px solid var(--line-clr);
    /* عدل من border-left إلى border-right */
    position: sticky;
    top: 0;
    align-self: start;
    transition: 300ms ease-in-out;
    overflow: hidden;
    text-wrap: nowrap;
    direction: rtl;
    /* حافظ على اتجاه النصوص */
    right: 0;
    /* تأكد أن السايدبار على اليمين */
    left: auto;
}

#sidebar.close {
    padding: 5px;
    width: 60px;
}

#sidebar ul {
    list-style: none;
}


/* باقي الأكواد تظل كما هي */

#sidebar>ul>li:first-child {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    .logo {
        font-weight: 600;
    }
}

#sidebar ul li.active a {
    color: var(--accent-clr);
    svg {
        fill: var(--accent-clr);
    }
}

#sidebar a,
#sidebar .dropdown-btn,
#sidebar .logo {
    border-radius: .5em;
    padding: .85em;
    text-decoration: none;
    color: var(--text-clr);
    display: flex;
    align-items: center;
    gap: 1em;
}

.dropdown-btn {
    width: 100%;
    text-align: right;
    background: none;
    border: none;
    font: inherit;
    cursor: pointer;
}

#sidebar svg {
    flex-shrink: 0;
    fill: var(--text-clr);
}

#sidebar a span,
#sidebar .dropdown-btn span {
    flex-grow: 1;
}

#sidebar a:hover,
#sidebar .dropdown-btn:hover {
    background-color: var(--hover-clr);
}

#sidebar .sub-menu {
    display: grid;
    grid-template-rows: 0fr;
    transition: 300ms ease-in-out;
    >div {
        overflow: hidden;
    }
}

#sidebar .sub-menu.show {
    grid-template-rows: 1fr;
}

.dropdown-btn svg {
    transition: 200ms ease;
}

.rotate svg:last-child {
    rotate: 180deg;
}

#sidebar .sub-menu a {
    padding-right: 2em;
}

#toggle-btn {
    margin-left: auto;
    padding: 1em;
    border: none;
    border-radius: .5em;
    background: none;
    cursor: pointer;
    svg {
        transition: rotate 150ms ease;
    }
}

#toggle-btn:hover {
    background-color: var(--hover-clr);
}

main {
    padding: min(30px, 7%);
}

main p {
    color: var(--secondary-text-clr);
    margin-top: 5px;
    margin-bottom: 15px;
}

.container {
    border: 1px solid var(--line-clr);
    border-radius: 1em;
    margin-bottom: 20px;
    padding: min(3em, 15%);
    h2,
    p {
        margin-top: 1em
    }
    margin: 20px;
}

.action-btn,
.add-btn {
    background-color: #3B82F6;
    color: white;
    border: none;
    padding: 12px 25px;
    cursor: pointer;
    border-radius: 25px;
    font-size: 16px;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.action-btn:hover,
.add-btn:hover {
    background-color: #2563EB;
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:active,
.add-btn:active {
    background-color: #1D4ED8;
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 400px;
    color: #06162b;
}

.input-field {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
    font-size: 16px;
}

.input-field:focus {
    border-color: #6fa3ef;
    box-shadow: 0 0 5px rgba(111, 163, 239, 0.5);
}

.store-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
}

.store-card {
    width: 200px;
    padding: 20px;
    background-color: #343434;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease, background-color 0.3s ease;
    text-align: center;
}

.store-card:hover {
    transform: scale(1.05);
    background-color: #fdb813;
    color: #06162b;
}

.store-card h3 {
    margin: 0;
    font-size: 18px;
}

.error-message {
    color: red;
    font-size: 14px;
}

.toggle-btn {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    border: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.toggle-btn.inactive {
    background-color: #f44336;
}

.toggle-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
}

.toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.image-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    position: relative;
}

.image-preview .delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: red;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
}

.search-field {
    width: 90%;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.checkbox-cell {
    cursor: pointer;
}

.popup-message {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 5px;
    font-size: 16px;
    color: #fff;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.popup-message.success {
    background-color: #4CAF50;
    /* Green */
}

.popup-message.error {
    background-color: #f44336;
    /* Red */
}

.popup-message.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.popup-message.hide {
    opacity: 0;
    transform: translateY(-20px);
}

@media(max-width: 800px) {
    body {
        grid-template-columns: 1fr;
    }
    main {
        padding: 2em 1em 60px 1em;
    }
    .container2 {
        border: none;
        padding: 0;
    }
    #sidebar {
        height: 60px;
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--line-clr);
        padding: 0;
        position: fixed;
        top: unset;
        bottom: 0;
        right: 0;
        left: auto;
        ul {
            padding: 0;
            display: grid;
            grid-auto-columns: 60px;
            grid-auto-flow: column;
            align-items: center;
            overflow-x: scroll;
        }
        ul li {
            height: 100%;
        }
        ul a,
        ul .dropdown-btn {
            width: 60px;
            height: 60px;
            padding: 0;
            border-radius: 0;
            justify-content: center;
        }
        ul li span,
        ul li:first-child,
        .dropdown-btn svg:last-child {
            display: none;
        }
        ul li .sub-menu.show {
            position: fixed;
            bottom: 60px;
            right: 0;
            left: auto;
            box-sizing: border-box;
            height: 60px;
            width: 100%;
            background-color: var(--hover-clr);
            border-top: 1px solid var(--line-clr);
            display: flex;
            justify-content: center;
            >div {
                overflow-x: auto;
            }
            li {
                display: inline-flex;
            }
            a {
                box-sizing: border-box;
                padding: 1em;
                width: auto;
                justify-content: center;
            }
        }
    }
}