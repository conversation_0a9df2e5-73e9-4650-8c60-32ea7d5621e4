<?php
include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['account_id'])) {
    $encrypted_account_id = $_GET['account_id'];
    $account_id = decrypt($encrypted_account_id, $key);

    if (!is_numeric($account_id)) {
        echo json_encode(['success' => false, 'message' => 'Invalid account ID']);
        exit;
    }

    $sql = "SELECT * FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        exit;
    }
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();

    if ($result->num_rows > 0) {
        $account = $result->fetch_assoc();
        echo json_encode(['success' => true, 'account' => $account]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Account not found']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

$conn->close();
?>
