<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Decrypt the encrypted account ID
$encrypted_account_id = $_GET['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if ($account_id === false) {
    error_log("Failed to decrypt account ID."); // Log decryption error
    echo json_encode([]); // Return an empty array if decryption fails
    exit;
}

// نوع البيانات (مبيعات أو مرتجعات) - الافتراضي هو مبيعات
$data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales';

// تحديد الجدول المطلوب بناءً على نوع البيانات
$table_name = ($data_type === 'returns') ? 'returns' : 'sales';

// Support both `order_dates` and `order_date` as comma-separated strings
$order_dates = isset($_GET['order_dates']) 
    ? explode(',', $_GET['order_dates']) 
    : (isset($_GET['order_date']) ? explode(',', $_GET['order_date']) : []);

if (empty($order_dates)) {
    // Fetch all orders for the account if no specific dates are provided
    if ($table_name === 'returns') {
        $sql = "SELECT r.return_id as sale_id, i.name, r.quantity, r.refund_amount as price, 
                   r.refund_amount AS total_amount, 
                   COALESCE(r.refunded, 0) AS collected, 
                   'confirmed' AS status,
                   r.item_id, DATE(r.time) AS order_date
            FROM returns r
            JOIN items i ON r.item_id = i.item_id
            WHERE r.account_id = ?";
    } else {
        $sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.cost,
                   (s.price * s.quantity) AS total_amount, 
                   ((s.price - s.cost) * s.quantity) AS profit,
                   COALESCE(s.collected, 0) AS collected, 
                   s.status, s.item_id, DATE(s.time) AS order_date
            FROM sales s
            JOIN items i ON s.item_id = i.item_id
            WHERE s.account_id = ?";
    }
    $params = [$account_id];
    $types = 'i';
} else {
    // Fetch orders for the account filtered by specific dates
    $placeholders = implode(',', array_fill(0, count($order_dates), '?'));
    
    if ($table_name === 'returns') {
        $sql = "SELECT r.return_id as sale_id, i.name, r.quantity, r.refund_amount as price, 
                   r.refund_amount AS total_amount, 
                   COALESCE(r.refunded, 0) AS collected, 
                   'confirmed' AS status,
                   r.item_id, DATE(r.time) AS order_date
            FROM returns r
            JOIN items i ON r.item_id = i.item_id
            WHERE r.account_id = ? AND DATE(r.time) IN ($placeholders)";
    } else {
        $sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.cost,
                   (s.price * s.quantity) AS total_amount, 
                   ((s.price - s.cost) * s.quantity) AS profit,
                   COALESCE(s.collected, 0) AS collected, 
                   s.status, s.item_id, DATE(s.time) AS order_date
            FROM sales s
            JOIN items i ON s.item_id = i.item_id
            WHERE s.account_id = ? AND DATE(s.time) IN ($placeholders)";
    }
    
    $params = array_merge([$account_id], $order_dates);
    $types = 'i' . str_repeat('s', count($order_dates));
}

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    error_log("SQL prepare failed: " . $conn->error); // Log SQL preparation error
    echo json_encode([]); // Return an empty array if the query preparation fails
    exit;
}

$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

if ($result === false) {
    error_log("SQL execution failed: " . $stmt->error); // Log SQL execution error
    echo json_encode([]); // Return an empty array if the query execution fails
    exit;
}

$order_details = [];
while ($row = $result->fetch_assoc()) {
    // حساب الباقي
    $row['remaining'] = $row['total_amount'] - $row['collected'];
    $order_details[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($order_details);
?>
