<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// التحقق من صلاحية الوصول لوحدة التقارير
checkPagePermission('reports', 'view');

// فحص الصلاحيات المختلفة لوحدة التقارير
$canExportExcel = hasPermission('reports', 'export_excel');
$canViewComprehensiveReport = hasPermission('reports', 'comprehensive_report');
$canViewQueries = hasPermission('reports', 'view_queries');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);

    $sql = "SELECT items.item_id, items.name, items.type, items.quantity, items.cost, items.price 
            FROM items 
            JOIN categories ON items.category_id = categories.category_id 
            WHERE categories.store_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
} else {
    echo "Store ID is missing.";
    exit();
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        /* أنماط أزرار التقارير */
        .reports-actions-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .export-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .summary-btn {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
        }
        
        .summary-btn:hover {
            background: linear-gradient(135deg, #0056b3, #520dc2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .queries-btn {
            background: linear-gradient(135deg, #fd7e14, #e83e8c);
            color: white;
        }
        
        .queries-btn:hover {
            background: linear-gradient(135deg, #e8650e, #d91a72);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
        }
        
        /* أنماط التقرير الشامل */
        .summary-stats {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-label {
            font-weight: 600;
            color: #495057;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-value.profit {
            color: #28a745;
        }
        
        /* أنماط نافذة الاستعلامات */
        .queries-content {
            margin-top: 20px;
        }
        
        .query-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .query-section h3 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .query-results {
            display: grid;
            gap: 10px;
        }
        
        .query-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 3px solid #007bff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .query-item.low-stock {
            border-left-color: #dc3545;
        }
        
        .item-name {
            font-weight: 600;
            color: #495057;
        }
        
        .item-profit {
            color: #28a745;
            font-weight: bold;
        }
        
        .item-quantity {
            color: #dc3545;
            font-weight: bold;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-box .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        /* تحسينات شاملة للوضع المظلم */
        [data-theme="dark"] .permissions-warning {
            background: #856404 !important;
            color: #fff3cd !important;
            border-color: #ffeaa7 !important;
        }
        
        /* أنماط الإحصائيات في الوضع المظلم */
        [data-theme="dark"] .stat-item {
            background: #21262d;
            color: #c9d1d9;
            border-left-color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-label {
            color: #8b949e;
        }
        
        [data-theme="dark"] .stat-value {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-value.profit {
            color: #3fb950;
        }
        
        /* أنماط نوافذ الاستعلامات في الوضع المظلم */
        [data-theme="dark"] .query-section {
            background: #21262d;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] .query-section h3 {
            color: #c9d1d9;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] .query-item {
            background: #161b22;
            color: #c9d1d9;
            border-left-color: var(--color-primary);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }
        
        [data-theme="dark"] .query-item.low-stock {
            border-left-color: #f85149;
        }
        
        [data-theme="dark"] .item-name {
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .item-profit {
            color: #3fb950;
        }
        
        [data-theme="dark"] .item-quantity {
            color: #f85149;
        }
        
        [data-theme="dark"] .stat-box {
            background: #161b22;
            color: #c9d1d9;
            border: 1px solid #30363d;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        [data-theme="dark"] .stat-number {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .stat-box .stat-label {
            color: #8b949e;
        }
        
        /* أنماط الأزرار في الوضع المظلم */
        [data-theme="dark"] .export-btn {
            background: linear-gradient(135deg, #238636, #2ea043);
        }
        
        [data-theme="dark"] .export-btn:hover {
            background: linear-gradient(135deg, #1f7a2e, #26973b);
            box-shadow: 0 4px 15px rgba(35, 134, 54, 0.4);
        }
        
        [data-theme="dark"] .summary-btn {
            background: linear-gradient(135deg, #1f6feb, #8b5cf6);
        }
        
        [data-theme="dark"] .summary-btn:hover {
            background: linear-gradient(135deg, #1a5cd8, #7c3aed);
            box-shadow: 0 4px 15px rgba(31, 111, 235, 0.4);
        }
        
        [data-theme="dark"] .queries-btn {
            background: linear-gradient(135deg, #fb8500, #d63384);
        }
        
        [data-theme="dark"] .queries-btn:hover {
            background: linear-gradient(135deg, #e07600, #c2185b);
            box-shadow: 0 4px 15px rgba(251, 133, 0, 0.4);
        }
        
        /* أنماط الجداول في الوضع المظلم */
        [data-theme="dark"] table {
            background-color: #161b22;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] th {
            background-color: var(--color-primary);
            color: #ffffff;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] td {
            border-bottom-color: #30363d;
            color: #c9d1d9;
        }
        
        [data-theme="dark"] tr:hover {
            background-color: #21262d;
            color: #c9d1d9;
        }
        
        /* أنماط النوافذ المنبثقة في الوضع المظلم */
        [data-theme="dark"] .modal-content.queries-modal {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .modal-content.summary-report {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .queries-content {
            color: #c9d1d9;
        }
        
        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .reports-actions-container {
                flex-direction: column;
            }
            
            .action-btn {
                width: 100%;
                justify-content: center;
            }
            
            .quick-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            // Apply stored theme or system preference on load
            const storedTheme = localStorage.getItem("theme");
            if (storedTheme) {
                document.documentElement.setAttribute("data-theme", storedTheme);
            } else if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
                document.documentElement.setAttribute("data-theme", "dark");
            }
        });
    </script>
    <style>
        /* أنماط إضافية للمود الدارك في تقارير الأصناف */
        [data-theme="dark"] .search-bar {
            background: linear-gradient(145deg, #21262d, #161b22);
            border-color: #30363d;
            color: #c9d1d9;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                        inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }
        
        [data-theme="dark"] .search-bar:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 
                        0 12px 35px rgba(0, 0, 0, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            background: linear-gradient(145deg, #262c36, #1c2128);
        }
        
        [data-theme="dark"] .search-bar::placeholder {
            color: #7d8590;
            opacity: 0.9;
        }
        
        /* أنماط النوافذ المنبثقة للمعاملات في الوضع المظلم */
        [data-theme="dark"] #transactionsModal .modal-content {
            background-color: #0d1117;
            border-color: var(--color-primary);
            color: #c9d1d9;
        }
        
        [data-theme="dark"] #transactionsModal table {
            background-color: #161b22;
            border: 1px solid #30363d;
        }
        
        [data-theme="dark"] #transactionsModal th {
            background-color: var(--color-primary);
            color: #ffffff;
            border-bottom-color: #30363d;
        }
        
        [data-theme="dark"] #transactionsModal td {
            border-bottom-color: #30363d;
            color: #c9d1d9;
        }
        
        [data-theme="dark"] #transactionsModal tr:hover {
            background-color: #21262d;
        }
        
        /* أنماط حاوية الجدول في الوضع المظلم */
        [data-theme="dark"] .table-container {
            background-color: #0d1117;
        }
        
        [data-theme="dark"] .table-responsive {
            border: 1px solid #30363d;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        /* أنماط رسائل الخ��أ في الوضع المظلم */
        [data-theme="dark"] .error-message {
            color: #f85149;
        }
        
        /* أنماط النصوص التوضيحية في الوضع المظلم */
        [data-theme="dark"] p {
            color: #8b949e;
        }
        
        /* أنماط SweetAlert2 للوضع المظلم */
        .swal2-dark {
            background-color: #0d1117 !important;
            border: 1px solid #30363d !important;
        }
        
        .swal2-title-dark {
            color: #e6edf3 !important;
        }
        
        .swal2-content-dark {
            color: #c9d1d9 !important;
        }
        
        .swal2-dark .swal2-html-container {
            color: #c9d1d9 !important;
        }
        
        /* تحسينات إضافية للتقارير */
        .item-report-card:hover,
        .shift-report-card:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .info-section {
            animation: fadeInUp 0.5s ease-out;
        }
    </style>
</head>
<body>

<?php include 'sidebar.php'; ?>

<div class="container">
    <h2>تقارير الأصناف</h2>
    
    <!-- أزرار العمليات حسب الصلاحيات -->
    <div class="reports-actions-container" style="margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
        <?php if ($canExportExcel): ?>
        <form action="export_excel.php" method="post" style="display: inline;">
            <input type="hidden" name="store_id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">
            <button type="submit" class='action-btn export-btn' title="تصدير البيانات إلى ملف Excel">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </button>
        </form>
        <?php endif; ?>
        
        <?php if ($canViewComprehensiveReport): ?>
        <button class='action-btn summary-btn' onclick="showSummaryReport()" title="عرض التقرير الشامل للأصناف">
            <i class="fas fa-chart-pie"></i> تقرير شامل
        </button>
        <?php endif; ?>
        
        <?php if ($canViewQueries): ?>
        <button class='action-btn queries-btn' onclick="showItemQueries()" title="عرض استعلامات الأصناف">
            <i class="fas fa-search"></i> استعلامات الأصناف
        </button>
        <?php endif; ?>
    </div>

    <input type="text" id="searchField" class="search-bar" placeholder="ابحث عن صنف...">

    <div class="table-container">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>نوع الصنف</th>
                        <th>الكمية</th>
                        <th>سعر الجملة</th>
                        <th>سعر البيع</th>
                        <th>ربح القطعة الواحدة</th>
                        <th>إجمالي جملة الصنف</th>
                        <th>الربح الصافي</th>
                        <?php if ($canViewQueries): ?>
                        <th>إجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody id="itemsTable">
                    <?php
                    $total_item_price = 0;
                    $total_item_cost = 0;
                    $total_net_profit = 0;

                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            $profit_per_piece = $row['price'] - $row['cost'];
                            $total_cost = $row['cost'] * $row['quantity'];
                            $total_profit = $profit_per_piece * $row['quantity'];

                            $total_item_price += $row['price'] * $row['quantity'];
                            $total_item_cost += $total_cost;
                            $total_net_profit += $total_profit;

                            $encrypted_item_id = encrypt($row['item_id'], $key);
                            echo "<tr>
                                    <td>" . htmlspecialchars($row['name']) . "</td>
                                    <td>" . htmlspecialchars($row['type']) . "</td>
                                    <td>" . htmlspecialchars($row['quantity']) . "</td>
                                    <td>" . htmlspecialchars($row['cost']) . "</td>
                                    <td>" . htmlspecialchars($row['price']) . "</td>
                                    <td>" . htmlspecialchars($profit_per_piece) . "</td>
                                    <td>" . htmlspecialchars($total_cost) . "</td>
                                    <td>" . htmlspecialchars($total_profit) . "</td>";
                            
                            if ($canViewQueries) {
                                echo "<td>
                                        <button class='action-btn' onclick='showTransactions(\"" . htmlspecialchars($encrypted_item_id) . "\")'>
                                            <i class='fas fa-eye'></i>
                                        </button>
                                      </td>";
                            }
                            
                            echo "</tr>";
                        }
                    } else {
                        $colspan = $canViewQueries ? '9' : '8';
                        echo "<tr><td colspan='$colspan'>لا توجد أصناف حالياً</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="transactionsModal" class="modal">
    <div class="modal-content" >
        <span class="close" onclick="closeTransactionsModal()">&times;</span>
        <h2>تفاصيل العمليات</h2>
        <div class="modal-table-container">
            <table>
                <thead>
                    <tr>
                        <th>نوع العملية</th>
                        <th>الكمية</th>
                        <th>تاريخ العملية</th>
                    </tr>
                </thead>
                <tbody id="transactionsTable">
                    <!-- سيتم تعبئة البيانات هنا باستخدام JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if ($canViewComprehensiveReport): ?>
<?php
// حساب إجمالي فواتير الشراء
$purchase_invoices_total = 0;
$purchase_invoices_count = 0;
$stmt_purchase = $conn->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM purchase_invoices WHERE store_id = ?");
$stmt_purchase->bind_param("i", $store_id);
$stmt_purchase->execute();
$result_purchase = $stmt_purchase->get_result();
if ($row_purchase = $result_purchase->fetch_assoc()) {
    $purchase_invoices_total = $row_purchase['total'];
    $purchase_invoices_count = $row_purchase['count'];
}
$stmt_purchase->close();

// حساب إجمالي فواتير البيع بالجملة
$wholesale_invoices_total = 0;
$wholesale_invoices_count = 0;
$stmt_wholesale = $conn->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM wholesale_invoices WHERE store_id = ?");
$stmt_wholesale->bind_param("i", $store_id);
$stmt_wholesale->execute();
$result_wholesale = $stmt_wholesale->get_result();
if ($row_wholesale = $result_wholesale->fetch_assoc()) {
    $wholesale_invoices_total = $row_wholesale['total'];
    $wholesale_invoices_count = $row_wholesale['count'];
}
$stmt_wholesale->close();
?>
<?php endif; ?>


<script>
function showTransactions(encryptedItemId) {
    fetch(`get_transactions.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const transactionsTable = document.getElementById('transactionsTable');
                transactionsTable.innerHTML = '';
                data.transactions.forEach(transaction => {
                    let transactionTypeAr;
                    switch (transaction.transaction_type) {
                        case 'purchase':
                            transactionTypeAr = 'شراء';
                            break;
                        case 'wholesale_purchase':
                            transactionTypeAr = 'شراء جملة';
                            break;
                        case 'wholesale_sale':
                            transactionTypeAr = 'بيع جملة';
                            break;
                        case 'edit':
                            transactionTypeAr = 'تعديل';
                            break;
                        default:
                            transactionTypeAr = 'رصيد افتتاحي';
                    }

                    const transactionDate = new Date(transaction.transaction_date).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                        second: 'numeric',
                        hour12: true
                    });

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${transactionTypeAr}</td>
                        <td>${transaction.quantity}</td>
                        <td>${transactionDate}</td>
                    `;
                    transactionsTable.appendChild(row);
                });
                document.getElementById('transactionsModal').classList.add('active'); // Use "active" class
            } else {
                alert('Failed to fetch transactions.');
            }
        })
        .catch(error => {
            console.error('Error fetching transactions:', error);
        });
}

function closeTransactionsModal() {
    document.getElementById('transactionsModal').classList.remove('active'); // Use "active" class
}

function showSummaryReport() {
    <?php if ($canViewComprehensiveReport): ?>
    // تحديد الوضع الحالي (فاتح أم مظلم)
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // ألوان متكيفة مع الوضع
    const colors = {
        background: isDarkMode ? '#21262d' : '#f8f9fa',
        text: isDarkMode ? '#e6edf3' : '#333333',
        title: isDarkMode ? '#58a6ff' : '#3498db',
        totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
        totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
        totalText: isDarkMode ? '#4caf50' : '#2e7d32',
        cardBorder: isDarkMode ? '#30363d' : 'transparent',
        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
        profitBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d4edda',
        profitBorder: isDarkMode ? '#28a745' : '#155724',
        profitText: isDarkMode ? '#4caf50' : '#155724',
        costBg: isDarkMode ? 'linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%)' : '#fff3cd',
        costBorder: isDarkMode ? '#ffc107' : '#856404',
        costText: isDarkMode ? '#f7cc47' : '#856404',
        priceBg: isDarkMode ? 'linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%)' : '#e3f2fd',
        priceBorder: isDarkMode ? '#3498db' : '#1565c0',
        priceText: isDarkMode ? '#58a6ff' : '#1565c0',
        invoiceBg: isDarkMode ? 'linear-gradient(135deg, rgba(155, 89, 182, 0.2) 0%, rgba(155, 89, 182, 0.1) 100%)' : '#f3e5f5',
        invoiceBorder: isDarkMode ? '#9b59b6' : '#7b1fa2',
        invoiceText: isDarkMode ? '#bc8dbf' : '#7b1fa2'
    };

    Swal.fire({
        title: '<i class="fas fa-chart-pie" style="color: #28a745;"></i> تقرير الأصناف الشامل',
        html: `
            <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                <div style="text-align: center; margin-bottom: 25px;">
                    <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                        <i class="fas fa-boxes" style="margin-left: 10px;"></i>
                        ملخص الأصناف
                    </h3>
                    <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                        <?php echo htmlspecialchars($store_name); ?>
                    </p>
                    <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                </div>
                
                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                    <div class="item-report-card" style="background: ${colors.priceBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.priceBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <div style="color: ${colors.priceText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي سعر الأصناف</div>
                                <div style="color: ${colors.priceText}; font-size: 24px; font-weight: 700;"><?php echo number_format($total_item_price, 2); ?> جنيه</div>
                            </div>
                            <div class="report-icon" style="background: linear-gradient(135deg, #3498db, #2980b9); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);">
                                <i class="fas fa-tags" style="color: white; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-report-card" style="background: ${colors.costBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.costBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <div style="color: ${colors.costText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي تكلفة الأصناف</div>
                                <div style="color: ${colors.costText}; font-size: 20px; font-weight: 700;"><?php echo number_format($total_item_cost, 2); ?> جنيه</div>
                            </div>
                            <div class="report-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);">
                                <i class="fas fa-dollar-sign" style="color: white; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-report-card" style="background: ${colors.invoiceBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.invoiceBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <div style="color: ${colors.invoiceText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي فواتير الشراء</div>
                                <div style="color: ${colors.invoiceText}; font-size: 20px; font-weight: 700;"><?php echo number_format($purchase_invoices_total, 2); ?> جنيه</div>
                                <div style="color: ${colors.invoiceText}; font-size: 16px; margin-top: 5px;">(<?php echo $purchase_invoices_count; ?> فاتورة)</div>
                            </div>
                            <div class="report-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);">
                                <i class="fas fa-shopping-cart" style="color: white; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-report-card" style="background: ${colors.profitBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.profitBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <div style="color: ${colors.profitText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي فواتير البيع بالجملة</div>
                                <div style="color: ${colors.profitText}; font-size: 20px; font-weight: 700;"><?php echo number_format($wholesale_invoices_total, 2); ?> جنيه</div>
                                <div style="color: ${colors.profitText}; font-size: 16px; margin-top: 5px;">(<?php echo $wholesale_invoices_count; ?> فاتورة)</div>
                            </div>
                            <div class="report-icon" style="background: linear-gradient(135deg, #28a745, #20c997); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                                <i class="fas fa-chart-line" style="color: white; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                    <div style="background: ${colors.totalBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.totalBorder}; text-align: center; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                        <div style="position: relative; z-index: 1;">
                            <div style="color: ${colors.totalText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                <i class="fas fa-chart-pie" style="margin-left: 8px;"></i>
                                إجمالي الربح الصافي
                            </div>
                            <div style="color: ${colors.totalText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                <?php echo number_format($total_net_profit, 2); ?> جنيه
                            </div>
                            <div style="width: 80px; height: 2px; background: ${colors.totalBorder}; margin: 15px auto; border-radius: 1px;"></div>
                            <div style="color: ${colors.totalText}; font-size: 12px; opacity: 0.8;">
                                الربح الصافي من جميع الأصناف
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="info-section" style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title}; animation: fadeInUp 0.5s ease-out;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                        <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات إضافية</span>
                    </div>
                    <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                        • يتم حساب الربح الصافي بناءً على الفرق بين سعر البيع وسعر التكلفة<br>
                        • جميع المبالغ محسوبة بالجنيه المصري<br>
                        • التقرير يشمل جميع الأصناف المتاحة في المتجر<br>
                        • يمكن استخدام البحث لتصفية الأصناف المطلوبة
                    </div>
                </div>
            </div>
        `,
        icon: null,
        confirmButtonText: 'إغلاق',
        confirmButtonColor: '#28a745',
        width: '750px',
        customClass: {
            popup: isDarkMode ? 'swal2-dark' : '',
            title: isDarkMode ? 'swal2-title-dark' : '',
            content: isDarkMode ? 'swal2-content-dark' : ''
        },
        background: isDarkMode ? '#0d1117' : '#ffffff',
        color: isDarkMode ? '#e6edf3' : '#333333',
        showClass: {
            popup: 'animate__animated animate__fadeInUp animate__faster'
        },
        hideClass: {
            popup: 'animate__animated animate__fadeOutDown animate__faster'
        }
    });
    <?php else: ?>
    Swal.fire({
        icon: 'error',
        title: 'غير مسموح',
        text: 'ليس لديك صلاحية لعرض التقرير الشامل'
    });
    <?php endif; ?>
}


function showItemQueries() {
    <?php if ($canViewQueries): ?>
    // تحديد الوضع الحالي (فاتح أم مظلم)
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // ألوان متكيفة مع الوضع
    const colors = {
        background: isDarkMode ? '#21262d' : '#f8f9fa',
        text: isDarkMode ? '#e6edf3' : '#333333',
        title: isDarkMode ? '#58a6ff' : '#3498db',
        cardBorder: isDarkMode ? '#30363d' : 'transparent',
        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
        sectionBg: isDarkMode ? '#161b22' : '#ffffff',
        sectionBorder: isDarkMode ? '#30363d' : '#dee2e6',
        profitText: isDarkMode ? '#4caf50' : '#28a745',
        lowStockText: isDarkMode ? '#f85149' : '#dc3545',
        itemBg: isDarkMode ? '#0d1117' : '#ffffff',
        itemBorder: isDarkMode ? '#30363d' : '#e9ecef'
    };

    // عرض النافذة مع محتوى فارغ أولاً
    Swal.fire({
        title: '<i class="fas fa-search" style="color: #28a745;"></i> استعلامات الأصناف',
        html: `
            <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                <div style="text-align: center; margin-bottom: 25px;">
                    <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                        <i class="fas fa-chart-bar" style="margin-left: 10px;"></i>
                        تحليل الأصناف
                    </h3>
                    <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                        <?php echo htmlspecialchars($store_name); ?>
                    </p>
                    <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                </div>
                
                <div id="queriesContent" style="min-height: 300px;">
                    <div style="text-align: center; padding: 50px; color: ${colors.text};">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 15px;"></i>
                        <p>جاري تحميل البيانات...</p>
                    </div>
                </div>
                
                <div class="info-section" style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title};">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                        <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات الاستعلامات</span>
                    </div>
                    <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                        • الأصناف الأكثر ربحية: مرتبة حسب إجمالي الربح<br>
                        • الأصناف منخفضة المخزون: الأصناف التي تحتاج إعادة تموين<br>
                        • الإحصائيات السريعة: ملخص عام لحالة المخزون
                    </div>
                </div>
            </div>
        `,
        icon: null,
        confirmButtonText: 'إغلاق',
        confirmButtonColor: '#28a745',
        width: '800px',
        customClass: {
            popup: isDarkMode ? 'swal2-dark' : '',
            title: isDarkMode ? 'swal2-title-dark' : '',
            content: isDarkMode ? 'swal2-content-dark' : ''
        },
        background: isDarkMode ? '#0d1117' : '#ffffff',
        color: isDarkMode ? '#e6edf3' : '#333333',
        showClass: {
            popup: 'animate__animated animate__fadeInUp animate__faster'
        },
        hideClass: {
            popup: 'animate__animated animate__fadeOutDown animate__faster'
        },
        didOpen: () => {
            // تحميل البيانات بعد فتح النافذة
            loadItemQueriesData(colors);
        }
    });
    <?php else: ?>
    Swal.fire({
        icon: 'error',
        title: 'غير مسموح',
        text: 'ليس لديك صلاحية لعرض استعلامات الأصناف'
    });
    <?php endif; ?>
}


function loadItemQueriesData(colors) {
    <?php if ($canViewQueries): ?>
    const storeId = '<?php echo htmlspecialchars($encrypted_store_id); ?>';
    const queriesContent = document.getElementById('queriesContent');
    
    // تحميل جميع البيانات بشكل متوازي
    Promise.all([
        fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=most_profitable`).then(r => r.json()),
        fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=low_stock`).then(r => r.json()),
        fetch(`get_item_queries.php?store_id=${encodeURIComponent(storeId)}&query=quick_stats`).then(r => r.json())
    ]).then(([profitableData, lowStockData, statsData]) => {
        let html = '';
        
        // الإحصائيات السريعة
        if (statsData.success) {
            html += `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-tachometer-alt"></i>
                        إحصائيات سريعة
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="background: ${colors.sectionBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.sectionBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.title}; margin-bottom: 5px;">${statsData.stats.total_items}</div>
                            <div style="color: ${colors.text}; font-size: 14px; opacity: 0.8;">إجمالي الأصناف</div>
                        </div>
                        <div style="background: ${colors.sectionBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.sectionBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.lowStockText}; margin-bottom: 5px;">${statsData.stats.out_of_stock}</div>
                            <div style="color: ${colors.text}; font-size: 14px; opacity: 0.8;">أصناف نفدت</div>
                        </div>
                        <div style="background: ${colors.sectionBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.sectionBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.profitText}; margin-bottom: 5px;">${parseFloat(statsData.stats.avg_profit).toFixed(2)}</div>
                            <div style="color: ${colors.text}; font-size: 14px; opacity: 0.8;">متوسط الربح</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // الأصناف الأكثر ربحية
        if (profitableData.success && profitableData.items.length > 0) {
            html += `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-trophy"></i>
                        الأصناف الأكثر ربحية
                    </h4>
                    <div style="display: grid; gap: 10px;">
            `;
            profitableData.items.forEach((item, index) => {
                const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                html += `
                    <div style="background: ${colors.itemBg}; padding: 15px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; border: 1px solid ${colors.itemBorder}; box-shadow: ${colors.shadow};">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 18px;">${rankIcon}</span>
                            <span style="font-weight: 600; color: ${colors.text};">${item.name}</span>
                        </div>
                        <span style="color: ${colors.profitText}; font-weight: bold; font-size: 16px;">${parseFloat(item.profit).toFixed(2)} جنيه</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }
        
        // الأصناف منخفضة المخزون
        if (lowStockData.success && lowStockData.items.length > 0) {
            html += `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        الأصناف منخفضة المخزون
                    </h4>
                    <div style="display: grid; gap: 10px;">
            `;
            lowStockData.items.forEach(item => {
                html += `
                    <div style="background: ${colors.itemBg}; padding: 15px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; border-left: 3px solid ${colors.lowStockText}; border: 1px solid ${colors.itemBorder}; box-shadow: ${colors.shadow};">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-box" style="color: ${colors.lowStockText};"></i>
                            <span style="font-weight: 600; color: ${colors.text};">${item.name}</span>
                        </div>
                        <span style="color: ${colors.lowStockText}; font-weight: bold; font-size: 16px;">${item.quantity} قطعة</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }
        
        if (html === '') {
            html = `
                <div style="text-align: center; padding: 50px; color: ${colors.text};">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>لا توجد بيانات متاحة حالياً</p>
                </div>
            `;
        }
        
        queriesContent.innerHTML = html;
    }).catch(error => {
        console.error('Error loading queries data:', error);
        queriesContent.innerHTML = `
            <div style="text-align: center; padding: 50px; color: ${colors.lowStockText};">
                <i class="fas fa-exclamation-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                <p>حدث خطأ في تحميل البيانات</p>
            </div>
        `;
    });
    <?php endif; ?>
}

document.getElementById('searchField').onkeyup = function() {
    var filter = this.value.toLowerCase();
    var rows = document.querySelectorAll('#itemsTable tr');
    rows.forEach(row => {
        var name = row.cells[0].textContent.toLowerCase();
        var type = row.cells[1].textContent.toLowerCase();
        if (name.includes(filter) || type.includes(filter)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
