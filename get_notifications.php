<?php
/**
 * Get Notifications
 *
 * This file returns the latest notifications for the current user
 */

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

include 'db_connection.php';
require_once 'encryption_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get store_id from URL parameter or session
    $encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : (isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '');
    $store_id = null;

    if (!empty($encrypted_store_id)) {
        $key = getenv('ENCRYPTION_KEY');
        $store_id = decrypt($encrypted_store_id, $key);
    }

    // Get current user ID from encrypted account_id in session
    $user_id = null;
    if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
        $key = getenv('ENCRYPTION_KEY');
        $user_id = decrypt($_SESSION['account_id'], $key);
    }

    if (!$user_id) {
        echo json_encode([
            'success' => false,
            'message' => 'User not logged in'
        ]);
        exit;
    }

    // Fetch unread notifications from the last week based on store_id
    $query = "
        SELECT n.id,
               SUBSTRING(n.message, 1, 50) as title,
               n.message, n.created_at, n.created_by, n.status,
               0 as is_read,
               NULL as viewed_at,
               u.username as sender_name
        FROM notifications n
        LEFT JOIN accounts u ON n.created_by = u.account_id
        WHERE n.status = 'notread' AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
    ";

    $params = [];

    // Add store filter if store_id is provided
    if ($store_id) {
        $query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
        $params[] = $store_id;
    }

    $query .= " ORDER BY n.created_at DESC";

    if (count($params) > 0) {
        $stmt = $conn->prepare($query);
        $stmt->bind_param(str_repeat('i', count($params)), ...$params);
    } else {
        $stmt = $conn->prepare($query);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Fetch unread notifications count
    $unread_query = "
        SELECT COUNT(*) as unread_count
        FROM notifications n
        WHERE n.status = 'notread' AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
    ";

    $unread_params = [];

    if ($store_id) {
        $unread_query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
        $unread_params[] = $store_id;
    }

    if (count($unread_params) > 0) {
        $unread_stmt = $conn->prepare($unread_query);
        $unread_stmt->bind_param(str_repeat('i', count($unread_params)), ...$unread_params);
    } else {
        $unread_stmt = $conn->prepare($unread_query);
    }

    $unread_stmt->execute();
    $unread_result = $unread_stmt->get_result();
    $unread_assoc = $unread_result->fetch_assoc();
    $unreadCount = isset($unread_assoc['unread_count']) ? $unread_assoc['unread_count'] : 0;

    // Build notifications array
    $notifications = [];
    while ($notification = $result->fetch_assoc()) {
        $notifications[] = [
            'id' => $notification['id'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'created_at' => $notification['created_at'],
            'sender_name' => $notification['sender_name'] ?? 'النظام',
            'is_read' => $notification['is_read']
        ];
    }

    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'unread_count' => $unreadCount
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
