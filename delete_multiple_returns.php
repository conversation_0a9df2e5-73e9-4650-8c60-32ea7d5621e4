<?php
include 'db_connection.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get POST data
    $input_data = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input_data['ids']) && is_array($input_data['ids']) && !empty($input_data['ids'])) {
        $return_ids = $input_data['ids'];
        
        try {
            // Start transaction
            $conn->begin_transaction();
            
            // Create a parameter placeholder string for the IN clause
            $placeholders = str_repeat('?,', count($return_ids) - 1) . '?';
            
            // Delete the return records
            $stmt = $conn->prepare("DELETE FROM returns WHERE return_id IN ($placeholders)");
            
            // Bind all IDs as parameters
            $types = str_repeat('i', count($return_ids));
            $stmt->bind_param($types, ...$return_ids);
            $stmt->execute();
            
            if ($stmt->affected_rows > 0) {
                // Commit transaction
                $conn->commit();
                
                // Return success response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true, 
                    'message' => 'تم حذف المرتجعات بنجاح',
                    'deleted_count' => $stmt->affected_rows
                ]);
            } else {
                // Rollback if no rows were affected
                $conn->rollback();
                
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'لم يتم العثور على بيانات للحذف']);
            }
        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
        }
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'لم يتم توفير معرفات للحذف أو البيانات غير صالحة']);
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صالحة. يجب استخدام طلب POST']);
}

// Close connection
$conn->close();
?> 