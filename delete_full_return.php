<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $account_id = decrypt($data['account_id'], getenv('ENCRYPTION_KEY'));
    $order_date = $data['order_date'];

    // Fetch all returns in the order
    $stmt = $conn->prepare("SELECT return_id, item_id, quantity FROM returns WHERE account_id = ? AND DATE(time) = ?");
    $stmt->bind_param("is", $account_id, $order_date);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $return_id = $row['return_id'];
            
            // Delete the return record
            $stmt_delete = $conn->prepare("DELETE FROM returns WHERE return_id = ?");
            $stmt_delete->bind_param("i", $return_id);
            $stmt_delete->execute();
            $stmt_delete->close();
        }
        
        $stmt->close();
        $conn->close();
        
        echo json_encode(['success' => true]);
    } else {
        $stmt->close();
        $conn->close();
        
        echo json_encode(['success' => false, 'message' => 'No returns found for this date.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?> 