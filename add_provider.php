<?php
include 'db_connection.php';
include 'auth_check.php';

header('Content-Type: application/json');

// Retrieve and validate input
$provider_name = isset($_POST['provider_name']) ? trim($_POST['provider_name']) : null;

if (!$provider_name) {
    echo json_encode(['success' => false, 'error' => 'اسم المزود مطلوب.']);
    exit();
}

// Fetch the current ENUM values for the provider column
$result = $conn->query("SHOW COLUMNS FROM balance_transfers LIKE 'provider'");
$row = $result->fetch_assoc();
$enum_values = str_replace(["enum(", ")", "'"], "", $row['Type']);
$enum_array = explode(",", $enum_values);

// Ensure 'Unknown' is always included
if (!in_array('Unknown', $enum_array)) {
    $enum_array[] = 'Unknown';
}

// Check if the provider already exists
if (in_array($provider_name, $enum_array)) {
    echo json_encode(['success' => false, 'error' => 'المزود موجود بالفعل.']);
    exit();
}

// Add the new provider to the ENUM values
$enum_array[] = $provider_name;
$new_enum_values = "'" . implode("','", $enum_array) . "'";
$alter_query = "ALTER TABLE balance_transfers MODIFY COLUMN provider ENUM($new_enum_values) NOT NULL";

if ($conn->query($alter_query)) {
    // Log the provider addition action
    include 'encryption_functions.php';
    $key = getenv('ENCRYPTION_KEY');
    $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
                VALUES (?, 'add', 'balance_transfers', ?)";
    $description = "تم إضافة مزود جديد باسم $provider_name";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("is", $account_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم إضافة المزود بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في تحديث خيارات المزود.']);
}

$conn->close();
?>
