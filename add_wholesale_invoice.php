<?php

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_GET['store_id'], $key);
$account_id = $_GET['account_id']; // Use the encrypted account ID directly

$sql_categories = "SELECT * FROM categories WHERE store_id = ?";
$stmt = $conn->prepare($sql_categories);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$categories_result = $stmt->get_result();
$stmt->close();

// Get account_id for favorites
$decrypted_account_id = decrypt($account_id, $key);

if ($decrypted_account_id) {
    $sql_items = "SELECT i.*, c.name AS category_name,
                  (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite,
                  (SELECT COUNT(*) FROM item_images WHERE item_id = i.item_id) AS image_count
                  FROM items i
                  JOIN categories c ON i.category_id = c.category_id
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("iii", $decrypted_account_id, $store_id, $decrypted_account_id);
} else {
    $sql_items = "SELECT i.*, c.name AS category_name, 0 as is_favorite,
                  (SELECT COUNT(*) FROM item_images WHERE item_id = i.item_id) AS image_count
                  FROM items i
                  JOIN categories c ON i.category_id = c.category_id
                  WHERE c.store_id = ? AND i.status = 'active'
                  ORDER BY i.name ASC";
    $stmt = $conn->prepare($sql_items);
    $stmt->bind_param("i", $store_id);
}

$stmt->execute();
$items_result = $stmt->get_result();
$stmt->close();

$sql_stores = "SELECT * FROM stores";
$stores_result = $conn->query($sql_stores);
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فاتورة بيع بالجملة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/invoice.css">

    <style>
        

        /* أنماط مربعات التصنيفات */
        .category-boxes {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
            justify-content: center;
            padding: 10px;
        }
        
        .category-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 130px;
            height: 130px;
            background-color: var(--color-secondary);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid #eee;
            text-align: center;
        }
        
        .category-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-color: var(--color-primary);
        }
        
        .category-box.active {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
            transform: translateY(-3px);
        }
        
        .category-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                135deg, 
                rgba(255, 255, 255, 0.3) 0%, 
                rgba(255, 255, 255, 0) 60%
            );
            z-index: 1;
        }
        
        .category-box i {
            font-size: 38px;
            margin-bottom: 12px;
            color: var(--color-primary);
            z-index: 2;
            position: relative;
        }
        
        .category-box.active i {
            color: white;
        }
        
        .category-box span {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            z-index: 2;
            position: relative;
        }
        
        /* تنسيق للوضع المظلم */
        [data-theme="dark"] .category-box {
            background-color: var(--color-secondary);
            border-color: #444;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="dark"] .category-box:hover {
            border-color: var(--color-primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
        }
        
        [data-theme="dark"] .category-box.active {
            background-color: var(--color-primary);
            border-color: var(--color-primary);
        }
        
        [data-theme="dark"] .category-box::before {
            background: linear-gradient(
                135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                rgba(255, 255, 255, 0) 60%
            );
        }
        
        /* تحسين العرض على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .category-box {
                width: 110px;
                height: 110px;
                padding: 10px;
            }
            
            .category-box i {
                font-size: 30px;
                margin-bottom: 8px;
            }
            
            .category-box span {
                font-size: 14px;
            }
        }
        
        @media (max-width: 480px) {
            .category-box {
                width: 90px;
                height: 90px;
            }
            
            .category-box i {
                font-size: 24px;
                margin-bottom: 5px;
            }
            
            .category-box span {
                font-size: 12px;
            }
            
            .category-boxes {
                gap: 10px;
            }
        }
          /* تنسيق شريط البحث */
    .search-bar {
        width: 60%;
        padding: 12px 15px;
        border-radius: 25px;
        border: 1px solid #ddd;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        margin: 15px auto;
        top: 100px;
        display: block;
    }

    /* إخفاء عمود الباركود وإضافة أنماط الصور */
    .barcode-col { display: none; }
    .has-images-icon i { color: #28a745; }
    .no-images-icon i { color: #aaa; }
    .no-images-icon { cursor: default; pointer-events: none; opacity: 0.6; }
    .images-gallery { display: flex; flex-wrap: wrap; gap: 10px; justify-content: center; }
    </style>

</head>
<body>
<div class="container">
    <div class="categories">
        <h3>تصنيف</h3>
        <input type="text" id="categorySearch" class="search-bar" placeholder="ابحث عن تصنيف...">
        <div class="category-boxes" id="categoryBoxes">
            <!-- إضافة مربع كل الأصناف بشكل افتراضي -->
            <div class="category-box active" data-category-id="all">
                <i class="fas fa-boxes"></i>
                <span>كل الأصناف</span>
            </div>
            <?php
            if ($categories_result->num_rows > 0) {
                while($row = $categories_result->fetch_assoc()) {
                    $encrypted_category_id = encrypt($row['category_id'], $key);
                    echo '<div class="category-box" data-category-id="' . $encrypted_category_id . '">';
                    echo '<i class="fas fa-box"></i>';
                    echo '<span>' . $row['name'] . '</span>';
                    echo '</div>';
                }
            }
            ?>
        </div>
    </div>

    <div class="items">
        <h3>الأصناف</h3>
        <input type="search" id="itemSearch" class="search-bar" placeholder="ابحث عن صنف...">
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th class="barcode-col">الباركود</th>
                    <th>الكمية</th>
                    <th>الصور</th>
                </tr>
            </thead>
            <tbody id="itemsList">
                <?php
                if ($items_result->num_rows > 0) {
                    while($row = $items_result->fetch_assoc()) {
                        $quantityClass = $row['quantity'] < 10 ? 'red' : 'green';
                        $favoriteIcon = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                        $favoriteClass = isset($row['is_favorite']) && $row['is_favorite'] > 0 ? 'favorite-item' : '';
                        $image_class = $row['image_count'] > 0 ? 'has-images-icon' : 'no-images-icon';
                        $image_title = $row['image_count'] > 0 ? 'عرض الصور' : 'لا توجد صور';

                        $encId = htmlspecialchars(encrypt($row['item_id'],$key));
                        $row['encrypted_id'] = $encId;
                        $imageBtn = ($row['image_count'] > 0)
                            ? "<button class='action-btn has-images-icon' type='button' title='عرض الصور' onclick=\"viewItemImages('{$encId}', event)\"><i class='fas fa-eye'></i></button>"
                            : "<button class='action-btn no-images-icon' type='button' title='لا توجد صور' disabled onclick='event.stopPropagation();'><i class='fas fa-eye-slash'></i></button>";

                        $rowJson = htmlspecialchars(json_encode($row), ENT_QUOTES, 'UTF-8');
                        echo "<tr class='{$favoriteClass}' data-item='{$rowJson}' onclick='addItemToInvoice(JSON.parse(this.dataset.item))'>
                                <td>{$favoriteIcon}{$row['name']}</td>
                                <td class='barcode-col'>{$row['barcode']}</td>
                                <td class='item-quantity {$quantityClass}'>{$row['quantity']}</td>
                                <td>{$imageBtn}</td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='4'>لا توجد أصناف حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>

    <div class="selected-items">
        <h3>الأصناف المختارة</h3>
        <form method="POST" action="save_wholesale_invoice.php" id="wholesaleInvoiceForm" enctype="multipart/form-data">
            <input type="hidden" name="store_id" value="<?php echo htmlspecialchars($_GET['store_id']); ?>">
            <input type="hidden" name="account_id" value="<?php echo htmlspecialchars($account_id); ?>"> <!-- Pass account_id directly -->
            <input type="hidden" name="total_amount" id="total_amount_input" />
            
            <div>
                <label for="invoice_status">حالة الفاتورة:</label>
                <select id="invoice_status" name="invoice_status" class="input-field" required>
                    <option value="confirmed" selected>مؤكدة</option>
                    <option value="pending">معلقة</option>
                </select>
            </div>

            <div>
                <label for="buyer_select">اختر المخزن أو شخص:</label>
                <select id="buyer_select" name="buyer" class="input-field" onchange="toggleBuyerOptions(this.value)" required>
                    <option value="">اختر...</option>
                    <?php
                    if ($stores_result->num_rows > 0) {
                        while($row = $stores_result->fetch_assoc()) {
                            $encrypted_store_id = encrypt($row['store_id'], $key);
                            echo "<option value='{$encrypted_store_id}'>{$row['name']}</option>";
                        }
                    } else {
                        echo "<option value=''>لا توجد مخازن حالياً</option>";
                    }
                    ?>
                    <option value="person">شخص</option>
                </select>
            </div>
            
            <div id="person_input_container" style="display: none;">
                <label for="person_name">اسم الشخص:</label>
                <input type="text" id="person_name" name="person_name" class="input-field" />
            </div>
            
            <div id="account_buyer_container" style="display: none;">
                <label for="account_buyer">اختر الشخص المشتري:</label>
                <select id="account_buyer" name="account_buyer" class="input-field">
                    <option value="">اختر...</option>
                    <?php
                    $sql_accounts = "SELECT account_id, name FROM accounts WHERE role = 'user'";
                    $accounts_result = $conn->query($sql_accounts);
                    if ($accounts_result->num_rows > 0) {
                        while($row = $accounts_result->fetch_assoc()) {
                            echo "<option value='{$row['account_id']}'>{$row['name']}</option>";
                        }
                    } else {
                        echo "<option value=''>لا توجد حسابات حالياً</option>";
                    }
                    ?>
                </select>
            </div>
            
            <!-- Begin: New Image Upload Section -->
            <div>
                <label for="invoice_images">صور الفاتورة:</label>
                <div class="image-upload-container">
                    <label for="invoice_images" class="image-upload-label">
                        <i class="fas fa-upload"></i> اختر الصور
                    </label>
                    <input type="file" id="invoice_images" name="invoice_images[]" multiple accept="image/*" class="image-upload-input">
                    <div id="image-preview" class="image-preview"></div>
                </div>
            </div>
            <!-- End: New Image Upload Section -->
            
            <table>
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th class="barcode-col">الباركود</th>
                        <th>الكمية</th>
                        <th>الصور</th>
                        <th>جملة</th>
                        <th>إجمالي</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="selectedItemsTable">
                    <!-- Selected items will be added here -->
                </tbody>
            </table>
            <div id="total_amount" class="total-display">
                <span>إجمالي الفاتورة: <span id="total_amount_value">0.00</span></span>
                <button type="button" class="add-btn" onclick="confirmSaveInvoice()">حفظ الفاتورة</button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    function loadItems(encryptedCategoryId) {
        // إذا التصنيف هو "كل الأصناف"، عرض جميع الأصناف
        if (encryptedCategoryId === 'all') {
            document.querySelectorAll('#itemsList tr').forEach(row => {
                row.style.display = '';
            });
            return;
        }
        
        const accountId = "<?php echo htmlspecialchars($account_id); ?>";
        fetch(`get_items.php?category_id=${encodeURIComponent(encryptedCategoryId)}&account_id=${accountId}`)
            .then(response => response.json())
            .then(data => {
                const itemsList = document.getElementById('itemsList');
                
                // عرض فقط العناصر للتصنيف المحدد
                document.querySelectorAll('#itemsList tr').forEach(row => {
                    row.style.display = 'none';
                });
                
                data.items.forEach(item => {
                    const quantityClass = item.quantity < 10 ? 'red' : 'green';
                    const favoriteIcon = item.is_favorite && item.is_favorite > 0 ? '<i class="fas fa-star" style="color: #ffc107; margin-left: 5px;" title="مفضل"></i>' : '';
                    const favoriteClass = item.is_favorite && item.is_favorite > 0 ? 'favorite-item' : '';
                    const image_class = item.image_count > 0 ? 'has-images-icon' : 'no-images-icon';
                    const image_title = item.image_count > 0 ? 'عرض الصور' : 'لا توجد صور';

                    // البحث عن الصف الحالي أولاً
                    let tr = null;
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        const cellText = row.querySelector('td')?.textContent;
                        if (cellText && cellText.includes(item.name)) {
                            tr = row;
                            tr.style.display = '';
                            tr.className = favoriteClass;
                            // تحديث محتوى الخلية لإضافة أيقونة المفضلة
                            const firstCell = tr.querySelector('td');
                            if (firstCell && !firstCell.innerHTML.includes('fa-star')) {
                                firstCell.innerHTML = favoriteIcon + item.name;
                            }
                        }
                    });

                    // إذا لم يتم العثور على الصف، قم بإنشائه
                    if (!tr) {
                        tr = document.createElement('tr');
                        tr.className = favoriteClass;
                        tr.innerHTML = `
                            <td>${favoriteIcon}${item.name}</td>
                            <td class='barcode-col'>${item.barcode}</td>
                            <td class="item-quantity ${quantityClass}">${item.quantity}</td>
                            <td><button class='action-btn ${image_class}' type='button' title='${image_title}' onclick='viewItemImages("${item.encrypted_id}", event)'><i class='fas fa-eye'></i></button></td>
                        `;
                        tr.onclick = () => addItemToInvoice(item);
                        itemsList.appendChild(tr);
                    }
                });
            })
            .catch(error => console.error('Error:', error));
    }

    function addItemToInvoice(item) {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        let existingRow = document.querySelector(`#selectedItemsTable tr[data-item-id="${item.item_id}"]`);
        if (existingRow) {
            const quantityInput = existingRow.querySelector('input[type="number"]');
            quantityInput.value = parseInt(quantityInput.value) + 1;
        } else {
            const row = document.createElement('tr');
            row.setAttribute('data-item-id', item.item_id);
            row.innerHTML = `
                <td>${item.name}</td>
                <td class="barcode-col">${item.barcode}</td>
                <td>
                    <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" name="items[${item.item_id}][quantity]" value="1" min="1" class="quantity-input device-status green" required oninput="updateTotal()">
                    <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">
                        <i class="fas fa-plus"></i>
                    </button>
                </td>
                <td>
                    ${item.image_count > 0 ? `<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages('${item.encrypted_id}', event)"><i class="fas fa-eye"></i></button>` : `<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>`}
                </td>
                <td>${item.cost}</td>
                <td class="item-total">${item.cost}</td>
                <td>
                    <button type="button" class="action-btn" onclick="removeItemFromInvoice(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                <input type="hidden" name="items[${item.item_id}][total]" value="${item.cost}" class="item-total-input">
                <input type="hidden" name="items[${item.item_id}][barcode]" value="${item.barcode}">
                <input type="hidden" name="items[${item.item_id}][name]" value="${item.name}">
            `;
            selectedItemsTable.appendChild(row);
        }
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function removeItemFromInvoice(button) {
        const row = button.parentElement.parentElement;
        row.remove();
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function increaseQuantity(button) {
        const input = button.previousElementSibling;
        input.value = parseInt(input.value) + 1;
        updateTotal();
        saveTempItems(); // Save items to the temporary JSON file
    }

    function decreaseQuantity(button) {
        const input = button.nextElementSibling;
        if (input.value > 1) {
            input.value = parseInt(input.value) - 1;
            updateTotal();
            saveTempItems(); // Save items to the temporary JSON file
        }
    }

    function updateTotal() {
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        let total = 0;
        rows.forEach(row => {
            const quantityInput = row.querySelector('input[type="number"]');
            const costCell = row.cells[4]; // تحديث الفهرس بعد إضافة عمود الصور
            const totalCell = row.cells[5]; // تحديث الفهرس بعد إضافة عمود الصور

            if (!quantityInput || !costCell || !totalCell) {
                console.error('Missing elements in row:', row);
                return; // Skip this row if any required element is missing
            }

            const quantity = parseFloat(quantityInput.value) || 0;
            const cost = parseFloat(costCell.textContent) || 0;
            const subtotal = quantity * cost;

            totalCell.textContent = subtotal.toFixed(2);
            total += subtotal;
        });

        const totalAmountElement = document.getElementById('total_amount_value');
        const totalAmountInput = document.getElementById('total_amount_input');

        if (totalAmountElement) {
            totalAmountElement.textContent = total.toFixed(2);
        }

        if (totalAmountInput) {
            totalAmountInput.value = total.toFixed(2);
        }

        // Update temporary JSON file with the latest quantity values
        saveTempItems();
    }

    document.getElementById('categorySearch').oninput = function() {
        const filter = this.value.toLowerCase();
        const categoryBoxes = document.getElementById('categoryBoxes');
        const categoryBoxesChildren = categoryBoxes.children;
        for (let i = 0; i < categoryBoxesChildren.length; i++) {
            const categoryBox = categoryBoxesChildren[i];
            const text = categoryBox.textContent || categoryBox.innerText;
            categoryBox.style.display = text.toLowerCase().indexOf(filter) > -1 ? '' : 'none';
        }
    }

    document.getElementById('itemSearch').oninput = function() {
        const filter = this.value.toLowerCase();
        const itemsList = document.getElementById('itemsList');
        const items = itemsList.getElementsByTagName('tr');
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const text = item.textContent || item.innerText;
            item.style.display = text.toLowerCase().indexOf(filter) > -1 ? '' : 'none';
        }
    }

    function toggleBuyerOptions(value) {
        const personInputContainer = document.getElementById('person_input_container');
        const accountBuyerContainer = document.getElementById('account_buyer_container');
        if (value === 'person') {
            personInputContainer.style.display = 'block';
            accountBuyerContainer.style.display = 'none';
        } else if (value) {
            personInputContainer.style.display = 'none';
            accountBuyerContainer.style.display = 'block';
            // If no buyer is selected yet, default to the first option (skipping the placeholder)
            const accountBuyer = document.getElementById('account_buyer');
            if (!accountBuyer.value && accountBuyer.options.length > 1) {
                accountBuyer.value = accountBuyer.options[1].value;
            }
        } else {
            personInputContainer.style.display = 'none';
            accountBuyerContainer.style.display = 'none';
        }
        saveTempItems(); // Save buyer details on change
    }

    <?php if (isset($_SESSION['message'])): ?>
        Swal.fire({
            icon: '<?php echo $_SESSION['message_type']; ?>',
            title: '<?php echo $_SESSION['message']; ?>',
            showConfirmButton: false,
            timer: 3000
        }).then(() => {
            window.location.href = 'wholesale_invoices.php?store_id=<?php echo urlencode($_GET['store_id']); ?>';
        });
        <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
    <?php endif; ?>

    document.addEventListener('DOMContentLoaded', function () {
        const accountId = "<?php echo htmlspecialchars($_GET['account_id']); ?>"; // Pass account_id
        fetch(`load_temp_wholesale_invoice.php?account_id=${encodeURIComponent(accountId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.items.length > 0) {
                    const selectedItemsTable = document.getElementById('selectedItemsTable');
                    const itemIds = data.items.map(item => item.item_id).join(',');

                    // Restore buyer details
                    if (data.buyer_type === 'person') {
                        document.getElementById('buyer_select').value = 'person';
                        toggleBuyerOptions('person');
                        document.getElementById('person_name').value = data.buyer_name || '';
                    } else if (data.buyer_type === 'branch') {
                        const buyerBranchIdEncrypted = data.buyer_branch_id;
                        fetch(`get_store_name.php?store_id=${encodeURIComponent(buyerBranchIdEncrypted)}`)
                            .then(response => response.json())
                            .then(storeData => {
                                if (storeData.success) {
                                    document.getElementById('buyer_select').value = buyerBranchIdEncrypted;
                                    toggleBuyerOptions(buyerBranchIdEncrypted);

                                    const buyerSelect = document.getElementById('buyer_select');
                                    const existingOption = Array.from(buyerSelect.options).find(option => option.value === buyerBranchIdEncrypted);
                                    if (!existingOption) {
                                        const newOption = document.createElement('option');
                                        newOption.value = buyerBranchIdEncrypted;
                                        newOption.textContent = storeData.name;
                                        newOption.selected = true;
                                        buyerSelect.appendChild(newOption);
                                    }

                                    document.getElementById('account_buyer').value = data.buyer_account_id || '';
                                } else {
                                    console.error('Failed to fetch store name:', storeData.error);
                                }
                            })
                            .catch(error => console.error('Error fetching store name:', error));
                    }

                    // Fetch item details from the database
                    fetch(`get_item_details.php?item_ids=${encodeURIComponent(itemIds)}`)
                        .then(response => response.json())
                        .then(itemDetails => {
                            data.items.forEach(item => {
                                const details = itemDetails.find(detail => detail.item_id == item.item_id);
                                if (details) {
                                    const row = document.createElement('tr');
                                    row.setAttribute('data-item-id', item.item_id);
                                    row.innerHTML = `
                                        <td>${details.name}</td>
                                        <td class="barcode-col">${details.barcode}</td>
                                        <td>
                                            <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" name="items[${item.item_id}][quantity]" value="${item.quantity}" min="1" step="1" class="quantity-input device-status green" required oninput="updateTotal()">
                                            <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </td>
                                        <td>
                                            ${details.image_count > 0 ? `<button type="button" class="action-btn has-images-icon" title="عرض الصور" onclick="viewItemImages('${details.encrypted_id}', event)"><i class="fas fa-eye"></i></button>` : `<button type="button" class="action-btn no-images-icon" title="لا توجد صور" disabled onclick="event.stopPropagation();"><i class="fas fa-eye-slash"></i></button>`}
                                        </td>
                                        <td>${item.total}</td>
                                        <td>${(item.quantity * item.total).toFixed(2)}</td>
                                        <td>
                                            <button type="button" class="action-btn" onclick="removeItemFromInvoice(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                        <input type="hidden" name="items[${item.item_id}][total]" value="${item.total}">
                                        <input type="hidden" name="items[${item.item_id}][barcode]" value="${details.barcode}">
                                        <input type="hidden" name="items[${item.item_id}][name]" value="${details.name}">
                                    `;
                                    selectedItemsTable.appendChild(row);
                                } else {
                                    console.error('Item details not found for item_id:', item.item_id);
                                }
                            });
                            updateTotal();
                        })
                        .catch(error => console.error('Error fetching item details:', error));
                }
            })
            .catch(error => console.error('Error loading temporary items:', error));
    });

    function saveTempItems() {
        const formData = new FormData();
        formData.append('account_id', "<?php echo htmlspecialchars($account_id); ?>"); // Pass account_id

        const items = [];
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        rows.forEach(row => {
            const itemId = row.getAttribute('data-item-id');
            const quantity = row.querySelector('input[type="number"]').value;
            const total = row.cells[4].textContent; // تحديث الفهرس بعد إضافة عمود الصور
            items.push({ item_id: itemId, quantity: parseFloat(quantity), total: parseFloat(total) });
        });

        // Save buyer details
        const buyerType = document.getElementById('buyer_select').value === 'person' ? 'person' : 'branch';
        const buyerName = buyerType === 'person' ? document.getElementById('person_name').value : null;
        const buyerBranchId = buyerType === 'branch' ? document.getElementById('buyer_select').value : null;
        const buyerAccountId = buyerType === 'branch' ? document.getElementById('account_buyer').value : null;

        formData.append('items', JSON.stringify(items));
        formData.append('buyer_type', buyerType);
        formData.append('buyer_name', buyerName);
        formData.append('buyer_branch_id', buyerBranchId);
        formData.append('buyer_account_id', buyerAccountId);

        fetch('save_temp_wholesale_invoice.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Error saving temporary items:', data.error);
            }
        })
        .catch(error => console.error('Error saving temporary items:', error));
    }

    // Ensure saveTempItems is called when buyer details are updated
    document.getElementById('buyer_select').addEventListener('change', saveTempItems);
    document.getElementById('person_name').addEventListener('input', saveTempItems);
    document.getElementById('account_buyer').addEventListener('change', saveTempItems);

    // Remove previous submit event listeners by replacing the form element
    const form = document.getElementById('wholesaleInvoiceForm');
    form.replaceWith(form.cloneNode(true));

    // Add new submit event listener
    document.getElementById('wholesaleInvoiceForm').addEventListener('submit', async function(event) {
        event.preventDefault();
        // -- Check if items exist
        const rows = document.querySelectorAll('#selectedItemsTable tr');
        if (!rows.length) {
            return Swal.fire({ icon: 'warning', title: 'لا توجد أصناف مضافة', text: 'أضف صنف واحد على الأقل.' });
        }
        // -- Upload images
        Swal.fire({ title: 'جاري رفع الصور...', allowOutsideClick: false, didOpen: Swal.showLoading });
        let imagePaths = [];
        try {
            const files = document.getElementById('invoice_images').files;
            const uploadResults = await Promise.all(
                Array.from(files).map(async file => {
                    const compressed = await compressImage(file);
                    const fd = new FormData();
                    fd.append('image', compressed);
                    fd.append('type', 'sale');
                    const res = await fetch('upload_image.php', { method: 'POST', body: fd });
                    return res.json();
                })
            );
            imagePaths = uploadResults.filter(r => r.path).map(r => r.path);
        } catch (err) {
            Swal.close();
            return Swal.fire({ icon: 'error', title: 'فشل رفع الصور', text: err.message });
        }
        Swal.close();
        // -- Clear the original file input so it doesn't get included in the final FormData
        const fileInput = document.getElementById('invoice_images');
        fileInput.value = ''; 
        // -- Create the final FormData and add the uploaded paths
        const invoiceFormData = new FormData(this);
        imagePaths.forEach((p, i) => invoiceFormData.append(`uploaded_images[${i}]`, p));
        
        console.log('Final payload:');
        for (let [k, v] of invoiceFormData.entries()) console.log(k, v);

        // -- Submit the data
        try {
          const saveResponse = await fetch('save_wholesale_invoice.php', {
              method: 'POST',
              body: invoiceFormData,
              credentials: 'include' // include cookies so PHP session is available
          });
          const saveJson = await saveResponse.json();
          if (saveJson.success) {
              Swal.fire({
                  icon: 'success',
                  title: 'تم حفظ الفاتورة بنجاح',
                  showConfirmButton: false,
                  timer: 1500
              }).then(() => {
                  window.location.href = 'wholesale_invoices.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>';
              });
          } else {
              throw new Error(saveJson.message || 'خطأ غير متوقع');
          }
        } catch (err) {
          Swal.fire({ icon: 'error', title: 'خطأ أثناء الحفظ', text: err.message });
        }
    });

    async function compressImage(file) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const maxDim = 800;
                let { width, height } = img;
                if (width > height && width > maxDim) {
                    height = height * (maxDim / width);
                    width = maxDim;
                } else if (height > maxDim) {
                    width = width * (maxDim / height);
                    height = maxDim;
                }
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(blob => {
                    resolve(new File([blob], file.name, { type: 'image/jpeg' }));
                }, 'image/jpeg', 0.75);
            };
            img.src = URL.createObjectURL(file);
        });
    }

    document.getElementById('invoice_images').addEventListener('change', function(event) {
        const previewContainer = document.getElementById('image-preview');
        previewContainer.innerHTML = ''; // Clear previous previews
        const files = event.target.files;
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.createElement('div');
                previewDiv.style.position = 'relative';
                previewDiv.style.width = '80px';
                previewDiv.style.height = '80px';
                previewDiv.style.border = '1px solid #ddd';
                previewDiv.style.borderRadius = '5px';
                previewDiv.style.overflow = 'hidden';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" style="width: 100%; height: 100%; object-fit: cover;">
                    <i class="fas fa-times remove-icon" style="position: absolute; top: 5px; right: 5px; cursor: pointer; background: white; border-radius: 50%; padding: 5px;"></i>
                `;
                previewDiv.querySelector('.remove-icon').addEventListener('click', () => {
                    previewDiv.remove();
                });
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        });
    });

    function confirmSaveInvoice() {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'هل تريد حفظ الفاتورة؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، حفظ',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const form = document.getElementById('wholesaleInvoiceForm');
                const formData = new FormData(form);

                // Show loading modal
                Swal.fire({
                    title: 'جاري حفظ الفاتورة...',
                    text: 'يرجى الانتظار حتى يتم حفظ الفاتورة.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Submit the form data via fetch
                fetch('save_wholesale_invoice.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    Swal.close(); // Close the loading modal
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم إضافة الفاتورة بنجاح.',
                            showConfirmButton: false,
                            timer: 3000
                        }).then(() => {
                            // Redirect to wholesale invoices page
                            window.location.href = `wholesale_invoices.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>`;
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'فشل في إضافة الفاتورة.',
                            text: data.message || 'حدث خطأ أثناء إضافة الفاتورة.',
                            showConfirmButton: true
                        });
                    }
                })
                .catch(error => {
                    Swal.close(); // Close the loading modal
                    console.error('Error saving wholesale invoice:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'فشل في إضافة الفاتورة.',
                        text: 'حدث خطأ أثناء إضافة الفاتورة.',
                        showConfirmButton: true
                    });
                });
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // إضافة معالج أحداث لمربعات التصنيف
        const categoryBoxes = document.querySelectorAll('.category-box');
        
        categoryBoxes.forEach(box => {
            box.addEventListener('click', function() {
                // إزالة الكلاس active من جميع المربعات
                categoryBoxes.forEach(b => b.classList.remove('active'));
                
                // إضافة الكلاس active للمربع المحدد
                this.classList.add('active');
                
                const categoryId = this.getAttribute('data-category-id');
                
                // إذا تم تحديد "كل الأصناف"
                if (categoryId === 'all') {
                    // عرض جميع الأصناف
                    document.querySelectorAll('#itemsList tr').forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    // تحميل الأصناف للتصنيف المحدد
                    loadItems(categoryId);
                }
            });
        });
        
        // معالج أحداث البحث في التصنيفات
        document.getElementById('categorySearch').addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            
            document.querySelectorAll('.category-box').forEach(box => {
                const text = box.querySelector('span').textContent.toLowerCase();
                if (text.includes(searchText) || box.getAttribute('data-category-id') === 'all') {
                    box.style.display = '';
                } else {
                    box.style.display = 'none';
                }
            });
        });
    });

    // View Item Images Functions
    function viewItemImages(encryptedItemId, evt){
        if(evt){evt.stopPropagation();}
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(r=>r.json())
        .then(d=>{
            if(d.success){
                const container=document.getElementById('modalImagesContainer');
                container.innerHTML='';
                document.getElementById('modalItemName').textContent=d.item.name;
                if(d.item.images&&d.item.images.length>0){
                    d.item.images.forEach(img=>{
                        const el=document.createElement('img');el.src=img.img_path;el.style.maxWidth='150px';el.style.borderRadius='8px';container.appendChild(el);
                    });
                }else{container.innerHTML='<p>لا توجد صور متاحة لهذا الصنف.</p>';}
                document.getElementById('viewImagesModal').classList.add('active');
            }else{Swal.fire({icon:'error',title:'خطأ',text:'فشل في جلب الصور'});}
        }).catch(err=>{console.error(err);Swal.fire({icon:'error',title:'خطأ',text:'تعذر الاتصال بالخادم'});});
    }
    function closeViewImagesModal(){document.getElementById('viewImagesModal').classList.remove('active');}
    window.onclick=function(e){const m=document.getElementById('viewImagesModal');if(e.target===m){m.classList.remove('active');}};

    // ميزة قارئ الباركود
    let globalBarcode = "";
    let barcodeTimer = null;

    document.addEventListener('keypress', function(e) {
        // تجاهل إذا كان التركيز على حقل إدخال أو إذا كان المفتاح Enter
        if (document.activeElement.tagName.toLowerCase() === "input" || e.key === "Enter") return;

        globalBarcode += e.key;

        if (barcodeTimer) clearTimeout(barcodeTimer);

        barcodeTimer = setTimeout(() => {
            processBarcode(globalBarcode);
            globalBarcode = "";
        }, 150);
    });

    function processBarcode(barcode) {
        // البحث عن الصنف بناءً على الباركود من الأصناف المعروضة حالياً
        const itemRows = document.querySelectorAll('#itemsList tr');
        let found = null;

        itemRows.forEach(row => {
            const barcodeCell = row.querySelector('.barcode-col');
            if (barcodeCell && barcodeCell.textContent.trim() === barcode.trim()) {
                const itemData = row.getAttribute('data-item');
                if (itemData) {
                    try {
                        found = JSON.parse(itemData);
                    } catch (e) {
                        console.error('Error parsing item data:', e);
                    }
                }
            }
        });

        if (found) {
            addItemToInvoice(found);
            // إظهار رسالة نجاح
            Swal.fire({
                icon: 'success',
                title: 'تم إضافة الصنف',
                text: `تم إضافة ${found.name} للفاتورة`,
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        } else {
            // إظهار رسالة خطأ
            Swal.fire({
                icon: 'error',
                title: '��نف غير موجود',
                text: "لم يتم العثور على صنف بهذا الباركود: " + barcode,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }
</script>
<script src="js/theme.js"></script>

<!-- Modal for viewing item images -->
<div id="viewImagesModal" class="modal">
    <div class="modal-content" style="max-width:800px;">
        <span class="close" onclick="closeViewImagesModal()">&times;</span>
        <h2 id="modalItemName">صور الصنف</h2>
        <div id="modalImagesContainer" class="images-gallery"></div>
    </div>
</div>

</body>
</html>

<?php
$conn->close();
?>
