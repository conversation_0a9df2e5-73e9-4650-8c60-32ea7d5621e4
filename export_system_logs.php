<?php
// تقييد الوصول للمديرين فقط
$requiredRole = 'admin';
require_once 'security.php';

// التحقق من أن المستخدم مدير
requireAdmin();

require_once 'db_connection.php';
require_once 'encryption_functions.php';

// التحقق من وجود معامل التصدير
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    header('Location: system_logs.php');
    exit();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// مصفوفة ربط أسماء الجداول بأسماء الملفات من السايد بار
$table_to_file_mapping = [
    'categories' => 'التصنيفات',
    'items' => 'الأصناف', 
    'purchase_invoices' => 'فواتير الشراء',
    'wholesale_invoices' => 'فواتير البيع بالجملة',
    'monthly_inventory' => 'الجرد',
    'accounts' => 'الحسابات',
    'expenses' => 'المصاريف',
    'shift_closures' => 'تقفيل الورديات',
    'balance_transfers' => 'تحويلات الرصيد',
    'stores' => 'لوحة التحكم',
    'notifications' => 'الإشعارات',
    'system_logs' => 'سجلات النظام',
    'permissions' => 'الصلاحيات',
    'sessions' => 'الجلسات',
    'reports' => 'التقارير',
    'item_reports' => 'التقارير'
];

// مصفوفة ربط أنواع العمليات بالعربية
$action_type_mapping = [
    'INSERT' => 'إضافة',
    'UPDATE' => 'تعديل',
    'DELETE' => 'حذف',
    'LOGIN' => 'تسجيل دخول',
    'LOGOUT' => 'تسجيل خروج',
    'CREATE' => 'إنشاء',
    'MODIFY' => 'تعديل',
    'REMOVE' => 'إزالة',
    'VIEW' => 'عرض',
    'EXPORT' => 'تصدير',
    'IMPORT' => 'استيراد',
    'BACKUP' => 'نسخ احتياطي',
    'RESTORE' => 'استعادة',
    'APPROVE' => 'موافقة',
    'REJECT' => 'رفض',
    'CANCEL' => 'إلغاء',
    'COMPLETE' => 'إكمال',
    'TRANSFER' => 'تحويل',
    'CLOSE' => 'إغلاق',
    'OPEN' => 'فتح',
    // أحرف صغيرة
    'insert' => 'إضافة',
    'update' => 'تعديل',
    'delete' => 'حذف',
    'login' => 'تسجيل دخول',
    'logout' => 'تسجيل خروج',
    'create' => 'إنشاء',
    'modify' => 'تعديل',
    'remove' => 'إزالة',
    'view' => 'عرض',
    'export' => 'تصدير',
    'import' => 'استيراد',
    'backup' => 'نسخ احتياطي',
    'restore' => 'استعادة',
    'approve' => 'موافقة',
    'reject' => 'رفض',
    'cancel' => 'إلغاء',
    'complete' => 'إكمال',
    'transfer' => 'تحويل',
    'close' => 'إغلاق',
    'open' => 'فتح'
];

// قائمة الجداول التي يجب إخفاؤها من العرض
$hidden_tables = ['inventory', 'modules'];

// دالة لتحويل اسم الجدول إلى اسم الملف
function getFileNameFromTable($table_name, $mapping, $hidden_tables = []) {
    // إخفاء الجداول غير المرغوب فيها
    if (in_array($table_name, $hidden_tables)) {
        return null;
    }
    return isset($mapping[$table_name]) ? $mapping[$table_name] : $table_name;
}

// دالة لتحويل نوع العملية إلى العربية
function getActionTypeInArabic($action_type, $mapping) {
    return isset($mapping[$action_type]) ? $mapping[$action_type] : $action_type;
}

// دالة لتحويل التوقيت إلى التنسيق العربي 12 ساعة للتصدير
function formatArabicDateTimeForExport($datetime) {
    if (empty($datetime)) return 'غير محدد';
    
    $timestamp = strtotime($datetime);
    if ($timestamp === false) return 'تاريخ غير صحيح';
    
    // تحويل إلى التوقيت المحلي (إذا لزم الأمر)
    $date = date('Y-m-d', $timestamp);
    $time = date('g:i A', $timestamp);
    
    // تحويل AM/PM إلى العربية
    $time = str_replace(['AM', 'PM'], ['ص', 'م'], $time);
    
    // تحويل الأرقام إلى العربية
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    $arabic_date = str_replace($english_numbers, $arabic_numbers, $date);
    $arabic_time = str_replace($english_numbers, $arabic_numbers, $time);
    
    // تحويل أسماء الأيام إلى العربية
    $day_name = '';
    $day_of_week = date('w', $timestamp);
    $arabic_days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    $day_name = $arabic_days[$day_of_week];
    
    // تحويل أسماء الشهور إلى العربية
    $month_num = date('n', $timestamp);
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $day = str_replace($english_numbers, $arabic_numbers, date('j', $timestamp));
    $year = str_replace($english_numbers, $arabic_numbers, date('Y', $timestamp));
    
    return $day_name . '، ' . $day . ' ' . $arabic_months[$month_num] . ' ' . $year . ' - ' . $arabic_time;
}

// معالجة الفلاتر (نفس المنطق من system_logs.php)
$action_filter = isset($_GET['action_type']) ? $_GET['action_type'] : '';
$table_filter = isset($_GET['table_name']) ? $_GET['table_name'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// بناء الاستعلام
$query = "
    SELECT 
        sl.log_id,
        sl.account_id,
        sl.action_type,
        sl.table_name,
        sl.record_id,
        sl.description,
        sl.created_at,
        a.username,
        a.name
    FROM system_logs sl
    LEFT JOIN accounts a ON sl.account_id = a.account_id
    WHERE 1=1
";

$params = [];
$types = "";

// إضافة الفلاتر
if (!empty($action_filter)) {
    $query .= " AND sl.action_type = ?";
    $params[] = $action_filter;
    $types .= "s";
}

if (!empty($table_filter)) {
    $query .= " AND sl.table_name = ?";
    $params[] = $table_filter;
    $types .= "s";
}

if (!empty($date_from)) {
    $query .= " AND DATE(sl.created_at) >= ?";
    $params[] = $date_from;
    $types .= "s";
}

if (!empty($date_to)) {
    $query .= " AND DATE(sl.created_at) <= ?";
    $params[] = $date_to;
    $types .= "s";
}

if (!empty($search_term)) {
    $query .= " AND (sl.description LIKE ? OR a.username LIKE ? OR a.name LIKE ?)";
    $search_param = "%$search_term%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "sss";
}

$query .= " ORDER BY sl.created_at DESC";

// تنفيذ الاستعلام
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// إعداد ملف CSV
$filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.csv';

// إعداد headers للتحميل
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// إنشاء ملف CSV
$output = fopen('php://output', 'w');

// إضافة BOM لدعم UTF-8 في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة رؤوس الأعمدة
$headers = [
    'رقم السجل',
    'رقم الحساب',
    'اسم المستخدم',
    'الاسم الكامل',
    'نوع العملية',
    'القسم',
    'رقم السجل المتأثر',
    'وصف العملية',
    'تاريخ ووقت العملية'
];

fputcsv($output, $headers);

// كتابة البيانات
while ($row = $result->fetch_assoc()) {
    $table_display_name = getFileNameFromTable($row['table_name'] ?? 'غير محدد', $table_to_file_mapping, $hidden_tables);
    
    // تخطي السجلات التي تحتوي على جداول مخفية
    if ($table_display_name === null) {
        continue;
    }
    
    $data = [
        $row['log_id'],
        $row['account_id'] ?? 'غير محدد',
        $row['username'] ?? 'غير محدد',
        $row['name'] ?? 'غير محدد',
        getActionTypeInArabic($row['action_type'] ?? 'غير محدد', $action_type_mapping),
        $table_display_name,
        $row['record_id'] ?? 'غير محدد',
        $row['description'] ?? 'لا يوجد وصف',
        formatArabicDateTimeForExport($row['created_at'])
    ];
    
    fputcsv($output, $data);
}

// إضافة معلومات إضافية في نهاية الملف
fputcsv($output, []); // سطر فارغ
fputcsv($output, ['معلومات التصدير:']);
fputcsv($output, ['تاريخ التصدير:', formatArabicDateTimeForExport(date('Y-m-d H:i:s'))]);
fputcsv($output, ['المستخدم:', $_SESSION['username'] ?? 'غير محدد']);
fputcsv($output, ['عدد السجلات:', $result->num_rows]);

if (!empty($action_filter)) {
    fputcsv($output, ['فلتر نوع العملية:', getActionTypeInArabic($action_filter, $action_type_mapping)]);
}
if (!empty($table_filter)) {
    $filter_display_name = getFileNameFromTable($table_filter, $table_to_file_mapping, $hidden_tables);
    if ($filter_display_name !== null) {
        fputcsv($output, ['فلتر القسم:', $filter_display_name]);
    }
}
if (!empty($date_from)) {
    fputcsv($output, ['من تاريخ:', $date_from]);
}
if (!empty($date_to)) {
    fputcsv($output, ['إلى تاريخ:', $date_to]);
}
if (!empty($search_term)) {
    fputcsv($output, ['مصطلح البحث:', $search_term]);
}

fclose($output);

$stmt->close();
$conn->close();
?>