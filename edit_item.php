<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول
checkPagePermission('items', 'access');

// فحص صلاحية التعديل
if (!hasPermission('items', 'edit_item')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل الأصناف']);
    exit();
}

$key = getenv('ENCRYPTION_KEY');

$response = [];

/**
 * Update multiple items with the same data
 */
function updateMultipleItems($main_item_id, $selected_items, $item_name, $cost, $price, $barcode, $pieces_per_box, $conn, $key) {
    global $response;
    
    $conn->begin_transaction();
    
    try {
        $updated_items = [];
        $price_change_notifications = [];
        
        // جمع معرفات جميع الأصناف للتحديث
        $all_item_ids = [$main_item_id];
        foreach ($selected_items as $encrypted_item_id) {
            $item_id = decrypt($encrypted_item_id, $key);
            if ($item_id && $item_id != $main_item_id) {
                $all_item_ids[] = $item_id;
            }
        }
        
        // فحص الأسعار الحالية قبل التحديث
        $current_items_data = [];
        foreach ($all_item_ids as $item_id) {
            if ($item_id) {
                $check_sql = "SELECT name, price, store_id FROM items WHERE item_id = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("i", $item_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                if ($check_result->num_rows > 0) {
                    $item_data = $check_result->fetch_assoc();
                    $current_items_data[$item_id] = [
                        'name' => $item_data['name'],
                        'store_id' => $item_data['store_id'],
                        'old_price' => $item_data['price']
                    ];
                }
                $check_stmt->close();
            }
        }
        
        // تحديث الصنف الرئيسي
        $update_sql = "UPDATE items SET name = ?, cost = ?, price = ?, barcode = ?, pieces_per_box = ? WHERE item_id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("sddsii", $item_name, $cost, $price, $barcode, $pieces_per_box, $main_item_id);
        
        if (!$stmt->execute()) {
            throw new Exception('فشل في تحديث الصنف الرئيسي');
        }
        $stmt->close();
        $updated_items[] = $main_item_id;
        
        // فحص تغيير السعر للصنف الرئيسي
        if (isset($current_items_data[$main_item_id]) && $current_items_data[$main_item_id]['old_price'] != $price) {
            $price_change_notifications[] = [
                'item_id' => $main_item_id,
                'store_id' => $current_items_data[$main_item_id]['store_id'],
                'item_name' => $item_name,
                'old_price' => $current_items_data[$main_item_id]['old_price'],
                'new_price' => $price
            ];
        }
        
        // تحديث الأصناف المختارة في الفروع الأخرى
        foreach ($selected_items as $encrypted_item_id) {
            $item_id = decrypt($encrypted_item_id, $key);
            if ($item_id && $item_id != $main_item_id) {
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("sddsii", $item_name, $cost, $price, $barcode, $pieces_per_box, $item_id);
                
                if (!$stmt->execute()) {
                    throw new Exception("فشل في تحديث الصنف رقم $item_id");
                }
                $stmt->close();
                $updated_items[] = $item_id;
                
                // فحص تغيير السعر
                if (isset($current_items_data[$item_id]) && $current_items_data[$item_id]['old_price'] != $price) {
                    $price_change_notifications[] = [
                        'item_id' => $item_id,
                        'store_id' => $current_items_data[$item_id]['store_id'],
                        'item_name' => $item_name,
                        'old_price' => $current_items_data[$item_id]['old_price'],
                        'new_price' => $price
                    ];
                }
            }
        }
        
        // تسجيل العملية في سجل النظام
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        $account_id = decrypt($_SESSION['account_id'], $key);
        if ($account_id) {
            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                        VALUES (?, 'update_multiple', 'items', ?, ?)";
            $description = "تم تحديث عدة أصناف بنفس الباركود: " . implode(', ', $updated_items);
            $log_stmt = $conn->prepare($log_sql);
            $log_stmt->bind_param("iis", $account_id, $main_item_id, $description);
            $log_stmt->execute();
            $log_stmt->close();
        }
        
        // معالجة إشعارات تغيير الأسعار
        $notifications_sent = [];
        if (!empty($price_change_notifications) && $account_id) {
            foreach ($price_change_notifications as $notification_data) {
                // حفظ الإشعار في قاعدة البيانات
                $notification_message = "تم تغيير سعر بيع الصنف '{$notification_data['item_name']}' من {$notification_data['old_price']} إلى {$notification_data['new_price']}";
                $stmt_notification = $conn->prepare("INSERT INTO notifications (message, store_id, item_id, created_by) VALUES (?, ?, ?, ?)");
                $stmt_notification->bind_param("siii", $notification_message, $notification_data['store_id'], $notification_data['item_id'], $account_id);
                $stmt_notification->execute();
                $notification_id = $stmt_notification->insert_id;
                $stmt_notification->close();
                
                // إرسال إشعار Firebase
                $firebase_result = sendPriceChangeNotification(
                    $notification_id, 
                    $notification_data['store_id'], 
                    $notification_data['item_name'], 
                    $notification_data['old_price'], 
                    $notification_data['new_price']
                );
                
                $notifications_sent[] = [
                    'item_id' => $notification_data['item_id'],
                    'store_id' => $notification_data['store_id'],
                    'firebase_result' => $firebase_result
                ];
            }
        }
        
        $conn->commit();
        
        $result = [
            'success' => true, 
            'message' => 'تم تحديث الأصناف بنجاح',
            'updated_count' => count($updated_items)
        ];
        
        // إضافة معلومات الإشعارات إذا تم إرسالها
        if (!empty($notifications_sent)) {
            $result['price_change_notifications'] = count($notifications_sent);
            $result['notifications_details'] = $notifications_sent;
        }
        
        return $result;
        
    } catch (Exception $e) {
        $conn->rollback();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Send Firebase notification for price change
 */
function sendPriceChangeNotification($notification_id, $store_id, $item_name, $old_price, $new_price) {
    global $conn;

    try {
        // Get FCM tokens for the store
        $tokens_query = "SELECT token FROM fcm_tokens WHERE store_id = ?";
        $tokens_stmt = $conn->prepare($tokens_query);
        $tokens_stmt->bind_param("i", $store_id);
        $tokens_stmt->execute();
        $tokens_result = $tokens_stmt->get_result();

        $tokens = [];
        while ($row = $tokens_result->fetch_assoc()) {
            $tokens[] = $row['token'];
        }
        $tokens_stmt->close();

        if (empty($tokens)) {
            return ['success' => false, 'message' => 'No FCM tokens found'];
        }

        // Path to service account key file
        $serviceAccountPath = __DIR__ . '/firebase-adminsdk.json';

        if (!file_exists($serviceAccountPath)) {
            return ['success' => false, 'message' => 'Service account key file not found'];
        }

        // Include the OAuth 2.0 access token helper
        require_once 'get_access_token.php';

        // Get OAuth 2.0 access token
        $accessTokenInfo = getAccessToken($serviceAccountPath);
        $accessToken = $accessTokenInfo['token'];

        // Get project ID from service account file
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

        if (!isset($serviceAccount['project_id'])) {
            return ['success' => false, 'message' => 'Invalid service account file'];
        }

        // Prepare notification title and message
        $title = "تغيير سعر صنف";
        $body = "تم تغيير سعر '$item_name' من $old_price إلى $new_price";

        // Prepare FCM message
        $fcmMessage = [
            'message' => [
                'token' => $tokens[0], // Will be updated for each token
                'notification' => [
                    'title' => $title,
                    'body' => $body
                ],
                'webpush' => [
                    'headers' => [
                        'Urgency' => 'high'
                    ],
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                        'icon' => 'https://' . $_SERVER['HTTP_HOST'] . '/uploads/img/logo2.png',
                        'badge' => 'https://' . $_SERVER['HTTP_HOST'] . '/uploads/img/logo2.png',
                        'click_action' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php',
                        'tag' => 'price-change-notification',
                        'requireInteraction' => false,
                        'silent' => false,
                        'vibrate' => [200, 100, 200]
                    ],
                    'fcm_options' => [
                        'link' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php'
                    ]
                ],
                'data' => [
                    'notification_id' => (string)$notification_id,
                    'type' => 'price_change',
                    'item_name' => $item_name,
                    'old_price' => (string)$old_price,
                    'new_price' => (string)$new_price
                ]
            ]
        ];

        $url = 'https://fcm.googleapis.com/v1/projects/' . $serviceAccount['project_id'] . '/messages:send';
        $headers = [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json'
        ];

        $successCount = 0;
        $failureCount = 0;

        // Send notification to each token
        foreach ($tokens as $token) {
            // Update token in message
            $fcmMessage['message']['token'] = $token;

            // Send request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmMessage));

            $result = curl_exec($ch);

            if ($result !== false) {
                $response = json_decode($result, true);
                if (isset($response['name'])) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            } else {
                $failureCount++;
            }

            curl_close($ch);
        }

        return [
            'success' => $successCount > 0,
            'message' => "Notification sent to $successCount devices" . ($failureCount > 0 ? " (Failed: $failureCount)" : ''),
            'sent_count' => $successCount,
            'failed_count' => $failureCount
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// التحقق من نوع الطلب - تحديث متعدد أم تحديث عادي
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    
    // التحديث المتعدد
    if (isset($_POST['main_item_id']) && isset($_POST['selected_items'])) {
        $main_item_id = decrypt($_POST['main_item_id'], $key);
        $selected_items = json_decode($_POST['selected_items'], true);
        $item_name = $_POST['item_name'];
        $cost = $_POST['cost'];
        $price = $_POST['price'];
        $barcode = $_POST['barcode'];
        $pieces_per_box = $_POST['pieces_per_box'] ?? 0;
        
        if (!$main_item_id || !is_array($selected_items)) {
            $response['success'] = false;
            $response['message'] = 'بيانات غير صحيحة';
        } else {
            $response = updateMultipleItems($main_item_id, $selected_items, $item_name, $cost, $price, $barcode, $pieces_per_box, $conn, $key);
        }
        
        echo json_encode($response);
        $conn->close();
        exit();
    }
    
    // التحديث العادي
    if (isset($_POST['edit_item_id'])) {
        $edit_item_id = decrypt($_POST['edit_item_id'], $key);
        $barcode = $_POST['barcode'];
        $pieces_per_box = 0; // Default value

    // Fetch the current item details from the database
    $stmt_select = $conn->prepare("SELECT name, cost, price, quantity, type, store_id, barcode FROM items WHERE item_id = ?");
    $stmt_select->bind_param("i", $edit_item_id);
    $stmt_select->execute();
    $stmt_select->bind_result($current_name, $current_cost, $current_price, $current_quantity, $item_type, $store_id, $current_barcode);
    $stmt_select->fetch();
    $stmt_select->close();

    // Check if the barcode has changed and if it's unique within the same store
    if ($barcode !== $current_barcode) {
        $barcode_check_sql = "SELECT COUNT(*) AS count FROM items WHERE barcode = ? AND store_id = ? AND item_id != ?";
        $barcode_check_stmt = $conn->prepare($barcode_check_sql);
        $barcode_check_stmt->bind_param("sii", $barcode, $store_id, $edit_item_id);
        $barcode_check_stmt->execute();
        $barcode_check_result = $barcode_check_stmt->get_result();
        $barcode_count = $barcode_check_result->fetch_assoc()['count'];
        $barcode_check_stmt->close();

        if ($barcode_count > 0) {
            $response['success'] = false;
            $response['message'] = 'لا يمكن تعديل الصنف: الباركود المدخل مستخدم بالفعل في صنف آخر بنفس الفرع.';
            echo json_encode($response);
            exit();
        }
    }

    if ($item_type == 'piece' || $item_type == 'other') {
        $item_name = $_POST['item_name_piece'];
        $cost = $_POST['cost_piece'];
        $price = $_POST['price1_piece'];
        $quantity = $_POST['quantity_piece'];
    } elseif ($item_type == 'box') {
        $item_name = $_POST['item_name_box'];
        $cost = $_POST['cost_box'];
        $price = $_POST['price1_box'];
        $quantity = $_POST['quantity_box'];
        $pieces_per_box = $_POST['pieces_per_box'];
    } elseif ($item_type == 'fridge') {
        $item_name = $_POST['item_name_fridge'];
        $cost = $_POST['cost_fridge'];
        $price = $_POST['price1_fridge'];
        $quantity = $_POST['quantity_fridge'];
    }

    // Check if the cost has changed
    if ($cost != $current_cost) {
        $stmt_cost = $conn->prepare("INSERT INTO itempricetransaction (item_id, old_price, new_price, price_type, quantity) VALUES (?, ?, ?, 'cost', ?)");
        $stmt_cost->bind_param("iddd", $edit_item_id, $current_cost, $cost, $current_quantity);
        $stmt_cost->execute();
        $stmt_cost->close();
    }

    // Check if the price has changed
    $price_changed = false;
    if ($price != $current_price) {
        $stmt_price = $conn->prepare("INSERT INTO itempricetransaction (item_id, old_price, new_price, price_type, quantity) VALUES (?, ?, ?, 'price', ?)");
        $stmt_price->bind_param("iddd", $edit_item_id, $current_price, $price, $current_quantity);
        $stmt_price->execute();
        $stmt_price->close();
        $price_changed = true;
    }

    // Handle image deletions
    if (isset($_POST['images_to_delete']) && !empty($_POST['images_to_delete'])) {
        $images_to_delete = json_decode($_POST['images_to_delete'], true);
        
        if (is_array($images_to_delete) && !empty($images_to_delete)) {
            foreach ($images_to_delete as $img_id) {
                // Get image path before deleting from database
                $get_path_sql = "SELECT img_path FROM item_images WHERE img_id = ? AND item_id = ?";
                $get_path_stmt = $conn->prepare($get_path_sql);
                $get_path_stmt->bind_param("ii", $img_id, $edit_item_id);
                $get_path_stmt->execute();
                $get_path_result = $get_path_stmt->get_result();
                
                if ($get_path_result->num_rows > 0) {
                    $image_data = $get_path_result->fetch_assoc();
                    $image_path = $image_data['img_path'];
                    
                    // Delete from database
                    $delete_sql = "DELETE FROM item_images WHERE img_id = ? AND item_id = ?";
                    $delete_stmt = $conn->prepare($delete_sql);
                    $delete_stmt->bind_param("ii", $img_id, $edit_item_id);
                    $delete_stmt->execute();
                    $delete_stmt->close();
                    
                    // Delete physical file
                    if (file_exists($image_path)) {
                        unlink($image_path);
                    }
                }
                
                $get_path_stmt->close();
            }
        }
    }

    // Handle image uploads
    if (isset($_FILES['item_images']) && !empty($_FILES['item_images']['name'][0])) {
        $upload_dir = 'uploads/items/';
        
        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $uploaded_images = [];
        $total_files = count($_FILES['item_images']['name']);

        for ($i = 0; $i < $total_files; $i++) {
            if ($_FILES['item_images']['error'][$i] == UPLOAD_ERR_OK) {
                $file_tmp = $_FILES['item_images']['tmp_name'][$i];
                $file_name = $_FILES['item_images']['name'][$i];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                
                // Check if file is an image
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                if (in_array($file_ext, $allowed_extensions)) {
                    // Generate unique filename and check for duplicates
                    $counter = 0;
                    do {
                        $new_filename = uniqid() . ($counter > 0 ? '_' . $counter : '') . '.' . $file_ext;
                        $file_path = $upload_dir . $new_filename;
                        $counter++;
                        
                        // Check if file path already exists in database
                        $check_path_sql = "SELECT COUNT(*) as count FROM item_images WHERE img_path = ?";
                        $check_path_stmt = $conn->prepare($check_path_sql);
                        $check_path_stmt->bind_param("s", $file_path);
                        $check_path_stmt->execute();
                        $check_result = $check_path_stmt->get_result();
                        $path_exists_in_db = $check_result->fetch_assoc()['count'] > 0;
                        $check_path_stmt->close();
                        
                    } while (file_exists($file_path) || $path_exists_in_db);
                    
                    if (move_uploaded_file($file_tmp, $file_path)) {
                        // Save image path to database
                        $img_sql = "INSERT INTO item_images (item_id, img_path) VALUES (?, ?)";
                        $img_stmt = $conn->prepare($img_sql);
                        $img_stmt->bind_param("is", $edit_item_id, $file_path);
                        $img_stmt->execute();
                        $img_stmt->close();
                        
                        $uploaded_images[] = $file_path;
                    }
                }
            }
        }
    }

    // Update the item details
    $stmt_update = $conn->prepare("UPDATE items SET name = ?, cost = ?, price = ?, quantity = ?, barcode = ?, pieces_per_box = ? WHERE item_id = ?");
    $stmt_update->bind_param("sdddsdi", $item_name, $cost, $price, $quantity, $barcode, $pieces_per_box, $edit_item_id);
    if ($stmt_update->execute()) {
        $response['success'] = true;
        $response['message'] = 'تم تعديل الصنف بنجاح.';

        // Log the item edit action
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Prepare a description of what was changed
        $changes = [];
        if ($item_name !== $current_name) $changes[] = "اسم الصنف من $current_name إلى $item_name";
        if ($cost != $current_cost) $changes[] = "التكلفة من $current_cost إلى $cost";
        if ($price != $current_price) $changes[] = "سعر البيع من $current_price إلى $price";
        if ($barcode !== $current_barcode) $changes[] = "الباركود من $current_barcode إلى $barcode";
        if ($quantity != $current_quantity) {
            $changes[] = "الكمية من $current_quantity إلى $quantity";

            // Calculate the quantity difference
            $quantity_difference = $quantity - $current_quantity;

            // Log the quantity change in itemtransactions
            $stmt_transaction = $conn->prepare("INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'edit', 0, ?)");
            $stmt_transaction->bind_param("id", $edit_item_id, $quantity_difference);
            $stmt_transaction->execute();
            $stmt_transaction->close();
        }

        $description = "تم تعديل الصنف رقم $edit_item_id: " . implode(", ", $changes);

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description)
                    VALUES (?, 'update', 'items', ?, ?)";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $edit_item_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        // Store a notification in the notifications table if the price has changed
        if ($price_changed) {
            $notification_message = "تم تغيير سعر بيع الصنف '$item_name' من $current_price إلى $price";
            $stmt_notification = $conn->prepare("INSERT INTO notifications (message, store_id, item_id, created_by) VALUES (?, ?, ?, ?)");
            $stmt_notification->bind_param("siii", $notification_message, $store_id, $edit_item_id, $account_id);
            $stmt_notification->execute();
            $notification_id = $stmt_notification->insert_id;
            $stmt_notification->close();

            // Send immediate Firebase notification
            $response['notification_sent'] = sendPriceChangeNotification($notification_id, $store_id, $item_name, $current_price, $price);
        }
    } else {
        $response['success'] = false;
        $response['message'] = 'حدث خطأ أثناء تعديل الصنف: ' . $stmt_update->error;
    }
    $stmt_update->close();
    
    } else {
        $response['success'] = false;
        $response['message'] = 'بيانات غير صحيحة للتحديث العادي.';
    }
} else {
    $response['success'] = false;
    $response['message'] = 'طلب غير صحيح.';
}

echo json_encode($response);
$conn->close();
?>
