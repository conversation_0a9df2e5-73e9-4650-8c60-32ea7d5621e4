<?php
/**
 * ملف التحقق من الصلاحيات المتقدم
 * يحل محل auth_check.php التقليدي ويوفر مرونة أكبر في إدارة الصلاحيات
 */

session_start();
include 'db_connection.php';
include 'encryption_functions.php';
include 'permission_system.php';

// التحقق من وجود جلسة نشطة
if (!isset($_SESSION['account_id'])) {
    header('Location: index.php');
    exit();
}

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_SESSION['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if (!$account_id) {
    header('Location: index.php');
    exit();
}

// التحقق من حالة الحساب
$sql = "SELECT status, role FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$stmt->bind_result($status, $role);
$stmt->fetch();
$stmt->close();

if ($status !== 'active') {
    session_unset();
    session_destroy();
    header('Location: index.php?error=account_inactive');
    exit();
}

// إنشاء مثيل من نظام الصلاحيات
$permission_system = new PermissionSystem($conn);

/**
 * دالة للتحقق من صلاحية معينة للصفحة الحالية
 */
function checkPagePermission($required_permission, $redirect_url = 'index.php') {
    global $permission_system;
    
    if (!$permission_system->currentUserHasPermission($required_permission)) {
        header("Location: $redirect_url?error=no_permission");
        exit();
    }
}

/**
 * دالة للتحقق من صلاحيات متعددة (يجب أن يملك المستخدم إحداها على الأقل)
 */
function checkAnyPermission($permissions, $redirect_url = 'index.php') {
    global $permission_system;
    
    foreach ($permissions as $permission) {
        if ($permission_system->currentUserHasPermission($permission)) {
            return true;
        }
    }
    
    header("Location: $redirect_url?error=no_permission");
    exit();
}

/**
 * دالة للتحقق من جميع الصلاحيات المطلوبة
 */
function checkAllPermissions($permissions, $redirect_url = 'index.php') {
    global $permission_system;
    
    foreach ($permissions as $permission) {
        if (!$permission_system->currentUserHasPermission($permission)) {
            header("Location: $redirect_url?error=no_permission");
            exit();
        }
    }
    
    return true;
}

/**
 * دالة للحصول على معلومات المستخدم الحالي
 */
function getCurrentUserInfo() {
    global $conn, $account_id;
    
    $sql = "SELECT account_id, username, name, role, store_id FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * دالة للتحقق من إمكانية الوصول لمتجر معين
 */
function canAccessStore($store_id) {
    global $permission_system, $role;
    
    // المدير يمكنه الوصول لجميع المتاجر
    if ($role === 'admin') {
        return true;
    }
    
    // التحقق من أن المستخدم ينتمي لهذا المتجر
    $user_info = getCurrentUserInfo();
    return $user_info['store_id'] == $store_id;
}

/**
 * دالة لعرض رسالة خطأ عدم وجود صلاحية
 */
function showPermissionError($permission_name = '') {
    $message = "عذراً، ليس لديك الصلاحية للوصول إلى هذه الصفحة.";
    if ($permission_name) {
        $message .= " الصلاحية المطلوبة: " . $permission_name;
    }
    
    echo "<div class='error-message' style='
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        margin: 20px;
        text-align: center;
        font-family: Arial, sans-serif;
    '>
        <h3>🚫 وصول مرفوض</h3>
        <p>$message</p>
        <a href='index.php' style='
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-top: 10px;
        '>العودة للرئيسية</a>
    </div>";
    exit();
}

// تعيين متغيرات عامة للاستخدام في الصفحات
$current_user = getCurrentUserInfo();
$current_user_id = $account_id;
$current_user_role = $role;

// دوال مساعدة سريعة
function isAdmin() {
    global $current_user_role;
    return $current_user_role === 'admin';
}

function isPurchaser() {
    global $current_user_role;
    return $current_user_role === 'purchaser';
}

function isUser() {
    global $current_user_role;
    return $current_user_role === 'user';
}

function isDealer() {
    global $current_user_role;
    return $current_user_role === 'dealer';
}

/**
 * دالة لتسجيل محاولات الوصول غير المصرح بها
 */
function logUnauthorizedAccess($page, $required_permission) {
    global $conn, $current_user_id;
    
    $sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
            VALUES (?, 'unauthorized_access', 'permissions', ?)";
    
    $description = "محاولة وصول غير مصرح بها للصفحة: $page، الصلاحية المطلوبة: $required_permission";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("is", $current_user_id, $description);
    $stmt->execute();
    $stmt->close();
}
?>
