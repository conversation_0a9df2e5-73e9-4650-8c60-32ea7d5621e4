<?php
include 'db_connection.php';

$inventory_id = isset($_GET['inventory_id']) ? intval($_GET['inventory_id']) : 0;

if ($inventory_id <= 0) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT 
            bt.account_id, 
            COALESCE(a.name, bt.account_name) AS account_name, 
            bt.provider, 
            bt.cost, 
            bt.sale_price, 
            bt.value, 
            bt.created_at 
        FROM inventory_balance_transfers bt
        LEFT JOIN accounts a ON bt.account_id = a.account_id
        WHERE bt.inventory_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$result = $stmt->get_result();

$transfers = [];
while ($row = $result->fetch_assoc()) {
    $transfers[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($transfers);
?>
