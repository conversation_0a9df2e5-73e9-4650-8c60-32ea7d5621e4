<?php
/**
 * حذف تحويل رصيد - مع فحص الصلاحيات
 * الصلاحية المطلوبة: delete_transfer في وحدة balance_transfers
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

// فحص صلاحية حذف تحويل رصيد
if (!hasPermission('balance_transfers', 'delete_transfer')) {
    echo json_encode(['success' => false, 'error' => 'ليس لديك صلاحية لحذف تحويلات الرصيد']);
    exit();
}

// Retrieve and validate input
$data = json_decode(file_get_contents('php://input'), true);
$balance_id = isset($data['balance_id']) ? intval($data['balance_id']) : null;

if (!$balance_id) {
    echo json_encode(['success' => false, 'error' => 'معرف التحويل غير صالح.']);
    exit();
}

// Fetch the balance transfer details before deletion
$stmt = $conn->prepare("SELECT provider, value FROM balance_transfers WHERE balance_id = ?");
$stmt->bind_param("i", $balance_id);
$stmt->execute();
$stmt->bind_result($provider, $value);
$stmt->fetch();
$stmt->close();

// Delete the balance transfer
$stmt = $conn->prepare("DELETE FROM balance_transfers WHERE balance_id = ?");
$stmt->bind_param("i", $balance_id);
$success = $stmt->execute();
$stmt->close();

if ($success) {
    // Log the balance transfer deletion action
    $key = getenv('ENCRYPTION_KEY');
    $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'delete', 'balance_transfers', ?, ?)";
    $description = "تم حذف تحويل رصيد من المزود $provider بقيمة $value";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $balance_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم حذف عملية التحويل بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في حذف عملية التحويل.']);
}

$conn->close();
?>
